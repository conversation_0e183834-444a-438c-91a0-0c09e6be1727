import { z } from 'zod';
import { Pagination } from '../../types';
import {
  AuditLogSchema,
  AuditLogsResponse,
  LockUserResponse,
  LoginAttemptSchema,
  LoginAttemptsResponse,
  SecurityEventSchema,
  SecurityEventsResponse,
  UnlockUserResponse,
} from './response-transformer';

// Audit Logs Types
export interface AuditLogsFilter {
  action?: string;
  user_id?: string;
  admin_id?: string;
  date_from?: string;
  date_to?: string;
}

export interface GetAuditLogsQueryProps {
  pagination?: Pagination;
  filters?: AuditLogsFilter;
}

// Security Types
export interface LockUserRequest {
  user_id: string;
  reason: string;
}

export interface UnlockUserRequest {
  user_id: string;
  reason: string;
}

// Zod Schemas for validation
export const auditLogsQuerySchema = z.object({
  page: z.coerce.number().default(0),
  limit: z.coerce.number().max(100).default(20),
  action: z.string().optional(),
  user_id: z.string().uuid().optional(),
  admin_id: z.string().uuid().optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
});

export const lockUserRequestSchema = z.object({
  user_id: z.string().uuid(),
  reason: z.string(),
});

export const unlockUserRequestSchema = z.object({
  user_id: z.string().uuid(),
  reason: z.string(),
});

// Type exports
export type AuditLogsQuery = z.infer<typeof auditLogsQuerySchema>;
export type LockUserRequestType = z.infer<typeof lockUserRequestSchema>;
export type UnlockUserRequestType = z.infer<typeof unlockUserRequestSchema>;

// Type exports
export type AuditLog = z.infer<typeof AuditLogSchema>;
export type LoginAttempt = z.infer<typeof LoginAttemptSchema>;
export type SecurityEvent = z.infer<typeof SecurityEventSchema>;
export type AuditLogsResponseType = z.infer<typeof AuditLogsResponse>;
export type LoginAttemptsResponseType = z.infer<typeof LoginAttemptsResponse>;
export type SecurityEventsResponseType = z.infer<typeof SecurityEventsResponse>;
export type LockUserResponseType = z.infer<typeof LockUserResponse>;
export type UnlockUserResponseType = z.infer<typeof UnlockUserResponse>;
