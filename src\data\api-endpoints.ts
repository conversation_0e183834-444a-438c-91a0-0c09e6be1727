/* eslint-disable sonarjs/no-duplicate-string */
export const API_ENDPOINT = {
  // Admin Auth endpoints
  auth: {
    login: '/login',
    register: '/register',
    logout: '/logout',
    forgotPassword: '/forgot-password',
    resetPassword: '/reset-password',
    verifyOtp: '/verify-otp',
    changePassword: '/change-password',
  },

  // Admin Profile endpoints
  profile: '/profile',

  // Admin Management endpoints
  admins: {
    list: '/all',
    status: '/status',
  },

  // User Management endpoints
  users: {
    list: (id?: string | undefined) => (id ? `/users/${id}` : '/users'),
    byId: (id: string) => `/users?id=${id}`,
    activationApproval: (id: string) => `/users/${id}/activation-approval`,
  },

  // Shipment Management endpoints
  shipments: {
    list: (id?: string | undefined) => (id ? `/shipments/${id}` : '/shipments'),
    byId: (id: string) => `/shipments?id=${id}`,
    expiredStats: '/shipments/expired-stats',
  },

  // settings Management endpoints
  settings: {
    list: () => 'settings',
    reset: () => 'settings/reset',
  },

  // Notifications endpoints
  notifications: {
    list: '/notifications',
    unreadCount: '/notifications/unread-count',
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/mark-all-read',
    preferences: '/notifications/preferences',
    filterOptions: '/notifications/filter-options',
    // Broadcast message endpoints
    broadcast: {
      send: '/notifications/broadcast/send',
      schedule: '/notifications/broadcast/schedule',
      preview: '/notifications/broadcast/preview',
      recipients: '/notifications/broadcast/recipients',
      validateRecipients: '/notifications/broadcast/validate-recipients',
    },
    // Template management endpoints
    templates: {
      list: '/notifications/templates',
      byId: (id: string) => `/notifications/templates/${id}`,
      create: '/notifications/templates',
      update: (id: string) => `/notifications/templates/${id}`,
      delete: (id: string) => `/notifications/templates/${id}`,
      duplicate: (id: string) => `/notifications/templates/${id}/duplicate`,
      usage: (id: string) => `/notifications/templates/${id}/usage`,
      preview: (id: string) => `/notifications/templates/${id}/preview`,
    },
  },

  // Admin Notifications endpoints
  adminNotifications: {
    my: '/notifications/my',
    unreadCount: '/notifications/unread-count',
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/mark-all-read',
    delete: (id: string) => `/notifications/${id}`,
  },

  // Dashboard endpoints
  dashboard: '/dashboard',

  reports: {
    users: () => '/reports/users',
    shipments: () => '/reports/shipments',
    system: () => '/reports/system',
  },
  export: {
    users: () => '/export/users',
    shipments: () => '/export/shipments',
    download: (id: string) => `/export/${id}/download`,
  },

  // Audit & Security endpoints
  audit: {
    logs: '/audit-logs',
  },
  security: {
    loginAttempts: '/security/login-attempts',
    events: '/security/events',
    lockUser: '/security/lock-user',
    unlockUser: '/security/unlock-user',
  },

};
