# Admin API: Notification Management Endpoints

This document provides a detailed overview of the notification management endpoints available in the admin API. It includes information on requests, responses, and data schemas, based on the implementation in `AdminController.ts` and `adminRoutes.ts`.

## Common Schemas

### `ApiResponseSchema<T>`

A generic wrapper for successful API responses.

-   `success`: `boolean` - Always `true` for successful responses.
-   `message`: `string` - A descriptive message about the result.
-   `data`: `T` - The payload, which varies by endpoint.

### `ApiErrorResponseSchema`

A standard format for error responses.

-   `success`: `boolean` - Always `false` for error responses.
-   `error`: `string` - A short error code or type (e.g., "Not Found").
-   `message`: `string` - A user-friendly error message.
-   `details`: `object` (optional) - Additional details about the error, often used for validation failures.

### `PaginationSchema`

Standard pagination object for list responses.

-   `page`: `number` - The current page number.
-   `limit`: `number` - The number of items per page.
-   `total`: `number` - The total number of items.
-   `totalPages`: `number` - The total number of pages.
-   `hasNext`: `boolean` - Indicates if there is a next page.
-   `hasPrev`: `boolean` - Indicates if there is a previous page.

---

## 1. Get All Notifications

-   **Endpoint:** `GET /api/admin/notifications`
-   **Description:** Retrieves a paginated list of all user notifications, with filtering capabilities. This is used to monitor all notifications sent across the system.
-   **Middleware:** `authMiddleware`, `adminMiddleware`

### Request

#### Query Parameters

-   `page`: `number` (optional) - The page number for pagination. Default: `1`.
-   `limit`: `number` (optional) - The number of notifications per page. Default: `20`.
-   `user_id`: `string` (optional) - Filter notifications by a specific user's UUID.
-   `type`: `string` (optional) - Filter by notification type (e.g., `SYSTEM`, `SHIPMENT_STATUS`).
-   `status`: `string` (optional) - Filter by read status (`read` or `unread`).
-   `priority`: `string` (optional) - Filter by priority level (`LOW`, `NORMAL`, `HIGH`).

### Response

#### Success (200 OK)

```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": "uuid-string",
        "user_id": "uuid-string",
        "notification_type": "SYSTEM",
        "title": "Example Notification",
        "message": "This is a test notification.",
        "read": false,
        "priority": "NORMAL",
        "created_at": "datetime-string",
        "updated_at": "datetime-string",
        "user": {
          "id": "uuid-string",
          "name": "John Doe",
          "email": "<EMAIL>",
          "user_type": "CUSTOMER"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

## 2. Broadcast Notification

-   **Endpoint:** `POST /api/admin/notifications/broadcast`
-   **Description:** Sends a notification to multiple users at once, targeting them by their user type.
-   **Middleware:** `authMiddleware`, `adminMiddleware`

### Request

#### Body

```json
{
  "title": "System Maintenance Announcement",
  "message": "The system will be down for maintenance tonight from 2 AM to 3 AM.",
  "user_types": ["CUSTOMER", "CAR_OPERATOR"],
  "priority": "HIGH"
}
```

-   `title`: `string` (required) - The title of the notification.
-   `message`: `string` (required) - The main content of the notification.
-   `user_types`: `string[]` (optional) - An array of user types to target. If omitted, it broadcasts to all users.
-   `priority`: `string` (optional) - The priority of the notification (`LOW`, `NORMAL`, `HIGH`). Default: `NORMAL`.

### Response

#### Success (201 Created)

```json
{
  "success": true,
  "message": "Broadcast notification sent to 50 users",
  "data": {
    "notification_count": 50,
    "recipient_count": 50
  }
}
```

---

## 3. Get Notification Templates

-   **Endpoint:** `GET /api/admin/notifications/templates`
-   **Description:** Retrieves a list of pre-defined notification templates. (Note: This is a placeholder implementation and does not use a database model).
-   **Middleware:** `authMiddleware`, `adminMiddleware`

### Request

No request body or query parameters.

### Response

#### Success (200 OK)

```json
{
  "success": true,
  "message": "Notification templates retrieved successfully",
  "data": [
    {
      "id": "1",
      "name": "Welcome Template",
      "title": "Welcome to NAQALAT",
      "message": "Thank you for joining our platform",
      "type": "WELCOME"
    },
    {
      "id": "2",
      "name": "Shipment Created",
      "title": "Shipment Created Successfully",
      "message": "Your shipment has been created and is awaiting pickup",
      "type": "SHIPMENT_CREATED"
    }
  ]
}
```

---

## 4. Create Notification Template

-   **Endpoint:** `POST /api/admin/notifications/templates`
-   **Description:** Creates a new notification template. (Note: This is a placeholder implementation).
-   **Middleware:** `authMiddleware`, `adminMiddleware`

### Request

#### Body

```json
{
  "name": "Password Reset Confirmation",
  "title": "Your Password Has Been Reset",
  "message": "This is a confirmation that the password for your account has just been changed.",
  "type": "PASSWORD_RESET"
}
```

-   `name`: `string` (required) - The internal name for the template.
-   `title`: `string` (required) - The default title for notifications using this template.
-   `message`: `string` (required) - The default message body.
-   `type`: `string` (required) - The category or type of the template.

### Response

#### Success (201 Created)

```json
{
  "success": true,
  "message": "Notification template created successfully",
  "data": {
    "id": "timestamp-string",
    "name": "Password Reset Confirmation",
    "title": "Your Password Has Been Reset",
    "message": "This is a confirmation that the password for your account has just been changed.",
    "type": "PASSWORD_RESET",
    "created_by": "admin-uuid-string",
    "created_at": "datetime-string"
  }
}
```

---

## 5. Update Notification Template

-   **Endpoint:** `PUT /api/admin/notifications/templates/:id`
-   **Description:** Updates an existing notification template. (Note: This is a placeholder implementation).
-   **Middleware:** `authMiddleware`, `adminMiddleware`

### Request

#### URL Parameters

-   `id`: `string` (required) - The UUID of the template to update.

#### Body

```json
{
  "title": "Your Password Was Successfully Reset",
  "message": "Your password has been successfully changed. If you did not make this change, please contact support immediately."
}
```

-   `name`: `string` (optional) - The updated name.
-   `title`: `string` (optional) - The updated title.
-   `message`: `string` (optional) - The updated message body.
-   `type`: `string` (optional) - The updated type.

### Response

#### Success (200 OK)

```json
{
  "success": true,
  "message": "Notification template updated successfully",
  "data": {
    "id": "uuid-string",
    "name": "Password Reset Confirmation",
    "title": "Your Password Was Successfully Reset",
    "message": "Your password has been successfully changed. If you did not make this change, please contact support immediately.",
    "type": "PASSWORD_RESET",
    "updated_by": "admin-uuid-string",
    "updated_at": "datetime-string"
  }
}
```

---

## 6. Get Notification Statistics

-   **Endpoint:** `GET /api/admin/notifications/stats`
-   **Description:** Retrieves aggregated statistics about notifications sent across the system.
-   **Middleware:** `authMiddleware`, `adminMiddleware`

### Request

No request body or query parameters.

### Response

#### Success (200 OK)

```json
{
  "success": true,
  "message": "Notification statistics retrieved successfully",
  "data": {
    "total": 500,
    "read": 150,
    "unread": 350,
    "read_rate": 30,
    "by_type": [
      {
        "type": "SYSTEM",
        "count": 200
      },
      {
        "type": "SHIPMENT_STATUS",
        "count": 300
      }
    ],
    "recent_24h": 25
  }
}
