import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data/api-endpoints';
import { getJwt } from '../../../src/utils/auth';
import { createApiError } from '../../../src/utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Get the JWT token from the request
    const { token } = await getJwt(req);

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'No token provided',
        },
      });
    }

    // Make the request to the backend with the token
    const response = await BACKEND_API(req).get(API_ENDPOINT.shipments.expiredStats, {
      headers: {
        Authorization: token,
      },
    });

    // Return the response data
    return res.status(200).json(response.data);
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}
