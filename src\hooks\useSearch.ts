import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useDebouncedCallback } from 'use-debounce';
import { useState, useEffect, useCallback } from 'react';

export const useSearch = (setPage?: (page: number) => void) => {
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();

  // ✅ Separate input display state from search query state
  const [searchInput, setSearchInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Get initial search value from URL
  const urlSearchValue = searchParams.get('query')?.toString() || '';

  // ✅ Debounced search handler - only updates search query and URL
  const debouncedSearch = useDebouncedCallback((term: string) => {
    if (setPage) {
      setPage(1);
    }

    setSearchQuery(term);

    // ⬇️ don't push a new URL if the query string is already the same
    const currentQuery = searchParams.get('query') || '';
    if (currentQuery === term.trim()) return;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params = new URLSearchParams(searchParams as any);
    if (term.trim()) {
      params.set('query', term);
    } else {
      params.delete('query');
    }
    replace(`${pathname}?${params.toString()}`);
  }, 300); // Reduced debounce time for better UX

  // ✅ Immediate input handler - only updates UI input value
  const handleInputChange = useCallback((value: string) => {
    setSearchInput(value); // Update input display immediately
    debouncedSearch(value); // Debounce the actual search
  }, [debouncedSearch]);

  // ✅ Sync URL params with local state - only when URL actually changes
  useEffect(() => {
    if (urlSearchValue !== searchQuery) {
      setSearchQuery(urlSearchValue);
      setSearchInput(urlSearchValue); // Also update the input display
    }
  }, [urlSearchValue, searchQuery]);

  // ✅ Legacy handleSearch for backward compatibility
  const handleSearch = useCallback((term: string) => {
    handleInputChange(term);
  }, [handleInputChange]);

  return {
    handleSearch,
    searchValue: searchQuery, // Use searchQuery for API calls
    searchInput, // Use searchInput for input display
    handleInputChange, // New optimized handler
  };
};
