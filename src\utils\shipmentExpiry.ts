/**
 * Utility functions for handling shipment expiry dates
 */

import dayjs from 'dayjs';
import { DATE_FORMAT } from '../data/constants';

export interface ExpiryInfo {
  isExpired: boolean;
  timeRemaining: string;
  color: 'red' | 'orange' | 'yellow' | 'blue' | 'gray';
  urgency: 'expired' | 'critical' | 'warning' | 'normal' | 'none';
}

/**
 * Check if a shipment has expired
 */
export const isShipmentExpired = (expiresAt: string | null): boolean => {
  if (!expiresAt) return false;
  return new Date(expiresAt) < new Date();
};

/**
 * Calculate time remaining until expiry
 */
export const getTimeRemaining = (expiresAt: string | null): string => {
  if (!expiresAt) return '';

  const now = new Date();
  const expiry = new Date(expiresAt);
  const diff = expiry.getTime() - now.getTime();

  if (diff <= 0) return 'Expired';

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h remaining`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes}m remaining`;
  }
  return `${minutes}m remaining`;
};

/**
 * Get expiry information with color and urgency
 */
export const getExpiryInfo = (expiresAt: string | null): ExpiryInfo => {
  if (!expiresAt) {
    return {
      isExpired: false,
      timeRemaining: '',
      color: 'gray',
      urgency: 'none',
    };
  }

  const now = new Date();
  const expiry = new Date(expiresAt);
  const diff = expiry.getTime() - now.getTime();

  if (diff <= 0) {
    return {
      isExpired: true,
      timeRemaining: 'Expired',
      color: 'red',
      urgency: 'expired',
    };
  }

  const hours = Math.floor(diff / (1000 * 60 * 60));
  const timeRemaining = getTimeRemaining(expiresAt);

  // Critical: Less than 2 hours
  if (hours < 2) {
    return {
      isExpired: false,
      timeRemaining,
      color: 'red',
      urgency: 'critical',
    };
  }

  // Warning: Less than 6 hours
  if (hours < 6) {
    return {
      isExpired: false,
      timeRemaining,
      color: 'orange',
      urgency: 'warning',
    };
  }

  // Caution: Less than 12 hours
  if (hours < 12) {
    return {
      isExpired: false,
      timeRemaining,
      color: 'yellow',
      urgency: 'warning',
    };
  }

  // Normal: More than 12 hours
  return {
    isExpired: false,
    timeRemaining,
    color: 'blue',
    urgency: 'normal',
  };
};

/**
 * Format expiry date for display
 */
export const formatExpiryDate = (expiresAt: string | null): string => {
  if (!expiresAt) return 'N/A';
  const date = new Date(expiresAt);
  if (Number.isNaN(date.getTime())) return 'Invalid date';
  return dayjs(date).format(DATE_FORMAT);
};

/**
 * Get expiry status text for display
 */
export const getExpiryStatusText = (expiresAt: string | null): string => {
  if (!expiresAt) return '';

  const info = getExpiryInfo(expiresAt);

  if (info.isExpired) {
    return 'EXPIRED';
  }

  switch (info.urgency) {
    case 'critical':
      return 'URGENT';
    case 'warning':
      return 'EXPIRES SOON';
    default:
      return 'ACTIVE';
  }
};

/**
 * Check if expiry should be highlighted (expired or critical)
 */
export const shouldHighlightExpiry = (expiresAt: string | null): boolean => {
  if (!expiresAt) return false;

  const info = getExpiryInfo(expiresAt);
  return info.urgency === 'expired' || info.urgency === 'critical';
};
