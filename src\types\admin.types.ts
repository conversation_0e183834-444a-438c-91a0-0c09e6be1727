// Admin-specific types based on API documentation

export enum UserType {
  CUSTOMER = 'CUSTOMER',
  ACCESS_OPERATOR = 'ACCESS_OPERATOR',
  CAR_OPERATOR = 'CAR_OPERATOR',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

export enum ApprovalStatus {
  APPROVED = 'APPROVED',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
}

export enum AdminRole {
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
}

export enum ShipmentStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
}

// Admin User Interface
export interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: AdminRole;
  status: UserStatus;
  created_at: string;
  updated_at: string;
  last_login?: string;
}
export interface UserProfile {
  business_name?: string;
  business_address?: string;
  business_license?: string;
  operating_hours?: string;
}
export interface UserStatistics {
  total_shipments: number;
  completed_shipments: number;
  success_rate: number;
}
// User Management Interfaces
export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  user_type: UserType;
  status: UserStatus;
  approval_status?: ApprovalStatus;
  created_at: string;
  updated_at?: string;
  last_login?: string | null;
  profile?: UserProfile;
  statistics?: UserStatistics;
  accessOperator?: {
    approved: boolean;
    business_name?: string;
    address?: string;
  } | null;
  carOperator?: {
    approved: boolean;
    business_name?: string;
    address?: string;
  } | null;
}

// Shipment Management Interfaces
export interface Shipment {
  id: string;
  trackingCode: string;
  status: ShipmentStatus;
  description: string;
  receiverName: string;
  receiverPhone?: string;
  receiverAddress?: string;
  weight?: number;
  dimensions?: string;
  specialInstructions?: string;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    name: string;
    email: string;
    phone?: string;
  } | null;
  originAO?: {
    id: string;
    businessName: string;
    address?: string;
    geoLatitude?: number;
    geoLongitude?: number;
  } | null;
  destAO?: {
    id: string;
    businessName: string;
    address?: string;
    geoLatitude?: number;
    geoLongitude?: number;
  } | null;
  auditLogs?: Array<{
    id: string;
    action: string;
    details?: string;
    createdAt: string;
    user?: {
      name: string;
      email: string;
    } | null;
    admin?: {
      name: string;
      email: string;
    } | null;
  }>;
  qrLabels?: Array<{
    id: string;
    qrCode: string;
    status: string;
    createdAt: string;
  }>;
}

export interface UserStats {
  total: number;
  customers: number;
  access_operators: number;
  car_operators: number;
  pending_approvals: number;
  active_users: number;
  inactive_users: number;
}

export interface ShipmentStats {
  total: number;
  active: number;
  delivered: number;
  cancelled: number;
  expired: number;
  delivery_rate: number;
  pending: number;
  in_transit: number;
}

export interface RecentActivity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  user_name: string;
}

export interface QuickAction {
  title: string;
  description: string;
  action: string;
  count: number;
}

// Dashboard Interfaces
export interface DashboardStats {
  user_stats: UserStats;
  shipment_stats: ShipmentStats;
  recent_activities: RecentActivity[];
  quick_actions: QuickAction[];
}

// Expired Shipment Statistics
export interface ExpiredShipmentStats {
  expired_shipments: {
    total: number;
    last_24h: number;
    last_week: number;
    last_month: number;
  };
  expiry_reasons: Array<{
    reason: string;
    count: number;
  }>;
}

// Authentication Interfaces
export interface AdminLoginRequest {
  email: string;
  password: string;
}

export interface AdminLoginResponse {
  success: boolean;
  message: string;
  data: {
    admin: AdminUser;
    token: string;
  };
}

export interface AdminRegisterRequest {
  name: string;
  email: string;
  password: string;
  role: AdminRole;
}

export interface OtpVerificationRequest {
  email: string;
  otp: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  email: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// User Management Request Interfaces
export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  user_type?: UserType;
  status?: UserStatus;
  approval_status?: ApprovalStatus;
}

export interface UserStatusChangeRequest {
  userId: string;
  status: UserStatus;
  reason?: string;
}

export interface UserApprovalRequest {
  userId: string;
  approval_status: ApprovalStatus;
  notes?: string;
}

// Admin Management Request Interfaces
export interface AdminStatusChangeRequest {
  adminId: string;
  status: UserStatus;
}

export interface AdminListParams {
  page?: number;
  limit?: number;
}

// Pagination Interface
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// API Response Interfaces
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

export interface PaginatedResponse<T, K extends string = 'items'> {
  success: boolean;
  data: {
    [P in K]: T[];
  } & {
    total: number;
    pagination: PaginationInfo;
  };
}

// Error Response Interface
export interface ApiError {
  error: string;
  message: string;
  details?: Array<{
    field: string;
    message: string;
  }>;
}
