import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, getJwt } from '../../../../src/utils';
import { apiMethods, HTTP_CODE } from '../../../../src/data';
import { BACKEND_API } from '../../../../src/lib/axios';
import { updateUserActivationApprovalBackendRequestSchema } from '../../../../src/requests/admin-users/request-transformer';
import { userActivationApprovalApiResponse } from '../../../../src/requests/admin-users/response-transformer';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id: itemId } = req.query;
  if (
    itemId
    && itemId !== 'undefined'
    && req.method === apiMethods.PUT
  ) {
    try {
      const { data } = await BACKEND_API(req).put(
        `/users/${itemId}/activation-approval`,
        updateUserActivationApprovalBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
        },
      );

      return res.status(HTTP_CODE.SUCCESS).json(userActivationApprovalApiResponse(data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
