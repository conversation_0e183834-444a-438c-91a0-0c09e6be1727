/* eslint-disable react-hooks/rules-of-hooks */
import { useMutation } from '@tanstack/react-query';
import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import {
  GetAuditLogsQueryProps,
  LockUserRequest,
  LockUserResponseType,
  UnlockUserRequest,
  UnlockUserResponseType,
} from './types';
import { transformAuditLogsParams, transformLockUserRequest, transformUnlockUserRequest } from './request-transformer';
import {
  normalizeAuditLogsResponse,
  normalizeLoginAttemptsResponse,
  normalizeSecurityEventsResponse,
} from './response-transformer';

enum queryKeys {
  auditLogs = 'audit-logs',
  loginAttempts = 'login-attempts',
  securityEvents = 'security-events',
  lockUser = 'lock-user',
  unlockUser = 'unlock-user',
}

// API Request Functions
const getAuditLogsRequest = async (props: GetAuditLogsQueryProps) => {
  const transformedParams = transformAuditLogsParams(props);

  return CLIENT_API.get(API_ENDPOINT.audit.logs, {
    params: transformedParams,
  })
    .then((res) => normalizeAuditLogsResponse(res?.data))
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

const getLoginAttemptsRequest = async () => CLIENT_API.get(API_ENDPOINT.security.loginAttempts)
  .then((res) => normalizeLoginAttemptsResponse(res?.data))
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

const getSecurityEventsRequest = async () => CLIENT_API.get(API_ENDPOINT.security.events)
  .then((res) => normalizeSecurityEventsResponse(res?.data))
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

const lockUserRequest = async (request: LockUserRequest): Promise<LockUserResponseType> => {
  const transformedRequest = transformLockUserRequest(request);

  return CLIENT_API.post(API_ENDPOINT.security.lockUser, transformedRequest)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

const unlockUserRequest = async (request: UnlockUserRequest): Promise<UnlockUserResponseType> => {
  const transformedRequest = transformUnlockUserRequest(request);

  return CLIENT_API.post(API_ENDPOINT.security.unlockUser, transformedRequest)
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// React Query Hooks
export const getAuditLogsQuery = (
  props: GetAuditLogsQueryProps,
  { isAuth }: { isAuth: boolean },
) => ({
  queryKey: [queryKeys.auditLogs, props],
  queryFn: () => getAuditLogsRequest(props),
  enabled: isAuth,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

export const getLoginAttemptsQuery = (
  { isAuth }: { isAuth: boolean },
) => ({
  queryKey: [queryKeys.loginAttempts],
  queryFn: () => getLoginAttemptsRequest(),
  enabled: isAuth,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

export const getSecurityEventsQuery = (
  { isAuth }: { isAuth: boolean },
) => ({
  queryKey: [queryKeys.securityEvents],
  queryFn: () => getSecurityEventsRequest(),
  enabled: isAuth,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

export const lockUserMutation = () => useMutation({
  mutationFn: lockUserRequest,
});

export const unlockUserMutation = () => useMutation({
  mutationFn: unlockUserRequest,
});

// Export request functions
export {
  getAuditLogsRequest,
  getLoginAttemptsRequest,
  getSecurityEventsRequest,
  lockUserRequest,
  unlockUserRequest,
};
