/* eslint-disable sonarjs/no-duplicate-string */
import { z } from 'zod';

// Constants for validation messages
const ADMIN_ID_REQUIRED = 'Admin ID is required';
const NAME_MIN_LENGTH = 'Name must be at least 2 characters';
const EMAIL_INVALID_FORMAT = 'Invalid email format';

// Zod schemas for request validation
export const adminListParamsSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
});

export const adminStatusChangeRequestSchema = z.object({
  adminId: z.string().min(1, ADMIN_ID_REQUIRED),
  status: z.enum(['ACTIVE', 'SUSPENDED']),
});

export const createAdminRequestSchema = z.object({
  name: z.string().min(2, NAME_MIN_LENGTH),
  email: z.string().email(EMAIL_INVALID_FORMAT),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['ADMIN', 'SUPER_ADMIN']),
});

export const updateAdminRequestSchema = z.object({
  adminId: z.string().min(1, ADMIN_ID_REQUIRED),
  name: z.string().min(2, NAME_MIN_LENGTH).optional(),
  email: z.string().email(EMAIL_INVALID_FORMAT).optional(),
  role: z.enum(['ADMIN', 'SUPER_ADMIN']).optional(),
});

export const updateProfileRequestSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  email: z.string().email('Invalid email format').optional(),
});

export const changePasswordRequestSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

// Zod schemas for response validation
export const adminUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  role: z.enum(['ADMIN', 'SUPER_ADMIN']),
  status: z.enum(['ACTIVE', 'SUSPENDED', 'PENDING']),
  created_at: z.string(),
  updated_at: z.string(),
  last_login: z.string().optional(),
});

export const paginationInfoSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

export const adminListResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    admins: z.array(adminUserSchema),
    total: z.number(),
    pagination: paginationInfoSchema,
  }),
});

export const adminDetailResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    admin: adminUserSchema,
  }),
});

export const createAdminResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: adminUserSchema,
  }),
});

export const updateAdminResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: adminUserSchema,
  }),
});

export const adminStatusChangeResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: z.object({
      id: z.string(),
      status: z.enum(['ACTIVE', 'SUSPENDED', 'PENDING']),
    }),
  }),
});

export const updateProfileResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: adminUserSchema,
  }),
});

export const changePasswordResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

// Type inference from schemas
export type AdminListParamsType = z.infer<typeof adminListParamsSchema>;
export type AdminStatusChangeRequestType = z.infer<typeof adminStatusChangeRequestSchema>;
export type CreateAdminRequestType = z.infer<typeof createAdminRequestSchema>;
export type UpdateAdminRequestType = z.infer<typeof updateAdminRequestSchema>;
export type UpdateProfileRequestType = z.infer<typeof updateProfileRequestSchema>;
export type ChangePasswordRequestType = z.infer<typeof changePasswordRequestSchema>;

export type AdminUserType = z.infer<typeof adminUserSchema>;
export type AdminListResponseType = z.infer<typeof adminListResponseSchema>;
export type AdminDetailResponseType = z.infer<typeof adminDetailResponseSchema>;
export type CreateAdminResponseType = z.infer<typeof createAdminResponseSchema>;
export type UpdateAdminResponseType = z.infer<typeof updateAdminResponseSchema>;
export type AdminStatusChangeResponseType = z.infer<typeof adminStatusChangeResponseSchema>;
export type UpdateProfileResponseType = z.infer<typeof updateProfileResponseSchema>;
export type ChangePasswordResponseType = z.infer<typeof changePasswordResponseSchema>;
