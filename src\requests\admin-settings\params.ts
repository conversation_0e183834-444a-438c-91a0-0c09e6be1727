/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-underscore-dangle */
import { NextApiRequest } from 'next';

// This map is used to convert front-end keys to back-end keys for the sort parameter
const sortKeysMapping = new Map<string, string>([
  ['name', 'name'],
  ['email', 'email'],
  ['userType', 'user_type'],
  ['status', 'status'],
  ['approvalStatus', 'approval_status'],
  ['createdAt', 'created_at'],
  ['updatedAt', 'updated_at'],
  ['lastLogin', 'last_login'],
]);

// eslint-disable-next-line complexity
export const returnSettingsListParams = (req: NextApiRequest) => {
  const params = req.query;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const cleanParams: Record<string, any> = {};

  // Handle pagination - both frontend and backend use 1-based pagination
  // Always set page parameter with default value of 1
  const pageNum = params.page ? parseInt(params.page as string, 10) : 1;
  cleanParams.page = Number.isNaN(pageNum) ? 1 : Math.max(1, pageNum);

  // Always set limit parameter with default value of 20
  let limitNum = 20;
  if (params.limit) {
    limitNum = parseInt(params.limit as string, 10);
  } else if (params.pageSize) {
    limitNum = parseInt(params.pageSize as string, 10);
  }
  cleanParams.limit = Number.isNaN(limitNum) ? 20 : limitNum;

  // Handle search
  if (params.search) {
    cleanParams.search = params.search;
  }

  // Handle filters - map frontend parameter names to backend parameter names
  if (params.userType) {
    cleanParams.user_type = params.userType;
  }
  if (params.user_type) {
    cleanParams.user_type = params.user_type;
  }
  if (params.status) {
    cleanParams.status = params.status;
  }
  if (params.approvalStatus) {
    cleanParams.approval_status = params.approvalStatus;
  }
  if (params.approval_status) {
    cleanParams.approval_status = params.approval_status;
  }

  // Handle sorting - backend expects 'sort' and 'order' parameters
  if (params.sort) {
    const sortParam = params.sort as string;
    if (sortParam.includes(':')) {
      const [field, direction] = sortParam.split(':');
      // Map frontend field names to backend field names
      const mappedField = sortKeysMapping.get(field) || field;
      cleanParams.sort = mappedField;
      cleanParams.order = direction;
    } else {
      // Fallback for simple field names
      const mappedField = sortKeysMapping.get(sortParam) || sortParam;
      cleanParams.sort = mappedField;
      cleanParams.order = 'asc';
    }
  }

  // Handle detail flag for user detail requests
  if (params.detail) {
    cleanParams.detail = params.detail;
  }

  // Handle user ID for detail requests
  if (params.id) {
    cleanParams.id = params.id;
  }

  // Add cache buster for fresh data
  if (params._t) {
    cleanParams._t = params._t;
  }

  return cleanParams;
};

// Helper function to transform sort parameter using the mapping
export const transformSortParam = (sort: string): string => {
  if (!sort) return sort;

  // Handle descending sort (starts with -)
  const isDescending = sort.startsWith('-');
  const sortKey = isDescending ? sort.substring(1) : sort;

  // Map the sort key if it exists in our mapping
  const mappedKey = sortKeysMapping.get(sortKey) || sortKey;

  // Return with proper prefix
  return isDescending ? `-${mappedKey}` : mappedKey;
};

// Export the mapping for external use
export { sortKeysMapping };
