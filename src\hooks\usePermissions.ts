import { useAdminAuthContext } from '../contexts/AdminAuthContext';
import {
  Permission,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getUserPermissions,
  canManageAdmin,
  canAccessAdminManagement,
  canCreateAdmin,
  canEditAdmin,
  canDeleteAdmin,
  canChangeAdminStatus,
  canManageUsers,
  canApproveOperators,
  canAccessSystemSettings,
  canManageSystemSettings,
} from '../utils/permissions';
import { AdminUser } from '../types/admin.types';

/**
 * Custom hook for checking user permissions
 */
export const usePermissions = () => {
  const { admin } = useAdminAuthContext();

  return {
    // Current user
    user: admin,

    // Basic permission checks
    hasPermission: (permission: Permission) => hasPermission(admin, permission),
    hasAnyPermission: (permissions: Permission[]) => hasAnyPermission(admin, permissions),
    hasAllPermissions: (permissions: Permission[]) => hasAllPermissions(admin, permissions),
    getUserPermissions: () => getUserPermissions(admin),

    // Admin management permissions
    canManageAdmin: (targetAdmin: AdminUser) => canManageAdmin(admin, targetAdmin),
    canAccessAdminManagement: () => canAccessAdminManagement(admin),
    canCreateAdmin: () => canCreateAdmin(admin),
    canEditAdmin: () => canEditAdmin(admin),
    canDeleteAdmin: () => canDeleteAdmin(admin),
    canChangeAdminStatus: () => canChangeAdminStatus(admin),

    // User management permissions
    canManageUsers: () => canManageUsers(admin),
    canApproveOperators: () => canApproveOperators(admin),

    // System permissions
    canAccessSystemSettings: () => canAccessSystemSettings(admin),
    canManageSystemSettings: () => canManageSystemSettings(admin),

    // Convenience checks
    isSuperAdmin: admin?.role === 'SUPER_ADMIN',
    isAdmin: admin?.role === 'ADMIN',
    isAuthenticated: !!admin,
  };
};

export default usePermissions;
