import { Pagination } from '../../types';

export type Filter = {
    search?: string;
    createdAtGte?: string | null;
    createdAtLte?: string | null;
    status?: string;
    originAoId?: string;
    destAoId?: string;
    customerId?: string;
};

export type getShipmentsQueryProps = {
  pagination?: Pagination;
   filters?: Filter;
   sort?: string;
   // eslint-disable-next-line @typescript-eslint/no-explicit-any
   params?: any;
};
