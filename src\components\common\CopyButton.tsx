import { ActionIcon, Tooltip } from '@mantine/core';
import { useClipboard } from '@mantine/hooks';
import { IconCopy, IconCheck } from '@tabler/icons-react';

interface CopyButtonProps {
  value: string;
  // eslint-disable-next-line react/require-default-props
  tooltipLabel?: string;
}

export function CopyButton({ value, tooltipLabel = 'Copy' }: CopyButtonProps) {
  const clipboard = useClipboard({ timeout: 500 });

  return (
    <Tooltip label={clipboard.copied ? 'Copied!' : tooltipLabel} withArrow position="right">
      <ActionIcon
        variant="subtle"
        color={clipboard.copied ? 'teal' : 'gray'}
        onClick={() => clipboard.copy(value)}
        size="sm"
      >
        {clipboard.copied ? <IconCheck size="0.8rem" /> : <IconCopy size="0.8rem" />}
      </ActionIcon>
    </Tooltip>
  );
}
