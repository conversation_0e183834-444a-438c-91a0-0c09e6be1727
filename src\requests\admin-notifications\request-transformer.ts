import * as z from 'zod';
import { AdminNotificationType, AdminNotificationPriority } from './types';

// Schema for getting admin notifications request
export const GetAdminNotificationsRequest = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  type: z.nativeEnum(AdminNotificationType).optional(),
  priority: z.nativeEnum(AdminNotificationPriority).optional(),
  read: z.boolean().optional(),
  fromDate: z.string().datetime().optional(),
  toDate: z.string().datetime().optional(),
  shipmentId: z.string().uuid().optional(),
});

// Schema for marking notification as read
export const MarkNotificationAsReadRequest = z.object({
  id: z.string().uuid(),
});

// Schema for marking all notifications as read
export const MarkAllNotificationsAsReadRequest = z.object({
  // No additional parameters needed
});

// Schema for deleting notification
export const DeleteNotificationRequest = z.object({
  id: z.string().uuid(),
});

// Transform get notifications request for backend
export const transformGetAdminNotificationsRequest = GetAdminNotificationsRequest.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result: Record<string, any> = {};

  if (data.page !== undefined) result.page = data.page;
  if (data.limit !== undefined) result.limit = data.limit;
  if (data.search !== undefined) result.search = data.search;
  if (data.type !== undefined) result.type = data.type;
  if (data.priority !== undefined) result.priority = data.priority;
  if (data.read !== undefined) result.read = data.read;
  if (data.fromDate !== undefined) result.from_date = data.fromDate;
  if (data.toDate !== undefined) result.to_date = data.toDate;
  if (data.shipmentId !== undefined) result.shipment_id = data.shipmentId;

  return result;
});

// Transform mark as read request for backend
export const transformMarkAsReadRequest = MarkNotificationAsReadRequest.transform((data) => ({
  id: data.id,
}));

// Transform delete request for backend
export const transformDeleteRequest = DeleteNotificationRequest.transform((data) => ({
  id: data.id,
}));
