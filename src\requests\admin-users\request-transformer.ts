import * as z from 'zod';

export const UserActivationApprovalRequest = z.object({
  active: z.boolean(),
  approved: z.boolean().optional(),
  reason: z.string().min(1).max(500).optional(),
});

export const updateUserActivationApprovalBackendRequestSchema = UserActivationApprovalRequest.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result: Record<string, any> = {
    active: data.active,
    approved: data.approved,
    reason: data.reason,
  };

  return result;
});
