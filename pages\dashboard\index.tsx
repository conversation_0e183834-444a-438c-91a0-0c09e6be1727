// pages/dashboard/index.tsx

import {
  Container,
  Title,
  Text,
  Grid,
  Paper,
  Group,
  Button,
  LoadingOverlay,
  Alert,
} from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useAdminAuthContext } from '../../src/contexts/AdminAuthContext';
import { getDashboardStatsQuery, AdminDashboardResponse } from '../../src/requests/admin-dashboard';
import { StatsGrid, RecentActivities, QuickActions } from '../../src/components/dashboard';

export default function AdminDashboard() {
  const { logout } = useAdminAuthContext();

  const {
    data: dashboard,
    isLoading,
    error,
    refetch,
  } = useQuery<AdminDashboardResponse>(getDashboardStatsQuery());

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <LoadingOverlay visible />
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading dashboard"
          color="red"
          variant="light"
        >
          <Text mb="md">Failed to load dashboard data. Please try again.</Text>
          <Group>
            <Button variant="outline" onClick={() => refetch()}>
              Retry
            </Button>
            <Button color="red" onClick={logout}>
              Logout
            </Button>
          </Group>
        </Alert>
      </Container>
    );
  }

  if (!dashboard) {
    return (
      <Container size="xl" py="xl">
        <Paper withBorder shadow="sm" p="lg">
          <Text ta="center" c="dimmed">
            No dashboard data available
          </Text>
        </Paper>
      </Container>
    );
  }

  const { adminInfo, dashboardData } = dashboard;

  return (
    <Container size="xl" py="xl">
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={1}>Admin Dashboard</Title>
          <Text c="dimmed" size="lg">
            Welcome back,
            {' '}
            {adminInfo.name}
            !
          </Text>
        </div>
      </Group>

      <StatsGrid
        userStats={{
          total: dashboardData.totalUsers,
          customers: dashboardData.totalCustomers,
          access_operators: dashboardData.totalAccessOperators,
          car_operators: dashboardData.totalCarOperators,
          pending_approvals: dashboardData.pendingApprovals,
          active_users: dashboardData.totalUsers - dashboardData.pendingApprovals,
          inactive_users: 0,
        }}
        shipmentStats={{
          total: dashboardData.totalShipments,
          active: dashboardData.activeShipments,
          delivered: dashboardData.deliveredShipments,
          delivery_rate: dashboardData.deliveryRate,
          cancelled: 0,
          expired: 0,
          pending: 0,
          in_transit: dashboardData.activeShipments,
        }}
      />

      <Grid mt="lg">
        <Grid.Col span={{ base: 12, md: 8 }}>
          <RecentActivities
            activities={dashboardData.recentUsers.map((u) => ({
              id: u.id,
              type: 'user_registration',
              description: `New ${u.user_type
                .toLowerCase()
                .replace('_', ' ')} registered: ${u.name}`,
              timestamp: u.created_at,
              user_name: u.name,
            }))}
          />
        </Grid.Col>
        <Grid.Col span={{ base: 12, md: 4 }}>
          <QuickActions
            actions={dashboardData.quickActions.map((a) => ({
              title: a.label,
              description: `Click to ${a.action.replace('_', ' ')}`,
              action: a.action,
              count: 0,
            }))}
          />
        </Grid.Col>
      </Grid>
    </Container>
  );
}
