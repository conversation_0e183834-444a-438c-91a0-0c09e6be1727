/* eslint-disable no-console */
import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, createApiResponse, getJwt } from '../../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../../src/data';
import { BACKEND_API } from '../../../../src/lib/axios';
import { downloadExportResponseSchema } from '../../../../src/requests/export/response-transformer';
import { downloadExportRequestSchema } from '../../../../src/requests/export/request-transformer';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id } = req.query;

  if (req.method === apiMethods.GET) {
    try {
      if (!id || typeof id !== 'string') {
        const error = createApiError({ error: 'Export ID is required' });
        return res.status(HTTP_CODE.BAD_REQUEST).json(error);
      }

      // Validate the ID parameter
      const validatedParams = downloadExportRequestSchema.parse({ id });

      const { data } = await BACKEND_API(req).get(API_ENDPOINT.export.download(validatedParams.id), {
        headers: { authorization: token },
      });

      return createApiResponse(
        res,
        downloadExportResponseSchema,
        {
          success: data.success,
          message: data.message,
          data: data.data,
        },
      );
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: 'Method not allowed' });
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
}

export default handler;
