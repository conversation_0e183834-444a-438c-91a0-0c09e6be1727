/* eslint-disable no-console */
import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, getJwt } from '../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { transformSystemSettingsResponse } from '../../../src/requests/admin-settings/response-transformer';
import { updateSystemSettingsBackendRequestSchema } from '../../../src/requests/admin-settings/request-transformer';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === apiMethods.GET) {
    try {
      const { data } = await BACKEND_API(req).get(API_ENDPOINT.settings.list(), {
        headers: { authorization: token },
      });

      const settingsData = data.data || {};

      const transformedData = transformSystemSettingsResponse(settingsData);

      return res.status(200).json({
        success: data.success,
        message: data.message,
        data: transformedData,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  if (req.method === apiMethods.PUT) {
    try {
      // Validate and transform the request body
      const validatedData = updateSystemSettingsBackendRequestSchema.parse(req.body);

      const { data } = await BACKEND_API(req).put(API_ENDPOINT.settings.list(), validatedData, {
        headers: { authorization: token },
      });

      // Handle case where data might be null or empty
      const settingsData = data.data || {};

      // Transform the data
      const transformedData = transformSystemSettingsResponse(settingsData);

      // Return response without schema validation for now
      return res.status(200).json({
        success: data.success,
        message: data.message,
        data: transformedData,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: 'Method not allowed' });
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
}

export default handler;
