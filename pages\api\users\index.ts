/* eslint-disable no-console */
import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { userDetailsApiResponse, usersListResponseSchema } from '../../../src/requests/admin-users/response-transformer';
import { returnUserListParams } from '../../../src/requests/admin-users';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id: userId } = req.query;
  if (userId && userId !== 'undefined' && req.method === apiMethods.GET) {
    try {
      const { data } = await BACKEND_API(req).get(
        API_ENDPOINT.users.list(`${userId}`),
        {
          headers: {
            authorization: token,
          },
        },
      );
      return res.status(HTTP_CODE.SUCCESS).json(userDetailsApiResponse(data.data));
    } catch (e) {
      console.log('error', e);
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  } else if (req.method === apiMethods.GET) {
    try {
      const { data } = await BACKEND_API(req).get(API_ENDPOINT.users.list(), {
        headers: { authorization: token },
        params: { ...returnUserListParams(req) },
      });

      return createApiResponse(
        res,
        usersListResponseSchema,
        {
          success: data.success,
          message: data.message,
          data: data.data,
        },
      );
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
