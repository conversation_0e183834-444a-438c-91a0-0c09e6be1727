import { MantineProvider, localStorageColorSchemeManager, useMantineColorScheme } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type { ReactNode } from 'react';
import { useState } from 'react';
import { useHotkeys } from '@mantine/hooks';
import { createLocaleTheme } from '../../../theme';
import { NotificationProvider } from '../notifications';

interface ProvidersProps {
  children: ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000,
        refetchOnWindowFocus: false,
      },
    },
  }));
  const currentTheme = createLocaleTheme();

  const colorSchemeManager = localStorageColorSchemeManager({
    key: 'mantine-color-scheme',
  });

  // Component to register color scheme hotkey within MantineProvider context
  function ColorSchemeHotkeys() {
    const { toggleColorScheme } = useMantineColorScheme();
    useHotkeys([['mod+J', toggleColorScheme]]);
    return null;
  }

  return (
    <div dir="ltr" className="font-english">
      <QueryClientProvider client={queryClient}>
        <MantineProvider
          theme={currentTheme}
          defaultColorScheme="light"
          colorSchemeManager={colorSchemeManager}
        >
          <ColorSchemeHotkeys />
          <Notifications zIndex={11000} />
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </MantineProvider>
      </QueryClientProvider>
    </div>
  );
}
