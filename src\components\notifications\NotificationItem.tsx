/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable react/require-default-props */
import {
  Group,
  Text,
  ActionIcon,
  Box,
  Badge,
  UnstyledButton,
  useMantineTheme,
  useMantineColorScheme,
  Tooltip,
} from '@mantine/core';
import {
  IconCheck,
  IconAlertCircle,
  IconInfoCircle,
  IconCircleCheck,
} from '@tabler/icons-react';
import { useIsClient } from '../../hooks/useIsClient';
import type { Notification } from '../../requests/notifications';

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead?: (id: string) => void;
  onClick?: () => void;
  compact?: boolean;
}

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'SUCCESS':
      return IconCircleCheck;
    case 'WARNING':
      return IconAlertCircle;
    case 'ERROR':
      return IconAlertCircle;
    case 'INFO':
    default:
      return IconInfoCircle;
  }
};

const getNotificationColor = (type: string) => {
  switch (type) {
    case 'SUCCESS':
      return 'green';
    case 'WARNING':
      return 'orange';
    case 'ERROR':
      return 'red';
    case 'INFO':
    default:
      return 'blue';
  }
};

export default function NotificationItem({
  notification,
  onMarkAsRead,
  onClick,
  compact = false,
}: NotificationItemProps) {
  const isClient = useIsClient();
  const theme = useMantineTheme();
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === 'dark';

  // Use notification title and message directly
  const title = notification.title || 'Notification';
  const message = notification.message || 'No message available';

  const IconComponent = getNotificationIcon(notification.notification_type);
  const iconColor = getNotificationColor(notification.notification_type);

  const formatDate = (dateString: string) => {
    if (!isClient) return 'Loading...';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Invalid date';
    }
  };

  const handleClick = () => {
    if (!notification.read && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
    if (onClick) {
      onClick();
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <UnstyledButton
      onClick={handleClick}
      style={{
        width: '100%',
        padding: compact ? theme.spacing.xs : theme.spacing.md,
        backgroundColor: notification.read
          ? 'transparent'
          : isDark
            ? theme.colors.dark[6]
            : theme.colors.gray[0],
        borderBottom: `1px solid ${isDark ? theme.colors.dark[4] : theme.colors.gray[2]}`,
        transition: 'background-color 0.2s ease',
      }}
      __vars={{
        '--button-hover': isDark ? theme.colors.dark[5] : theme.colors.gray[1],
      }}
    >
      <Group gap="sm" align="flex-start" wrap="nowrap">
        <IconComponent
          size={compact ? '1rem' : '1.2rem'}
          color={theme.colors[iconColor][6]}
          style={{ marginTop: '2px', flexShrink: 0 }}
        />

        <Box style={{ flex: 1, minWidth: 0 }}>
          <Group gap="xs" justify="space-between" align="flex-start" wrap="nowrap">
            <Box style={{ flex: 1, minWidth: 0 }}>
              <Text
                fw={notification.read ? 400 : 600}
                size={compact ? 'xs' : 'sm'}
                lineClamp={compact ? 1 : 2}
                c={isDark ? 'gray.1' : 'dark.7'}
              >
                {title}
              </Text>
              {!compact && message && (
                <Text
                  size="xs"
                  c="dimmed"
                  lineClamp={2}
                  mt={2}
                >
                  {message}
                </Text>
              )}
            </Box>

            <Group gap="xs" align="center" style={{ flexShrink: 0 }}>
              {!notification.read && (
                <Badge
                  size="xs"
                  variant="filled"
                  color="blue"
                  style={{ flexShrink: 0 }}
                >
                  New
                </Badge>
              )}
              {!notification.read && onMarkAsRead && (
                <Tooltip label="Mark as read" position="top">
                  <ActionIcon
                    size="md"
                    variant="subtle"
                    color="gray"
                    onClick={handleMarkAsRead}
                    aria-label="Mark as read"
                    style={{ padding: '4px' }}
                  >
                    <IconCheck size="1rem" />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          </Group>

          <Text
            size="xs"
            c="dimmed"
            mt={compact ? 2 : 4}
          >
            {formatDate(notification.created_at)}
          </Text>
        </Box>
      </Group>
    </UnstyledButton>
  );
}
