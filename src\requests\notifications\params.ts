// Parameter transformation utilities for notifications

export interface NotificationQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  read?: boolean;
  type?: string;
  priority?: string;
  shipmentId?: string;
  fromDate?: string;
  toDate?: string;
}

export interface NotificationFilters {
  search?: string;
  read?: boolean;
  type?: string;
  priority?: string;
  shipmentId?: string;
  fromDate?: string;
  toDate?: string;
}

export interface NotificationPagination {
  page?: number;
  limit?: number;
}

export interface NotificationSort {
  field?: string;
  order?: 'asc' | 'desc';
}

// Default values
export const DEFAULT_NOTIFICATION_PARAMS = {
  page: 0,
  limit: 20,
} as const;

export const DEFAULT_NOTIFICATION_FILTERS = {
  search: undefined,
  read: undefined,
  type: undefined,
  priority: undefined,
  shipmentId: undefined,
  fromDate: undefined,
  toDate: undefined,
} as const;

// Parameter builders
export const buildNotificationQueryParams = (
  pagination?: NotificationPagination,
  filters?: NotificationFilters,
  sort?: NotificationSort,
): NotificationQueryParams => ({
  ...DEFAULT_NOTIFICATION_PARAMS,
  ...pagination,
  ...filters,
  ...(sort?.field && { sortBy: sort.field }),
  ...(sort?.order && { sortOrder: sort.order }),
});

// URL parameter transformation
export const transformToUrlParams = (params: NotificationQueryParams): URLSearchParams => {
  const urlParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      urlParams.append(key, String(value));
    }
  });

  return urlParams;
};

// Parameter validation
export const validateNotificationParams = (params: NotificationQueryParams): NotificationQueryParams => {
  const validated: NotificationQueryParams = {};

  // Validate pagination
  if (params.page !== undefined) {
    validated.page = Math.max(0, Math.floor(params.page));
  }

  if (params.limit !== undefined) {
    validated.limit = Math.min(100, Math.max(1, Math.floor(params.limit)));
  }

  // Validate filters
  if (params.read !== undefined) {
    validated.read = Boolean(params.read);
  }

  if (params.type && typeof params.type === 'string') {
    validated.type = params.type.trim();
  }

  if (params.priority && typeof params.priority === 'string') {
    const validPriorities = ['LOW', 'NORMAL', 'HIGH', 'URGENT'];
    if (validPriorities.includes(params.priority.toUpperCase())) {
      validated.priority = params.priority.toUpperCase();
    }
  }

  if (params.shipmentId && typeof params.shipmentId === 'string') {
    validated.shipmentId = params.shipmentId.trim();
  }

  if (params.fromDate && typeof params.fromDate === 'string') {
    validated.fromDate = params.fromDate;
  }

  if (params.toDate && typeof params.toDate === 'string') {
    validated.toDate = params.toDate;
  }

  return validated;
};
