/* eslint-disable react/require-default-props */
import { ReactNode } from 'react';
import { Alert, Text } from '@mantine/core';
import { IconLock } from '@tabler/icons-react';
import { useAdminAuthContext } from '../../contexts/AdminAuthContext';
import {
  Permission, hasPermission, hasAnyPermission, hasAllPermissions,
} from '../../utils/permissions';

interface PermissionGuardProps {
  children: ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean; // If true, requires ALL permissions; if false, requires ANY permission
  fallback?: ReactNode;
  showFallback?: boolean;
}

/**
 * Component that conditionally renders children based on user permissions
 */
export function PermissionGuard({
  children,
  permission,
  permissions,
  requireAll = false,
  fallback,
  showFallback = false,
}: PermissionGuardProps) {
  const { admin } = useAdminAuthContext();

  // Check permissions
  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(admin, permission);
  } else if (permissions && permissions.length > 0) {
    if (requireAll) {
      hasAccess = hasAllPermissions(admin, permissions);
    } else {
      hasAccess = hasAnyPermission(admin, permissions);
    }
  } else {
    // No permissions specified, allow access
    hasAccess = true;
  }

  if (hasAccess) {
    return children;
  }

  if (fallback) {
    return fallback;
  }

  if (showFallback) {
    return (
      <Alert
        icon={<IconLock size="1rem" />}
        title="Access Denied"
        color="red"
        variant="light"
      >
        <Text size="sm">
          You don&apos;t have permission to access this feature.
        </Text>
      </Alert>
    );
  }

  return null;
}

export default PermissionGuard;
