/* eslint-disable react/require-default-props */
import {
  Box, Loader, Text, Overlay,
} from '@mantine/core';
import { ReactNode } from 'react';

interface FullScreenLoaderProps {
  visible: boolean;
  text?: string;
  children?: ReactNode;
}

/**
 * FullScreenLoader component that provides a screen-level loading overlay
 * This component covers the entire viewport and shows a loading spinner
 */
export default function FullScreenLoader({
  visible,
  text = 'Loading...',
  children,
}: FullScreenLoaderProps) {
  if (!visible && !children) {
    return null;
  }

  return (
    <>
      {children}
      {visible && (
        <Overlay
          color="#000"
          backgroundOpacity={0.35}
          blur={2}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Box
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '2rem',
              borderRadius: 'var(--mantine-radius-md)',
              boxShadow: 'var(--mantine-shadow-lg)',
              minWidth: '200px',
            }}
          >
            <Loader size="lg" mb="md" />
            <Text size="sm" c="dimmed" ta="center">
              {text}
            </Text>
          </Box>
        </Overlay>
      )}
    </>
  );
}
