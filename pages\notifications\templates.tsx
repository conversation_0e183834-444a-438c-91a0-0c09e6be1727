/* eslint-disable no-nested-ternary */
/* eslint-disable max-lines */
import React, { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Text,
  Table,
  Badge,
  ActionIcon,
  Menu,
  TextInput,
  Select,
  Pagination,
  Center,
  Alert,
  LoadingOverlay,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconPlus,
  IconSearch,
  IconDots,
  IconEdit,
  IconCopy,
  IconTrash,
  IconEye,
  IconTemplate,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { notifications } from '@mantine/notifications';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import AdminLayout from '../../src/components/layouts/AdminLayout';
import { PermissionGuard } from '../../src/components/auth/PermissionGuard';
import { Permission } from '../../src/utils/permissions';
import {
  getTemplatesQuery,
  deleteTemplateMutationWithInvalidation,
  duplicateTemplateMutationWithInvalidation,
} from '../../src/requests/notifications/template-calls';

export default function NotificationTemplatesPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch templates from API
  const {
    data: templatesData,
    isLoading,
  } = useQuery(
    getTemplatesQuery({
      page: currentPage - 1,
      limit: 10,
      search: searchQuery || undefined,
      isActive:
        statusFilter === 'ACTIVE'
          ? true
          : statusFilter === 'INACTIVE'
            ? false
            : undefined,
    }),
  );

  // API mutations
  const deleteMutation = useMutation(deleteTemplateMutationWithInvalidation(queryClient));
  const duplicateMutation = useMutation(duplicateTemplateMutationWithInvalidation(queryClient));

  const handleBack = () => {
    router.push('/notifications');
  };

  const handleCreateTemplate = () => {
    router.push('/notifications/templates/new');
  };

  const handleEditTemplate = (templateId: string) => {
    router.push(`/notifications/templates/${templateId}`);
  };

  const handleDuplicateTemplate = async (templateId: string) => {
    try {
      await duplicateMutation.mutateAsync({
        id: templateId,
        newName: '',
      });
      notifications.show({
        title: 'success',
        message: 'templates.templateDuplicated',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'error',
        message: 'templates.duplicateFailed',
        color: 'red',
      });
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    try {
      await deleteMutation.mutateAsync({ id: templateId });
      notifications.show({
        title: 'success',
        message: 'templates.templateDeleted',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'error',
        message: 'templates.deleteFailed',
        color: 'red',
      });
    }
  };

  const getStatusColor = (isActive: boolean) => (isActive ? 'green' : 'gray');

  const getStatusLabel = (isActive: boolean) => (isActive ? 'templates.active' : 'templates.inactive');

  const templates = templatesData?.data?.templates || [];
  const totalPages = templatesData?.data?.pagination?.totalPages || 1;

  return (
    <AdminLayout>
      <PermissionGuard permission={Permission.MANAGE_NOTIFICATION_TEMPLATES}>
        <Container size="xl" py="md">
          <Stack gap="lg">
            {/* Header */}
            <Group justify="space-between">
              <div>
                <Title order={1} size="h2">
                  templates.notificationTemplates
                </Title>
                <Text c="dimmed" size="sm" mt={4}>
                  templates.manageNotificationTemplates
                </Text>
              </div>
              <Group gap="sm">
                <Button
                  variant="light"
                  leftSection={<IconArrowLeft size="1rem" />}
                  onClick={handleBack}
                >
                  management.back
                </Button>
                <Button
                  leftSection={<IconPlus size="1rem" />}
                  onClick={handleCreateTemplate}
                >
                  templates.createTemplate
                </Button>
              </Group>
            </Group>

            {/* Filters */}
            <Paper withBorder p="md">
              <Group gap="md">
                <TextInput
                  placeholder="searchTemplates"
                  leftSection={<IconSearch size="1rem" />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  style={{ flex: 1 }}
                />
                <Select
                  placeholder="filterByStatus"
                  data={[
                    { value: '', label: 'allStatuses' },
                    { value: 'ACTIVE', label: 'active' },
                    { value: 'DRAFT', label: 'draft' },
                    { value: 'INACTIVE', label: 'inactive' },
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                  clearable
                />
              </Group>
            </Paper>

            {/* Templates Table */}
            <Paper withBorder pos="relative">
              <LoadingOverlay visible={isLoading} />
              {templates.length === 0 ? (
                <Center py="xl">
                  <Stack align="center" gap="md">
                    <IconTemplate size="3rem" color="gray" />
                    <div style={{ textAlign: 'center' }}>
                      <Text size="lg" fw={500}>
                        {searchQuery || statusFilter ? 'noTemplatesFound' : 'noTemplates'}
                      </Text>
                      <Text c="dimmed" size="sm">
                        {searchQuery || statusFilter
                          ? 'tryDifferentFilters'
                          : 'createFirstTemplate'}
                      </Text>
                    </div>
                    {!searchQuery && !statusFilter && (
                      <Button
                        leftSection={<IconPlus size="1rem" />}
                        onClick={handleCreateTemplate}
                      >
                        createTemplate
                      </Button>
                    )}
                  </Stack>
                </Center>
              ) : (
                <>
                  <Table>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th>templateName</Table.Th>
                        <Table.Th>type</Table.Th>
                        <Table.Th>status</Table.Th>
                        <Table.Th>usageCount</Table.Th>
                        <Table.Th>lastUsed</Table.Th>
                        <Table.Th>created</Table.Th>
                        <Table.Th />
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {templates.map((template) => (
                        <Table.Tr key={template.id}>
                          <Table.Td>
                            <Text fw={500}>{template.name}</Text>
                          </Table.Td>
                          <Table.Td>
                            <Text size="sm" c="dimmed">
                              {template.type.replace('_', ' ')}
                            </Text>
                          </Table.Td>
                          <Table.Td>
                            <Badge color={getStatusColor(template.isActive)} size="sm">
                              {getStatusLabel(template.isActive)}
                            </Badge>
                          </Table.Td>
                          <Table.Td>
                            <Text size="sm">
                              {template.usageCount.toLocaleString()}
                            </Text>
                          </Table.Td>
                          <Table.Td>
                            <Text size="sm" c="dimmed">
                              {template.updatedAt
                                ? new Date(template.updatedAt).toLocaleDateString()
                                : 'templates.never'}
                            </Text>
                          </Table.Td>
                          <Table.Td>
                            <Text size="sm" c="dimmed">
                              {new Date(template.createdAt).toLocaleDateString()}
                            </Text>
                          </Table.Td>
                          <Table.Td>
                            <Menu shadow="md" width={200}>
                              <Menu.Target>
                                <ActionIcon variant="subtle" color="gray">
                                  <IconDots size="1rem" />
                                </ActionIcon>
                              </Menu.Target>
                              <Menu.Dropdown>
                                <Menu.Item
                                  leftSection={<IconEye size="0.9rem" />}
                                  onClick={() => handleEditTemplate(template.id)}
                                >
                                  templates.view
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={<IconEdit size="0.9rem" />}
                                  onClick={() => handleEditTemplate(template.id)}
                                >
                                  templates.edit
                                </Menu.Item>
                                <Menu.Item
                                  leftSection={<IconCopy size="0.9rem" />}
                                  onClick={() => handleDuplicateTemplate(template.id)}
                                  disabled={duplicateMutation.isPending}
                                >
                                  templates.duplicate
                                </Menu.Item>
                                <Menu.Divider />
                                <Menu.Item
                                  leftSection={<IconTrash size="0.9rem" />}
                                  color="red"
                                  onClick={() => handleDeleteTemplate(template.id)}
                                  disabled={template.usageCount > 0 || deleteMutation.isPending}
                                >
                                  delete
                                </Menu.Item>
                              </Menu.Dropdown>
                            </Menu>
                          </Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <Group justify="center" p="md">
                      <Pagination
                        total={totalPages}
                        value={currentPage}
                        onChange={setCurrentPage}
                      />
                    </Group>
                  )}
                </>
              )}
            </Paper>

            {/* Info Alert */}
            <Alert color="blue">
              <Text size="sm">
                templatesInfo
              </Text>
            </Alert>
          </Stack>
        </Container>
      </PermissionGuard>
    </AdminLayout>
  );
}

// Protect the page with authentication
NotificationTemplatesPage.auth = true;
