import { z } from 'zod';

// Base API response schema
const baseApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

// Export Job Data Schema for Users
export const exportUsersJobDataSchema = z.object({
  id: z.string(), // Not UUID - uses timestamp
  type: z.literal('users'),
  format: z.enum(['csv', 'excel', 'json']),
  filters: z.record(z.any()),
  status: z.literal('processing'),
  created_by: z.string().uuid(),
  created_at: z.string().datetime(),
});

// Export Job Data Schema for Shipments
export const exportShipmentsJobDataSchema = z.object({
  id: z.string(), // Not UUID - uses timestamp
  type: z.literal('shipments'),
  format: z.enum(['csv', 'excel', 'json']),
  filters: z.record(z.any()),
  status: z.literal('processing'),
  created_by: z.string().uuid(),
  created_at: z.string().datetime(),
});

// Export Response Schemas
export const exportUsersResponseSchema = baseApiResponseSchema.extend({
  data: exportUsersJobDataSchema,
});

export const exportShipmentsResponseSchema = baseApiResponseSchema.extend({
  data: exportShipmentsJobDataSchema,
});

// Legacy export response schema for backward compatibility
export const exportResponseSchema = baseApiResponseSchema.extend({
  data: z.union([exportUsersJobDataSchema, exportShipmentsJobDataSchema]),
});

// Download Export Response Schema - handles both processing and completed states
export const downloadExportResponseSchema = z.union([
  baseApiResponseSchema.extend({
    data: z.object({
      export_id: z.string(),
    }),
  }),
  z.object({
    success: z.boolean(),
    message: z.string(),
    // File will be streamed directly, no JSON response when completed
  }),
]);
