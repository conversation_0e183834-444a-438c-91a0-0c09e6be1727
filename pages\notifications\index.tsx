import React, { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Text,
  Badge,
  ActionIcon,
  Tooltip,
  Box,
} from '@mantine/core';
import {
  IconRefresh,
  IconBroadcast,
  IconTemplate,
  IconChartBar,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { useDebouncedValue } from '@mantine/hooks';
import { PermissionGuard } from '../../src/components/auth/PermissionGuard';
import { Permission } from '../../src/utils/permissions';
import { NotificationList } from '../../src/components/notifications';
import {
  getAdminNotificationsQuery,
  markAllAsReadMutation,
  markAsReadMutation,
} from '../../src/requests/admin-notifications/calls';
import { useLoading } from '../../src/contexts/LoadingContext';
import { AdminNotificationFilter } from '../../src/requests/admin-notifications/types';

const ADMIN_NOTIFICATIONS_QUERY_KEY = 'admin-notifications';

export default function NotificationsManagementPage() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState<AdminNotificationFilter>({
    search: '',
    read: undefined,
    type: undefined,
    priority: undefined,
  });
  const [pagination, setPagination] = useState({ page: 0, limit: 20 });
  const [debouncedFilters] = useDebouncedValue(filters, 300);

  const {
    data: notificationData,
    isLoading,
    isFetching,
    refetch,
    error,
  } = useQuery({
    ...getAdminNotificationsQuery({
      pagination,
      filters: debouncedFilters,
    }),
    queryKey: [ADMIN_NOTIFICATIONS_QUERY_KEY, debouncedFilters, pagination],
  });

  useEffect(() => {
    if (isLoading || isFetching) {
      showLoading('Loading notifications...');
    } else if (!isLoading || error) {
      hideLoading();
    }
  }, [isLoading, isFetching, showLoading, hideLoading, error]);

  const markAsRead = useMutation({
    ...markAsReadMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [ADMIN_NOTIFICATIONS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
    },
  });

  const markAllAsRead = useMutation({
    ...markAllAsReadMutation(),
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'All notifications marked as read',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: [ADMIN_NOTIFICATIONS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
    },
    onError: () => {
      notifications.show({
        title: 'Error',
        message: 'Failed to mark all as read',
        color: 'red',
      });
    },
  });

  const handleRefresh = () => {
    refetch();
    notifications.show({
      title: 'Refreshed',
      message: 'Notifications list updated.',
      color: 'blue',
    });
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead.mutateAsync({ id: notificationId });
    } catch {
      notifications.show({
        title: 'Error',
        message: 'Failed to mark notification as read',
        color: 'red',
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (notificationData?.unreadCount === 0) return;
    await markAllAsRead.mutateAsync();
  };

  const handleCreateBroadcast = () => router.push('/notifications/broadcast');
  const handleManageTemplates = () => router.push('/notifications/templates');
  const handleViewAnalytics = () => router.push('/notifications/analytics');

  const notificationsList = notificationData?.notifications || [];
  const unreadCount = notificationData?.unreadCount || 0;
  const totalCount = notificationData?.pagination?.total || 0;

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <Paper withBorder p="xl">
          <Group justify="space-between" align="flex-start">
            <Box>
              <Title order={1} size="h2" mb="xs">
                Notification Management
              </Title>
              <Text c="dimmed" size="sm">
                Manage system notifications and broadcasts
              </Text>
            </Box>

            <Group gap="sm">
              <Tooltip label="Refresh">
                <ActionIcon
                  variant="light"
                  size="lg"
                  onClick={handleRefresh}
                  loading={isFetching}
                >
                  <IconRefresh size="1.2rem" />
                </ActionIcon>
              </Tooltip>

              <PermissionGuard permission={Permission.VIEW_NOTIFICATION_ANALYTICS}>
                <Button
                  variant="light"
                  leftSection={<IconChartBar size="1rem" />}
                  onClick={handleViewAnalytics}
                >
                  Analytics
                </Button>
              </PermissionGuard>

              <PermissionGuard permission={Permission.MANAGE_NOTIFICATION_TEMPLATES}>
                <Button
                  variant="light"
                  leftSection={<IconTemplate size="1rem" />}
                  onClick={handleManageTemplates}
                >
                  Templates
                </Button>
              </PermissionGuard>

              <PermissionGuard permission={Permission.SEND_BROADCAST_MESSAGES}>
                <Button
                  leftSection={<IconBroadcast size="1rem" />}
                  onClick={handleCreateBroadcast}
                >
                  Send Broadcast
                </Button>
              </PermissionGuard>
            </Group>
          </Group>
        </Paper>

        {/* Stats */}
        <Paper withBorder p="xl" shadow="sm">
          <Group gap="xl">
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                Total Notifications
              </Text>
              <Text size="xl" fw={700}>
                {totalCount.toLocaleString()}
              </Text>
            </div>
            <div>
              <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                Unread Notifications
              </Text>
              <Group gap="xs" align="center">
                <Text size="xl" fw={700}>
                  {unreadCount.toLocaleString()}
                </Text>
                {unreadCount > 0 && (
                <Badge color="red" size="sm">
                  New
                </Badge>
                )}
              </Group>
            </div>
          </Group>
        </Paper>

        {/* Notifications List */}
        <Paper withBorder p="xl" pos="relative" shadow="sm">
          <Title order={3} mb="lg">Notifications List</Title>
          <NotificationList
            notifications={notificationsList}
            pagination={{ ...pagination, total: totalCount }}
            unreadCount={unreadCount}
            isLoading={isLoading}
            isFetching={isFetching}
            filters={{
              searchTerm: filters.search || '',
              readFilter: filters.read === undefined ? null : String(filters.read),
              typeFilter: filters.type || null,
              priorityFilter: filters.priority || null,
            }}
            onFilterChange={(newFilters) => setFilters({
              search: newFilters.searchTerm,
              read: newFilters.readFilter === null ? undefined : newFilters.readFilter === 'true',
              type: newFilters.typeFilter || undefined,
              priority: newFilters.priorityFilter || undefined,
            })}
            onPageChange={(page) => setPagination({ ...pagination, page })}
            onRefresh={handleRefresh}
            onMarkAsRead={handleMarkAsRead}
            onMarkAllAsRead={handleMarkAllAsRead}
            showFilters
            showPagination
          />
        </Paper>
      </Stack>
    </Container>
  );
}

NotificationsManagementPage.auth = true;
