import { useSession } from 'next-auth/react';
import { useEffect } from 'react';
import { useRouter } from 'next/router';

/**
 * Hook to handle token expiration
 * Automatically redirects to login when token expires
 */
export const useTokenExpiration = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Check if session is unauthenticated when it should be authenticated
    // This could indicate token expiration
    if (status === 'unauthenticated' && typeof window !== 'undefined') {
      const wasAuthenticated = localStorage.getItem('wasAuthenticated');
      if (wasAuthenticated === 'true') {
        // User was previously authenticated but now isn't - likely token expired
        localStorage.removeItem('wasAuthenticated');
        router.push('/auth/login');
      }
    } else if (status === 'authenticated') {
      // Mark that user is authenticated
      localStorage.setItem('wasAuthenticated', 'true');
    }
  }, [session, status, router]);

  return {
    isTokenExpired: status === 'unauthenticated',
    isAuthenticated: status === 'authenticated',
  };
};

export default useTokenExpiration;
