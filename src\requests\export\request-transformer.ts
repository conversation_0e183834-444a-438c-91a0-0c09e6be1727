import { z } from 'zod';

// Export Users Request Schema
export const exportUsersRequestSchema = z.object({
  format: z.enum(['csv', 'excel', 'json']).default('csv'),
  filters: z.record(z.any()).optional(),
});

// Export Shipments Request Schema
export const exportShipmentsRequestSchema = z.object({
  format: z.enum(['csv', 'excel', 'json']).default('csv'),
  filters: z.record(z.any()).optional(),
});

// Legacy export request schema for backward compatibility
export const exportRequestSchema = z.object({
  format: z.enum(['csv', 'excel', 'json']).default('csv'),
  filters: z.record(z.any()).optional(),
});

// Download Export Request Schema - ID is not UUID, uses timestamp
export const downloadExportRequestSchema = z.object({
  id: z.string(),
});
