import React from 'react';
import {
  Badge, Group, ActionIcon, Text,
} from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { DataTableColumn } from '../common/DataTable';
import { Shipment, ShipmentStatus } from '../../types/admin.types';

// Helper functions for badge colors
export const getShipmentStatusColor = (status: ShipmentStatus): string => {
  switch (status) {
    case ShipmentStatus.PENDING:
      return 'orange';
    case ShipmentStatus.ASSIGNED:
      return 'blue';
    case ShipmentStatus.IN_TRANSIT:
      return 'cyan';
    case ShipmentStatus.DELIVERED:
      return 'green';
    case ShipmentStatus.CANCELLED:
      return 'red';
    case ShipmentStatus.EXPIRED:
      return 'gray';
    default:
      return 'gray';
  }
};

export const formatDate = (dateString: string): string => new Date(dateString).toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'short',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit',
});

// Define the columns for the shipments table
export const getShipmentsColumns = (
  onViewShipment: (shipment: Shipment) => void,
): DataTableColumn<Shipment>[] => [
  {
    label: 'Tracking Code',
    accessor: 'trackingCode',
    sortable: true,
    render: (shipment: Shipment) => (
      <div>
        <Text fw={500} size="sm">{shipment.trackingCode}</Text>
        <Text size="xs" c="dimmed">{shipment.description}</Text>
      </div>
    ),
  },
  {
    label: 'Status',
    accessor: 'status',
    sortable: true,
    render: (shipment: Shipment) => (
      <Badge color={getShipmentStatusColor(shipment.status)}>
        {shipment.status.replace('_', ' ')}
      </Badge>
    ),
  },
  {
    label: 'Receiver',
    accessor: 'receiverName',
    sortable: true,
    render: (shipment: Shipment) => (
      <div>
        <Text fw={500} size="sm">{shipment.receiverName}</Text>
        {shipment.receiverPhone && (
          <Text size="xs" c="dimmed">{shipment.receiverPhone}</Text>
        )}
      </div>
    ),
  },
  {
    label: 'Customer',
    accessor: 'customer',
    sortable: false,
    render: (shipment: Shipment) => (
      <div>
        {shipment.customer ? (
          <>
            <Text fw={500} size="sm">{shipment.customer.name}</Text>
            <Text size="xs" c="dimmed">{shipment.customer.email}</Text>
          </>
        ) : (
          <Text size="sm" c="dimmed">No customer</Text>
        )}
      </div>
    ),
  },
  {
    label: 'Origin AO',
    accessor: 'originAO',
    sortable: false,
    render: (shipment: Shipment) => (
      <div>
        {shipment.originAO ? (
          <Text fw={500} size="sm">{shipment.originAO.businessName}</Text>
        ) : (
          <Text size="sm" c="dimmed">Not assigned</Text>
        )}
      </div>
    ),
  },
  {
    label: 'Destination AO',
    accessor: 'destAO',
    sortable: false,
    render: (shipment: Shipment) => (
      <div>
        {shipment.destAO ? (
          <Text fw={500} size="sm">{shipment.destAO.businessName}</Text>
        ) : (
          <Text size="sm" c="dimmed">Not assigned</Text>
        )}
      </div>
    ),
  },
  {
    label: 'Created',
    accessor: 'createdAt',
    sortable: true,
    render: (shipment: Shipment) => formatDate(shipment.createdAt),
  },
  {
    label: 'Actions',
    accessor: 'actions',
    render: (shipment: Shipment) => (
      <Group gap="xs">
        <ActionIcon
          variant="light"
          color="blue"
          onClick={() => onViewShipment(shipment)}
          title="View Full Details"
        >
          <IconEye size="1rem" />
        </ActionIcon>
      </Group>
    ),
  },
];
