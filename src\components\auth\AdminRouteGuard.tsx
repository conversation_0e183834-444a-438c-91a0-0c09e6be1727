/* eslint-disable react/function-component-definition */
/* eslint-disable react/require-default-props */
/* eslint-disable complexity */
/* eslint-disable sonarjs/cognitive-complexity */
import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';
import { LoadingOverlay } from '@mantine/core';
import { useSession } from 'next-auth/react';
import { useAdminAuthContext } from '../../contexts/AdminAuthContext';
import { AdminRole } from '../../types/admin.types';

interface AdminRouteGuardProps {
  children: ReactNode;
  requiredRole?: AdminRole;
  redirectTo?: string;
}

const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password', '/auth/reset-password', '/auth/verify-otp'];

export const AdminRouteGuard = ({
  children,
  requiredRole,
  redirectTo = '/auth/login',
}: AdminRouteGuardProps) => {
  const router = useRouter();
  const { status } = useSession();
  const { admin } = useAdminAuthContext();

  useEffect(() => {
    // Only proceed when NextAuth is not loading
    if (status !== 'loading') {
      const isPublicRoute = publicRoutes.includes(router.pathname);

      // Use NextAuth status as the primary source of truth
      if (status === 'unauthenticated' && !isPublicRoute) {
        router.push(redirectTo);
        return;
      }

      // If authenticated and on public route, redirect to dashboard
      if (status === 'authenticated' && isPublicRoute) {
        router.push('/dashboard');
        return;
      }

      // Check role-based access when authenticated
      if (status === 'authenticated' && requiredRole && admin) {
        const hasRequiredRole = admin.role === requiredRole || admin.role === AdminRole.SUPER_ADMIN;

        if (!hasRequiredRole) {
          router.push('/dashboard');
        }
      }
    }
  }, [status, router, redirectTo, requiredRole, admin]);

  // Show loading while NextAuth is loading
  if (status === 'loading') {
    return <LoadingOverlay visible />;
  }

  const isPublicRoute = publicRoutes.includes(router.pathname);

  // Show loading while redirecting unauthenticated users
  if (status === 'unauthenticated' && !isPublicRoute) {
    return <LoadingOverlay visible />;
  }

  // Show loading while redirecting authenticated users from public routes
  if (status === 'authenticated' && isPublicRoute) {
    return <LoadingOverlay visible />;
  }

  // Show loading for role-based access checks
  if (status === 'authenticated' && requiredRole && admin) {
    const hasRequiredRole = admin.role === requiredRole || admin.role === AdminRole.SUPER_ADMIN;
    if (!hasRequiredRole) {
      return <LoadingOverlay visible />;
    }
  }

  return children;
};

export default AdminRouteGuard;
