/* eslint-disable max-lines */
import React, { useState } from 'react';
import Head from 'next/head';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Alert,
  Table,
  Text,
  Badge,
  Tabs,
  Modal,
  TextInput,
  Textarea,
  Stack,
} from '@mantine/core';
import {
  IconRefresh,
  IconAlertCircle,
  IconShield,
  IconLock,
  IconLockOpen,
  IconEye,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useAdminAuthContext } from '../../src/contexts/AdminAuthContext';
import { useLoading } from '../../src/contexts/LoadingContext';
import {
  getLoginAttemptsQuery,
  getSecurityEventsQuery,
  lockUserMutation,
  unlockUserMutation,
  LoginAttempt,
  SecurityEvent,
} from '../../src/requests/audit-security';

function SecurityPage() {
  const { isAuthenticated } = useAdminAuthContext();
  const { showLoading, hideLoading } = useLoading();
  const [activeTab, setActiveTab] = useState<string | null>('login-attempts');

  // Modals
  const [lockModalOpened, { open: openLockModal, close: closeLockModal }] = useDisclosure(false);
  const [unlockModalOpened, { open: openUnlockModal, close: closeUnlockModal }] = useDisclosure(false);

  // Fetch data
  const {
    data: loginAttemptsData,
    isLoading: loginAttemptsLoading,
    error: loginAttemptsError,
    refetch: refetchLoginAttempts,
  } = useQuery(getLoginAttemptsQuery({ isAuth: isAuthenticated }));

  const {
    data: securityEventsData,
    isLoading: securityEventsLoading,
    error: securityEventsError,
    refetch: refetchSecurityEvents,
  } = useQuery(getSecurityEventsQuery({ isAuth: isAuthenticated }));

  // Mutations
  const lockMutation = lockUserMutation();
  const unlockMutation = unlockUserMutation();

  // Forms
  const lockForm = useForm({
    initialValues: {
      user_id: '',
      reason: '',
    },
    validate: {
      user_id: (value) => (!value ? 'User ID is required' : null),
      reason: (value) => (!value ? 'Reason is required' : null),
    },
  });

  const unlockForm = useForm({
    initialValues: {
      user_id: '',
      reason: '',
    },
    validate: {
      user_id: (value) => (!value ? 'User ID is required' : null),
      reason: (value) => (!value ? 'Reason is required' : null),
    },
  });

  React.useEffect(() => {
    if (loginAttemptsLoading || securityEventsLoading) {
      showLoading('Loading security data...');
    } else {
      hideLoading();
    }
  }, [loginAttemptsLoading, securityEventsLoading, showLoading, hideLoading]);

  const handleLockUser = (values: { user_id: string; reason: string }) => {
    lockMutation.mutate(values, {
      onSuccess: () => {
        notifications.show({
          title: 'Success',
          message: 'User locked successfully',
          color: 'green',
        });
        closeLockModal();
        lockForm.reset();
      },
      onError: (error) => {
        notifications.show({
          title: 'Error',
          message: error?.message || 'Failed to lock user',
          color: 'red',
        });
      },
    });
  };

  const handleUnlockUser = (values: { user_id: string; reason: string }) => {
    unlockMutation.mutate(values, {
      onSuccess: () => {
        notifications.show({
          title: 'Success',
          message: 'User unlocked successfully',
          color: 'green',
        });
        closeUnlockModal();
        unlockForm.reset();
      },
      onError: (error) => {
        notifications.show({
          title: 'Error',
          message: error?.message || 'Failed to unlock user',
          color: 'red',
        });
      },
    });
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleString();

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'blue';
      case 'MEDIUM': return 'yellow';
      case 'HIGH': return 'orange';
      case 'CRITICAL': return 'red';
      default: return 'gray';
    }
  };

  if (loginAttemptsError || securityEventsError) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading security data"
          color="red"
          variant="light"
        >
          <Text mb="md">
            Failed to load security monitoring data. Please try again.
          </Text>
          <Button
            variant="outline"
            onClick={() => {
              refetchLoginAttempts();
              refetchSecurityEvents();
            }}
          >
            Retry
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>Security Monitoring - NAQALAT Admin</title>
      </Head>

      <Container size="xl" py="xl">
        <Group justify="space-between" mb="lg">
          <Title order={1}>Security Monitoring</Title>
          <Group>
            <Button
              leftSection={<IconLock size="1rem" />}
              onClick={openLockModal}
              color="red"
            >
              Lock User
            </Button>
            <Button
              leftSection={<IconLockOpen size="1rem" />}
              onClick={openUnlockModal}
              color="green"
            >
              Unlock User
            </Button>
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="outline"
              onClick={() => {
                refetchLoginAttempts();
                refetchSecurityEvents();
              }}
              loading={loginAttemptsLoading || securityEventsLoading}
            >
              Refresh
            </Button>
          </Group>
        </Group>

        <Tabs value={activeTab} onChange={setActiveTab}>
          <Tabs.List>
            <Tabs.Tab value="login-attempts" leftSection={<IconEye size="1rem" />}>
              Login Attempts
            </Tabs.Tab>
            <Tabs.Tab value="security-events" leftSection={<IconShield size="1rem" />}>
              Security Events
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="login-attempts" pt="md">
            <Paper>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Admin ID</Table.Th>
                    <Table.Th>IP Address</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Attempted At</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {(loginAttemptsData?.data?.attempts as LoginAttempt[])?.map((attempt) => (
                    <Table.Tr key={attempt.id}>
                      <Table.Td>
                        <Text size="sm">{attempt.admin_id}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{attempt.ip_address}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={attempt.successful ? 'green' : 'red'}>
                          {attempt.successful ? 'Success' : 'Failed'}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{formatDate(attempt.attempted_at)}</Text>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Paper>
          </Tabs.Panel>

          <Tabs.Panel value="security-events" pt="md">
            <Paper>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Event Type</Table.Th>
                    <Table.Th>Severity</Table.Th>
                    <Table.Th>Description</Table.Th>
                    <Table.Th>Created At</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {securityEventsData?.data?.events?.map((event: SecurityEvent) => (
                    <Table.Tr key={event.id}>
                      <Table.Td>
                        <Text size="sm">{event.event_type}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getSeverityColor(event.severity)}>
                          {event.severity}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{event.description}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{formatDate(event.created_at)}</Text>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Paper>
          </Tabs.Panel>
        </Tabs>

        {/* Lock User Modal */}
        <Modal opened={lockModalOpened} onClose={closeLockModal} title="Lock User">
          <form onSubmit={lockForm.onSubmit(handleLockUser)}>
            <Stack>
              <TextInput
                label="User ID"
                placeholder="Enter user ID to lock"
                {...lockForm.getInputProps('user_id')}
                required
              />
              <Textarea
                label="Reason"
                placeholder="Enter reason for locking the user"
                {...lockForm.getInputProps('reason')}
                required
              />
              <Group justify="flex-end">
                <Button variant="outline" onClick={closeLockModal}>
                  Cancel
                </Button>
                <Button type="submit" color="red" loading={lockMutation.isPending}>
                  Lock User
                </Button>
              </Group>
            </Stack>
          </form>
        </Modal>

        {/* Unlock User Modal */}
        <Modal opened={unlockModalOpened} onClose={closeUnlockModal} title="Unlock User">
          <form onSubmit={unlockForm.onSubmit(handleUnlockUser)}>
            <Stack>
              <TextInput
                label="User ID"
                placeholder="Enter user ID to unlock"
                {...unlockForm.getInputProps('user_id')}
                required
              />
              <Textarea
                label="Reason"
                placeholder="Enter reason for unlocking the user"
                {...unlockForm.getInputProps('reason')}
                required
              />
              <Group justify="flex-end">
                <Button variant="outline" onClick={closeUnlockModal}>
                  Cancel
                </Button>
                <Button type="submit" color="green" loading={unlockMutation.isPending}>
                  Unlock User
                </Button>
              </Group>
            </Stack>
          </form>
        </Modal>
      </Container>
    </>
  );
}

export default SecurityPage;
