// COMMENTED OUT: Unused imports when polling is disabled
// import { useEffect, useRef, useCallback } from 'react';
// import { useQuery, useQueryClient } from '@tanstack/react-query';
// import { useAdminAuthContext } from '../contexts/AdminAuthContext';
// import { notifications } from '@mantine/notifications';

import { useCallback } from 'react';
// COMMENTED OUT: Unused imports when polling is disabled
// import { QUERY_KEYS } from '../requests/cache-invalidation';
// import { getUnreadCountQuery } from '../requests/notifications';

// type Timeout = ReturnType<typeof setTimeout>;

interface UseNotificationPollingOptions {
  /**
   * Polling interval in milliseconds
   * @default 30000 (30 seconds)
   */
  interval?: number;

  /**
   * Whether to enable polling
   * @default true
   */
  enabled?: boolean;

  /**
   * Whether to show toast notifications for new notifications
   * @default true
   */
  showToasts?: boolean;

  /**
   * Callback when new notifications are detected
   */
  onNewNotifications?: (newCount: number, previousCount: number) => void;
}

export function useNotificationPolling(options: UseNotificationPollingOptions = {}) {
  // COMMENTED OUT: Disable notification polling entirely
  // const {
  //   interval = 30000,
  //   enabled = true,
  //   showToasts = true,
  //   onNewNotifications,
  // } = options;

  // const { isAuthenticated, admin } = useAdminAuthContext();
  // const queryClient = useQueryClient();
  // const previousCountRef = useRef<number>(0);
  // const intervalRef = useRef<Timeout | null>(null);
  // const isDocumentVisibleRef = useRef(true);

  // COMMENTED OUT: Disable notification polling entirely
  // All polling functionality is disabled to prevent /api/notifications/unread-count requests

  // Return mock values to maintain interface compatibility
  const unreadCount = 0;
  const refetch = useCallback(() => Promise.resolve({ data: 0 }), []);
  const isLoading = false;

  // COMMENTED OUT: Disable poll function
  // const poll = useCallback(async () => {
  //   if (!enabled || !isAuthenticated || !admin || !isDocumentVisibleRef.current) {
  //     return;
  //   }

  //   try {
  //     const result = await refetch();
  //     const newCount = result.data || 0;
  //     const previousCount = previousCountRef.current;

  //     if (newCount !== previousCount) {
  //       handleNewNotifications(newCount, previousCount);
  //       previousCountRef.current = newCount;
  //     }
  //   } catch (error) {
  //     if (process.env.NODE_ENV === 'development') {
  //       // eslint-disable-next-line no-console
  //       console.error('Notification polling error:', error);
  //     }
  //   }
  // }, [enabled, isAuthenticated, admin, refetch, handleNewNotifications]);

  // Mock poll function
  const poll = useCallback(async () => {
    // Do nothing - polling is disabled
  }, []);

  // COMMENTED OUT: Disable visibility change handling
  // useEffect(() => {
  //   const handleVisibilityChange = (): void => {
  //     isDocumentVisibleRef.current = !document.hidden;

  //     if (!document.hidden && enabled && isAuthenticated && admin) {
  //       poll().catch(() => {
  //         // Handle errors silently
  //       });
  //     }
  //   };

  //   document.addEventListener('visibilitychange', handleVisibilityChange);
  //   return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  // }, [poll, enabled, isAuthenticated, admin]);

  // COMMENTED OUT: Disable polling interval
  // useEffect(() => {
  //   if (!enabled || !isAuthenticated || !admin) {
  //     return undefined;
  //   }

  //   if (unreadCount !== undefined) {
  //     previousCountRef.current = unreadCount;
  //   }

  //   intervalRef.current = setInterval(() => {
  //     poll().catch(() => {
  //       // Handle errors silently
  //     });
  //   }, interval);

  //   return () => {
  //     if (intervalRef.current) {
  //       clearInterval(intervalRef.current);
  //     }
  //   };
  // }, [enabled, isAuthenticated, admin, poll, interval, unreadCount]);

  // COMMENTED OUT: Disable cleanup effect
  // useEffect(() => {
  //   if (!isAuthenticated || !admin) {
  //     previousCountRef.current = 0;
  //     if (intervalRef.current) {
  //       clearInterval(intervalRef.current);
  //     }
  //   }
  // }, [isAuthenticated, admin]);

  return {
    unreadCount,
    isLoading,
    refetch,
    poll,
  };
}

export default useNotificationPolling;
