import {
  Html, Head, Main, NextScript, DocumentContext, DocumentInitialProps,
} from 'next/document';
import { ColorSchemeScript, mantineHtmlProps } from '@mantine/core';

interface MyDocumentProps extends DocumentInitialProps {
  locale: string;
}

export default function Document({ locale }: MyDocumentProps) {
  return (
    <Html lang={locale} {...mantineHtmlProps}>
      <Head>
        <ColorSchemeScript />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:wght@200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}

Document.getInitialProps = async (ctx: DocumentContext): Promise<MyDocumentProps> => {
  const initialProps = await ctx.defaultGetInitialProps(ctx);
  const locale = ctx.locale || 'en';

  return {
    ...initialProps,
    locale,
  };
};
