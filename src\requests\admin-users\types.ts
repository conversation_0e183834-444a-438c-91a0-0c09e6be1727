import { z } from 'zod';
import { Pagination } from '../../types';
import { UserActivationApprovalRequest } from './request-transformer';

export type Filter = {
  search?: string;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  userType?: string;
  status?: string;
  approvalStatus?: string;
};

export interface getUsersQueryProps {
  pagination?: Pagination;
  filters?: Filter;
  sort?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: any;
}

export type UserActiveApproval = z.infer<typeof UserActivationApprovalRequest>;
