import { NextApiRequest } from 'next';
import { getToken } from 'next-auth/jwt';
import { TOKEN_SECRET } from '../data';

// This function decodes JWT from NextAuth and extracts user email and token
export const getJwt = async (req: NextApiRequest) => {
  try {
    const jwt = await getToken({
      req,
      secret: TOKEN_SECRET,
    });

    // Check if we have a valid token
    if (jwt?.accessToken && typeof jwt.accessToken === 'string') {
      return {
        token: `Bearer ${jwt.accessToken}`,
        email: (jwt?.email as string) || '',
        session: {
          user: {
            id: jwt.sub || '',
            email: jwt.email || '',
            name: jwt.name || '',
            user_type: jwt.user_type || '',
          },
        },
      };
    }

    // Check if token is expired
    if (jwt?.error === 'TokenExpired') {
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('getJwt - Token expired, user needs to re-authenticate');
      }
    } else if (process.env.NODE_ENV === 'development') {
      // Log when token is null for debugging
      // eslint-disable-next-line no-console
      console.log('getJwt - Returning null token. JWT:', jwt);
    }

    return {
      token: null,
      email: '',
      session: null,
    };
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error getting JWT token:', error);
    return {
      token: null,
      email: '',
      session: null,
    };
  }
};

// Helper function to get token from cookies for App Router
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getTokenFromCookies = async (cookieStore: any): Promise<string | undefined> => {
  try {
    if (!cookieStore) {
      return undefined;
    }

    // Handle both ReadonlyRequestCookies and RequestCookies
    const token = typeof cookieStore.get === 'function'
      ? cookieStore.get('token')?.value
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      : cookieStore.find((c: any) => c.name === 'token')?.value;

    if (token && typeof token === 'string') {
      return `Bearer ${token}`;
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error getting token from cookies:', error);
  }

  return undefined;
};
