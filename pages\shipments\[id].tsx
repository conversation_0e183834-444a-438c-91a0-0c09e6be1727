/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable max-lines */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Group,
  Text,
  Badge,
  Button,
  Stack,
  Grid,
  Card,
  Alert,
  Avatar,
  Paper,
  Box,
  ThemeIcon,
  SimpleGrid,
  Anchor,
  Timeline,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconAlertCircle,
  IconPackage,
  IconPhone,
  IconCalendar,
  IconMapPin,
  IconBuilding,
  IconActivity,
  IconExternalLink,
  IconUser,
  IconClipboard,
  IconQrcode,
} from '@tabler/icons-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useLoading } from '../../src/contexts/LoadingContext';
import { ShipmentStatus } from '../../src/types/admin.types';
import { getShipmentQuery } from '../../src/requests/shipment';
import { getShipmentStatusColor } from '../../src/components/shipments/ShipmentsTableColumns';

// Helper functions
const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit',
});

function ShipmentDetailPageContent() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { status: sessionStatus } = useSession();
  const { showLoading, hideLoading } = useLoading();
  const { id } = router.query;
  const shipmentId = Array.isArray(id) ? id[0] : id;

  // Handle authentication
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/login');
    }
  }, [sessionStatus, router]);

  const {
    data: shipmentData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    getShipmentQuery(shipmentId || '', { isAuth: sessionStatus === 'authenticated' }),
  );

  // Use global loading for this query
  useEffect(() => {
    if (isLoading && shipmentId) {
      showLoading('Loading shipment details...');
    } else {
      hideLoading();
    }
  }, [isLoading, shipmentId, showLoading, hideLoading]);

  // Force refetch when shipmentId changes
  useEffect(() => {
    if (shipmentId && router.isReady) {
      queryClient.removeQueries({ queryKey: ['shipment'] });
      queryClient.invalidateQueries({ queryKey: ['shipment', shipmentId] });
      refetch();
    }
  }, [shipmentId, router.isReady, queryClient, refetch]);

  // Don't render anything while loading
  if (isLoading) {
    return null;
  }

  if (error || !shipmentData) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading shipment data"
          color="red"
          variant="light"
          radius="md"
        >
          <Text mb="md">
            Failed to load shipment data. Please try again.
          </Text>
          <Group>
            <Button variant="outline" onClick={() => refetch()}>
              Retry
            </Button>
            <Button variant="outline" onClick={() => router.back()}>
              Go Back
            </Button>
          </Group>
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="xl" py={{ base: 'md', sm: 'xl' }} key={`${router.asPath}-${shipmentId}`}>
      {/* Enhanced Header with Gradient Background */}
      <Paper
        withBorder
        p={{ base: 'md', sm: 'xl' }}
        mb="xl"
        radius="lg"
      >
        <Stack gap="lg">
          {/* Navigation and Title */}
          <Group justify="space-between" align="flex-start" wrap="wrap" gap="md">
            <Group gap="md" wrap="wrap">
              <Button
                variant="subtle"
                leftSection={<IconArrowLeft size="1rem" />}
                onClick={() => router.back()}
                radius="md"
                size="sm"
              >
                Back to Shipments
              </Button>
            </Group>
          </Group>

          {/* Shipment Header Info */}
          <Group gap="lg" wrap="wrap">
            <Avatar size="xl" radius="lg" color="blue" variant="gradient" gradient={{ from: 'blue', to: 'indigo' }}>
              <IconPackage size="2rem" />
            </Avatar>
            <Box flex={1} style={{ minWidth: 0 }}>
              <Title order={2} mb="xs" style={{ wordBreak: 'break-word' }}>
                {shipmentData.trackingCode}
              </Title>
              <Group gap="lg" wrap="wrap">
                <Group gap="xs">
                  <ThemeIcon size="sm" variant="light" color="blue">
                    <IconUser size="0.8rem" />
                  </ThemeIcon>
                  <Text c="dimmed">
                    To:
                    {' '}
                    {shipmentData.receiverName}
                  </Text>
                </Group>
                {shipmentData.receiverPhone && (
                  <Group gap="xs">
                    <ThemeIcon size="sm" variant="light" color="blue">
                      <IconPhone size="0.8rem" />
                    </ThemeIcon>
                    <Text c="dimmed">{shipmentData.receiverPhone}</Text>
                  </Group>
                )}
              </Group>
            </Box>
          </Group>
        </Stack>
      </Paper>

      <Grid gutter="xl">
        {/* Main Content */}
        <Grid.Col span={{ base: 12, lg: 8 }}>
          <Stack gap="xl">
            {/* Status Overview Cards */}
            <SimpleGrid cols={{ base: 1, xs: 2, sm: 3 }} spacing="md">
              <Card withBorder shadow="sm" p="lg" radius="lg">
                <Group gap="md">
                  <ThemeIcon size="xl" radius="lg" variant="light" color={getShipmentStatusColor(shipmentData.status as ShipmentStatus)}>
                    <IconPackage size="1.5rem" />
                  </ThemeIcon>
                  <Box flex={1}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700} mb={4}>
                      Status
                    </Text>
                    <Badge color={getShipmentStatusColor(shipmentData.status as ShipmentStatus)} size="lg" radius="md">
                      {shipmentData.status.replace('_', ' ')}
                    </Badge>
                  </Box>
                </Group>
              </Card>

              <Card withBorder shadow="sm" p="lg" radius="lg">
                <Group gap="md">
                  <ThemeIcon size="xl" radius="lg" variant="light" color="blue">
                    <IconCalendar size="1.5rem" />
                  </ThemeIcon>
                  <Box flex={1}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700} mb={4}>
                      Created
                    </Text>
                    <Text fw={500} size="sm">
                      {formatDate(shipmentData.createdAt)}
                    </Text>
                  </Box>
                </Group>
              </Card>

              <Card withBorder shadow="sm" p="lg" radius="lg">
                <Group gap="md">
                  <ThemeIcon size="xl" radius="lg" variant="light" color="green">
                    <IconActivity size="1.5rem" />
                  </ThemeIcon>
                  <Box flex={1}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700} mb={4}>
                      Last Updated
                    </Text>
                    <Text fw={500} size="sm">
                      {formatDate(shipmentData.updatedAt)}
                    </Text>
                  </Box>
                </Group>
              </Card>
            </SimpleGrid>

            {/* Shipment Details */}
            <Card withBorder shadow="sm" p="xl" radius="lg">
              <Group mb="xl">
                <ThemeIcon size="lg" radius="md" variant="light" color="blue">
                  <IconClipboard size="1.2rem" />
                </ThemeIcon>
                <Title order={3}>Shipment Details</Title>
              </Group>

              <SimpleGrid cols={{ base: 1, xs: 2, md: 3 }} spacing="xl">
                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Description</Text>
                  <Text fw={500}>{shipmentData.description}</Text>
                </Box>

                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Receiver Name</Text>
                  <Text fw={500}>{shipmentData.receiverName}</Text>
                </Box>

                {shipmentData.receiverPhone && (
                  <Box>
                    <Text size="sm" fw={600} c="dimmed" mb="xs">Receiver Phone</Text>
                    <Text fw={500}>{shipmentData.receiverPhone}</Text>
                  </Box>
                )}

                {shipmentData.receiverAddress && (
                  <Box>
                    <Text size="sm" fw={600} c="dimmed" mb="xs">Receiver Address</Text>
                    <Text fw={500}>{shipmentData.receiverAddress}</Text>
                  </Box>
                )}

                {shipmentData.weight && (
                  <Box>
                    <Text size="sm" fw={600} c="dimmed" mb="xs">Weight</Text>
                    <Text fw={500}>
                      {shipmentData.weight}
                      {' '}
                      kg
                    </Text>
                  </Box>
                )}

                {shipmentData.dimensions && (
                  <Box>
                    <Text size="sm" fw={600} c="dimmed" mb="xs">Dimensions</Text>
                    <Text fw={500}>{shipmentData.dimensions}</Text>
                  </Box>
                )}
              </SimpleGrid>

              {shipmentData.specialInstructions && (
                <Box mt="xl">
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Special Instructions</Text>
                  <Paper withBorder p="md" radius="md" bg="var(--mantine-color-blue-0)">
                    <Text>{shipmentData.specialInstructions}</Text>
                  </Paper>
                </Box>
              )}
            </Card>
          </Stack>
        </Grid.Col>

        {/* Sidebar */}
        <Grid.Col span={{ base: 12, lg: 4 }}>
          <Stack gap="xl">
            {/* Audit Logs */}
            {shipmentData.auditLogs && shipmentData.auditLogs.length > 0 && (
              <Card withBorder shadow="sm" p="xl" radius="lg">
                <Group mb="xl">
                  <ThemeIcon size="lg" radius="md" variant="light" color="indigo">
                    <IconActivity size="1.2rem" />
                  </ThemeIcon>
                  <Title order={3}>Activity Log</Title>
                </Group>

                <Timeline active={shipmentData.auditLogs.length} bulletSize={24} lineWidth={2}>
                  {shipmentData.auditLogs.map((log: any) => (
                    <Timeline.Item
                      key={log.id}
                      bullet={<IconActivity size="0.8rem" />}
                      title={log.action}
                    >
                      <Text c="dimmed" size="sm">
                        {log.details}
                      </Text>
                      <Text size="xs" c="dimmed" mt="xs">
                        {formatDate(log.createdAt)}
                        {' '}
                        by
                        {' '}
                        {log.user?.name || log.admin?.name || 'System'}
                      </Text>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            )}
            {/* Customer Information */}
            {shipmentData.customer && (
              <Card withBorder shadow="sm" p="xl" radius="lg">
                <Group mb="xl">
                  <ThemeIcon size="lg" radius="md" variant="light" color="green">
                    <IconUser size="1.2rem" />
                  </ThemeIcon>
                  <Title order={3}>Customer</Title>
                </Group>

                <Stack gap="lg">
                  <Box>
                    <Text size="sm" fw={600} c="dimmed" mb="xs">Name</Text>
                    <Text fw={500}>{shipmentData.customer.name}</Text>
                  </Box>
                  <Box>
                    <Text size="sm" fw={600} c="dimmed" mb="xs">Email</Text>
                    <Text fw={500}>{shipmentData.customer.email}</Text>
                  </Box>
                  {shipmentData.customer.phone && (
                    <Box>
                      <Text size="sm" fw={600} c="dimmed" mb="xs">Phone</Text>
                      <Text fw={500}>{shipmentData.customer.phone}</Text>
                    </Box>
                  )}
                </Stack>
              </Card>
            )}

            {/* Access Operators */}
            <Card withBorder shadow="sm" p="xl" radius="lg">
              <Group mb="xl">
                <ThemeIcon size="lg" radius="md" variant="light" color="orange">
                  <IconBuilding size="1.2rem" />
                </ThemeIcon>
                <Title order={3}>Access Operators</Title>
              </Group>

              <Stack gap="lg">
                {/* Origin AO */}
                <Box>
                  <Text size="sm" fw={600} c="green" mb="xs">
                    <Group gap="xs">
                      <IconMapPin size="1rem" />
                      Origin Access Operator
                    </Group>
                  </Text>
                  {shipmentData.originAO ? (
                    <Paper withBorder p="md" radius="md" bg="var(--mantine-color-green-0)">
                      <Text fw={500} mb="xs">
                        {shipmentData.originAO.businessName}
                      </Text>
                      {shipmentData.originAO.address && (
                        <Text size="sm" c="dimmed" mb="xs">
                          {shipmentData.originAO.address}
                        </Text>
                      )}
                      {shipmentData.originAO.geoLatitude && shipmentData.originAO.geoLongitude && (
                        <Anchor
                          href={`https://maps.google.com/?q=${shipmentData.originAO.geoLatitude},${shipmentData.originAO.geoLongitude}`}
                          target="_blank"
                          size="xs"
                        >
                          <Group gap="xs">
                            View on Maps
                            <IconExternalLink size="0.8rem" />
                          </Group>
                        </Anchor>
                      )}
                    </Paper>
                  ) : (
                    <Text size="sm" c="dimmed" fs="italic">Not assigned</Text>
                  )}
                </Box>

                {/* Destination AO */}
                <Box>
                  <Text size="sm" fw={600} c="blue" mb="xs">
                    <Group gap="xs">
                      <IconMapPin size="1rem" />
                      Destination Access Operator
                    </Group>
                  </Text>
                  {shipmentData.destAO ? (
                    <Paper withBorder p="md" radius="md" bg="var(--mantine-color-blue-0)">
                      <Text fw={500} mb="xs">
                        {shipmentData.destAO.businessName}
                      </Text>
                      {shipmentData.destAO.address && (
                        <Text size="sm" c="dimmed" mb="xs">
                          {shipmentData.destAO.address}
                        </Text>
                      )}
                      {shipmentData.destAO.geoLatitude && shipmentData.destAO.geoLongitude && (
                        <Anchor
                          href={`https://maps.google.com/?q=${shipmentData.destAO.geoLatitude},${shipmentData.destAO.geoLongitude}`}
                          target="_blank"
                          size="xs"
                        >
                          <Group gap="xs">
                            View on Maps
                            <IconExternalLink size="0.8rem" />
                          </Group>
                        </Anchor>
                      )}
                    </Paper>
                  ) : (
                    <Text size="sm" c="dimmed" fs="italic">Not assigned</Text>
                  )}
                </Box>
              </Stack>
            </Card>

            {/* QR Labels */}
            {shipmentData.qrLabels && shipmentData.qrLabels.length > 0 && (
              <Card withBorder shadow="sm" p="xl" radius="lg">
                <Group mb="xl">
                  <ThemeIcon size="lg" radius="md" variant="light" color="violet">
                    <IconQrcode size="1.2rem" />
                  </ThemeIcon>
                  <Title order={3}>QR Labels</Title>
                </Group>

                <Stack gap="md">
                  {shipmentData.qrLabels.map((qr: any) => (
                    <Paper key={qr.id} withBorder p="md" radius="md">
                      <Group justify="space-between" align="flex-start">
                        <Box>
                          <Text fw={500} mb="xs">
                            QR Code:
                            {' '}
                            {qr.qrCode}
                          </Text>
                          <Badge color={qr.status === 'ACTIVE' ? 'green' : 'gray'} size="sm">
                            {qr.status}
                          </Badge>
                        </Box>
                        <Text size="xs" c="dimmed">
                          {formatDate(qr.createdAt)}
                        </Text>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            )}
          </Stack>
        </Grid.Col>
      </Grid>
    </Container>
  );
}

// Wrapper component that forces re-mounting when shipmentId changes
export default function ShipmentDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const shipmentId = Array.isArray(id) ? id[0] : id;

  // Don't render anything until router is ready and we have a shipmentId
  if (!router.isReady || !shipmentId) {
    return null;
  }

  // Use shipmentId as key to force complete component remount when it changes
  return <ShipmentDetailPageContent key={shipmentId} />;
}
