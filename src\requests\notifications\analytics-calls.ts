import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QUERY_KEYS } from '../cache-invalidation';

// Analytics API endpoints (extend existing API_ENDPOINT)
const ANALYTICS_ENDPOINTS = {
  overview: '/notifications/analytics/overview',
  byType: '/notifications/analytics/by-type',
  trends: '/notifications/analytics/trends',
  export: '/notifications/analytics/export',
};

// Analytics request/response types
export interface GetAnalyticsOverviewRequest {
  fromDate?: string;
  toDate?: string;
  type?: string;
}

export interface AnalyticsOverview {
  totalSent: number;
  totalDelivered: number;
  totalOpened: number;
  totalFailed: number;
  deliveryRate: number;
  openRate: number;
  failureRate: number;
}

export interface AnalyticsTrends {
  sentTrend: number;
  deliveredTrend: number;
  openedTrend: number;
  failedTrend: number;
}

export interface AnalyticsByType {
  type: string;
  sent: number;
  delivered: number;
  opened: number;
  failed: number;
}

export interface GetAnalyticsOverviewResponse {
  success: boolean;
  data: {
    overview: AnalyticsOverview;
    trends: AnalyticsTrends;
    byType: AnalyticsByType[];
  };
}

export interface ExportAnalyticsRequest {
  fromDate?: string;
  toDate?: string;
  type?: string;
  format: 'csv' | 'excel';
}

// Use centralized query keys
const queryKeys = QUERY_KEYS.notifications;

/**
 * @description Get notification analytics overview
 * @param params - Date range and filter parameters
 * @returns analytics overview data
 */
const getAnalyticsOverviewRequest = (params: GetAnalyticsOverviewRequest = {}) => {
  const urlParams = new URLSearchParams();

  if (params.fromDate) urlParams.append('fromDate', params.fromDate);
  if (params.toDate) urlParams.append('toDate', params.toDate);
  if (params.type) urlParams.append('type', params.type);

  const queryString = urlParams.toString();
  const url = queryString ? `${ANALYTICS_ENDPOINTS.overview}?${queryString}` : ANALYTICS_ENDPOINTS.overview;

  return CLIENT_API.get(url)
    .then((res) => res?.data as GetAnalyticsOverviewResponse)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Export analytics data
 * @param params - Export parameters
 * @returns file download response
 */
const exportAnalyticsRequest = (params: ExportAnalyticsRequest) => {
  const urlParams = new URLSearchParams();

  if (params.fromDate) urlParams.append('fromDate', params.fromDate);
  if (params.toDate) urlParams.append('toDate', params.toDate);
  if (params.type) urlParams.append('type', params.type);
  urlParams.append('format', params.format);

  const queryString = urlParams.toString();
  const url = queryString ? `${ANALYTICS_ENDPOINTS.export}?${queryString}` : ANALYTICS_ENDPOINTS.export;

  return CLIENT_API.get(url, { responseType: 'blob' })
    .then((res) => {
      // Create download link
      const blob = new Blob([res.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `notification-analytics-${new Date().toISOString().split('T')[0]}.${params.format === 'excel' ? 'xlsx' : 'csv'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      return { success: true };
    })
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// React Query configurations for analytics operations
export const getAnalyticsOverviewQuery = (params: GetAnalyticsOverviewRequest = {}) => ({
  queryKey: [queryKeys.analytics, 'overview', params],
  queryFn: () => getAnalyticsOverviewRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 60000, // 1 minute - analytics can be refreshed more frequently
});

// Mutation functions for analytics operations
export const exportAnalyticsMutation = () => ({
  mutationKey: [queryKeys.analytics, 'export'],
  mutationFn: (params: ExportAnalyticsRequest) => exportAnalyticsRequest(params),
});

// Mock data generator for development/testing
export const generateMockAnalyticsData = (params: GetAnalyticsOverviewRequest = {}): GetAnalyticsOverviewResponse => {
  // Generate realistic mock data based on parameters
  const baseData = {
    totalSent: 15420,
    totalDelivered: 14890,
    totalOpened: 8934,
    totalFailed: 530,
  };

  // Apply some variation based on date range or type filter
  const variation = params.type ? 0.8 : 1.0;
  const adjustedData = {
    totalSent: Math.floor(baseData.totalSent * variation),
    totalDelivered: Math.floor(baseData.totalDelivered * variation),
    totalOpened: Math.floor(baseData.totalOpened * variation),
    totalFailed: Math.floor(baseData.totalFailed * variation),
  };

  return {
    success: true,
    data: {
      overview: {
        ...adjustedData,
        deliveryRate: (adjustedData.totalDelivered / adjustedData.totalSent) * 100,
        openRate: (adjustedData.totalOpened / adjustedData.totalDelivered) * 100,
        failureRate: (adjustedData.totalFailed / adjustedData.totalSent) * 100,
      },
      trends: {
        sentTrend: 12.5,
        deliveredTrend: 8.3,
        openedTrend: -2.1,
        failedTrend: -15.2,
      },
      byType: [
        {
          type: 'SHIPMENT_DELIVERED', sent: 5420, delivered: 5380, opened: 3240, failed: 40,
        },
        {
          type: 'SYSTEM_ANNOUNCEMENT', sent: 3200, delivered: 3150, opened: 2100, failed: 50,
        },
        {
          type: 'BROADCAST_MESSAGE', sent: 2800, delivered: 2750, opened: 1650, failed: 50,
        },
        {
          type: 'DELIVERY_REMINDER', sent: 2000, delivered: 1950, opened: 1170, failed: 50,
        },
        {
          type: 'MAINTENANCE_ALERT', sent: 2000, delivered: 1660, opened: 774, failed: 340,
        },
      ].map((item) => ({
        ...item,
        sent: Math.floor(item.sent * variation),
        delivered: Math.floor(item.delivered * variation),
        opened: Math.floor(item.opened * variation),
        failed: Math.floor(item.failed * variation),
      })),
    },
  };
};

// Custom hook for analytics data with real API integration
export const useAnalyticsData = (params: GetAnalyticsOverviewRequest = {}) => {
  // Check if we should use mock data (can be controlled via environment variable)
  const USE_MOCK_DATA = process.env.NEXT_PUBLIC_USE_MOCK_ANALYTICS === 'true';

  // Use React Query for real API calls
  const queryResult = useQuery({
    ...getAnalyticsOverviewQuery(params),
    enabled: !USE_MOCK_DATA,
  });

  // Mock data state for development
  const [mockData, setMockData] = React.useState<GetAnalyticsOverviewResponse | null>(null);
  const [mockLoading, setMockLoading] = React.useState(USE_MOCK_DATA);

  React.useEffect(() => {
    if (USE_MOCK_DATA) {
      setMockLoading(true);
      const timer = setTimeout(() => {
        setMockData(generateMockAnalyticsData(params));
        setMockLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [params, USE_MOCK_DATA]);

  const mockRefetch = React.useCallback(() => {
    if (USE_MOCK_DATA) {
      setMockLoading(true);
      setTimeout(() => {
        setMockData(generateMockAnalyticsData(params));
        setMockLoading(false);
      }, 300);
    }
    return Promise.resolve();
  }, [params, USE_MOCK_DATA]);

  // Return appropriate data based on mode
  if (USE_MOCK_DATA) {
    return {
      data: mockData,
      isLoading: mockLoading,
      error: null,
      refetch: mockRefetch,
    };
  }

  return {
    data: queryResult.data,
    isLoading: queryResult.isLoading,
    error: queryResult.error,
    refetch: queryResult.refetch,
  };
};

// Export request functions for direct use
export {
  getAnalyticsOverviewRequest,
  exportAnalyticsRequest,
};
