/* eslint-disable no-underscore-dangle */
import * as z from 'zod';
import { ApiResponseSchema, PaginationSchema } from '../common';

export const UserSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string(),
  user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']),
  status: z.enum(['ACTIVE', 'PENDING', 'SUSPENDED']),
  approval_status: z.enum(['APPROVED', 'PENDING', 'REJECTED']).nullable(),
  email_verified: z.boolean(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  accessOperator: z.object({
    id: z.string().uuid(),
    business_name: z.string(),
    address: z.string(),
    geo_latitude: z.number().nullable(),
    geo_longitude: z.number().nullable(),
    approved: z.boolean(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    updated_by: z.string().uuid().nullable(),
  }).nullable(),
  carOperator: z.object({
    id: z.string().uuid(),
    license_number: z.string(),
    vehicle_info: z.string().nullable(),
    approved: z.boolean(),
    pickup_access_point_id: z.string().uuid().nullable(),
    dropoff_access_point_id: z.string().uuid().nullable(),
    created_at: z.string().datetime(),
    updated_at: z.string().datetime(),
    updated_by: z.string().uuid().nullable(),
  }).nullable(),
  _count: z.object({
    shipments: z.number(),
    auditLogs: z.number(),
    emailVerifications: z.number(),
    userVerifications: z.number(),
    loginAttempts: z.number(),
    passwordResets: z.number(),
    securityEvents: z.number(),
  }),
});

export const UserDetailsResponse = ApiResponseSchema(UserSchema);

export const userDetailsApiResponse = (
  item: z.infer<typeof UserSchema>,
) => ({
  id: item.id,
  name: item.name,
  email: item.email,
  phone: item.phone,
  userType: item.user_type,
  status: item.status,
  approvalStatus: item.approval_status,
  emailVerified: item.email_verified,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  accessOperator: item.accessOperator
    ? {
      id: item.accessOperator.id,
      businessName: item.accessOperator.business_name,
      address: item.accessOperator.address,
      geoLatitude: item.accessOperator.geo_latitude,
      geoLongitude: item.accessOperator.geo_longitude,
      approved: item.accessOperator.approved,
      createdAt: item.accessOperator.created_at,
      updatedAt: item.accessOperator.updated_at,
      updatedBy: item.accessOperator.updated_by,
    }
    : null,
  carOperator: item.carOperator
    ? {
      id: item.carOperator.id,
      licenseNumber: item.carOperator.license_number,
      vehicleInfo: item.carOperator.vehicle_info,
      approved: item.carOperator.approved,
      pickupAccessPointId: item.carOperator.pickup_access_point_id,
      dropoffAccessPointId: item.carOperator.dropoff_access_point_id,
      createdAt: item.carOperator.created_at,
      updatedAt: item.carOperator.updated_at,
      updatedBy: item.carOperator.updated_by,
    }
    : null,
  count: {
    shipments: item._count?.shipments,
    auditLogs: item._count?.auditLogs,
    emailVerifications: item._count?.emailVerifications,
    userVerifications: item._count?.userVerifications,
    loginAttempts: item._count?.loginAttempts,
    passwordResets: item._count?.passwordResets,
    securityEvents: item._count?.securityEvents,
  },
});

export const usersListResponseSchema = ApiResponseSchema(
  z.object({
    users: z.array(UserSchema),
    pagination: PaginationSchema,
  }),
);

export const UserActivationApprovalResponse = ApiResponseSchema(
  z.object({
    activation: z.object({
      changed: z.boolean(),
      active: z.boolean(),
    }),
    approval: z.object({
      changed: z.boolean(),
      applicable: z.boolean(),
      approved: z.boolean().optional(),
    }),
    user: UserSchema.optional(),
  }),
);

export const userActivationApprovalApiResponse = (
  data: z.infer<typeof UserActivationApprovalResponse>,
) => ({
  success: data.success,
  message: data.message,
  data: {
    activation: data.data.activation,
    approval: data.data.approval,
    user: data.data.user,
  },
});
