import { NextApiRequest, NextApiResponse } from 'next';
import { apiMethods, HTTP_CODE } from '../../../src/data';
import { returnAdminNotificationListParams } from '../../../src/requests/admin-notifications';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { AdminNotificationsListResponse } from '../../../src/requests/admin-notifications/response-transformer';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== apiMethods.GET) {
    return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    const { token } = await getJwt(req);
    const params = returnAdminNotificationListParams(req);

    const { data } = await BACKEND_API(req).get('/notifications/my', {
      headers: { authorization: token },
      params,
    });

    return createApiResponse(
      res,
      AdminNotificationsListResponse,
      {
        success: data.success,
        message: data.message || 'Notifications retrieved successfully.',
        data: data.data,
      },
    );
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default handler;
