/* Global CSS for PinInput components - LTR only */

/* LTR direction for PinInput containers */
.ltr-pin-input-container {
  direction: ltr !important;
  unicode-bidi: embed !important;
  writing-mode: horizontal-tb !important;
}

/* LTR direction for all PinInput inputs */
.ltr-pin-input-container input,
.ltr-pin-input-container input[data-pin-input],
.ltr-pin-input-container .mantine-PinInput-input,
.ltr-pin-input-container input[type="text"] {
  direction: ltr !important;
  text-align: center !important;
  unicode-bidi: embed !important;
  font-family: monospace, sans-serif !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  writing-mode: horizontal-tb !important;
}
