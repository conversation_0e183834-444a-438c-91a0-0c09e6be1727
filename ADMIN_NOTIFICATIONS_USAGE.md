# Admin Notifications System - Usage Guide

This document provides examples of how to use the newly implemented admin notifications system.

## Overview

The admin notifications system follows the same pattern as the existing `admin-users` structure and provides:

- **Frontend Request Structure**: `src/requests/admin-notifications/`
- **Backend API Endpoints**: `pages/api/admin/notifications/`
- **Full TypeScript Support**: Type-safe requests and responses
- **React Query Integration**: Optimized caching and state management

## Frontend Usage

### 1. Import the Required Functions

```typescript
import {
  getAdminNotificationsQuery,
  getUnreadCountQuery,
  markAsReadMutation,
  markAllAsReadMutation,
  deleteNotificationMutation,
  AdminNotificationType,
  AdminNotificationPriority,
} from '../src/requests/admin-notifications';
```

### 2. Get Admin Notifications with Filtering

```typescript
import { useQuery } from '@tanstack/react-query';

const AdminNotificationsPage = () => {
  // Get notifications with filters
  const { data: notificationsData, isLoading, error } = useQuery(
    getAdminNotificationsQuery({
      filters: {
        search: 'user registration',
        type: AdminNotificationType.USER_REGISTERED,
        priority: AdminNotificationPriority.HIGH,
        read: false, // Only unread notifications
        fromDate: '2024-01-01T00:00:00Z',
        toDate: '2024-12-31T23:59:59Z',
      },
      pagination: {
        page: 1,
        limit: 20,
      },
      sort: 'createdAt:desc',
    })
  );

  if (isLoading) return <div>Loading notifications...</div>;
  if (error) return <div>Error loading notifications</div>;

  return (
    <div>
      <h1>Admin Notifications ({notificationsData?.data?.unreadCount} unread)</h1>
      {notificationsData?.data?.notifications?.map((notification) => (
        <div key={notification.id} className={`notification ${!notification.read ? 'unread' : ''}`}>
          <h3>{notification.title}</h3>
          <p>{notification.message}</p>
          <span>Priority: {notification.priority}</span>
          <span>Type: {notification.notificationType}</span>
          <span>Created: {new Date(notification.createdAt).toLocaleString()}</span>
        </div>
      ))}
    </div>
  );
};
```

### 3. Get Unread Count for Badge/Header

```typescript
import { useQuery } from '@tanstack/react-query';

const NotificationBadge = () => {
  const { data: unreadData } = useQuery(getUnreadCountQuery());
  
  const unreadCount = unreadData?.data?.unreadCount || 0;
  
  return (
    <div className="notification-badge">
      <BellIcon />
      {unreadCount > 0 && (
        <span className="badge">{unreadCount}</span>
      )}
    </div>
  );
};
```

### 4. Mark Notification as Read

```typescript
import { useMutation, useQueryClient } from '@tanstack/react-query';

const NotificationItem = ({ notification }) => {
  const queryClient = useQueryClient();
  
  const markAsReadMut = useMutation({
    ...markAsReadMutation(),
    onSuccess: () => {
      // Invalidate and refetch notifications
      queryClient.invalidateQueries(['adminNotifications']);
      queryClient.invalidateQueries(['unreadCount']);
    },
  });

  const handleMarkAsRead = () => {
    if (!notification.read) {
      markAsReadMut.mutate({ id: notification.id });
    }
  };

  return (
    <div className={`notification ${!notification.read ? 'unread' : ''}`}>
      <h3>{notification.title}</h3>
      <p>{notification.message}</p>
      {!notification.read && (
        <button onClick={handleMarkAsRead} disabled={markAsReadMut.isLoading}>
          Mark as Read
        </button>
      )}
    </div>
  );
};
```

### 5. Mark All Notifications as Read

```typescript
import { useMutation, useQueryClient } from '@tanstack/react-query';

const NotificationActions = () => {
  const queryClient = useQueryClient();
  
  const markAllAsReadMut = useMutation({
    ...markAllAsReadMutation(),
    onSuccess: (data) => {
      console.log(`Marked ${data.data.updatedCount} notifications as read`);
      queryClient.invalidateQueries(['adminNotifications']);
      queryClient.invalidateQueries(['unreadCount']);
    },
  });

  return (
    <button 
      onClick={() => markAllAsReadMut.mutate()}
      disabled={markAllAsReadMut.isLoading}
    >
      Mark All as Read
    </button>
  );
};
```

### 6. Delete Notification

```typescript
import { useMutation, useQueryClient } from '@tanstack/react-query';

const NotificationItem = ({ notification }) => {
  const queryClient = useQueryClient();
  
  const deleteNotificationMut = useMutation({
    ...deleteNotificationMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries(['adminNotifications']);
      queryClient.invalidateQueries(['unreadCount']);
    },
  });

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this notification?')) {
      deleteNotificationMut.mutate({ id: notification.id });
    }
  };

  return (
    <div className="notification">
      <h3>{notification.title}</h3>
      <p>{notification.message}</p>
      <button onClick={handleDelete} disabled={deleteNotificationMut.isLoading}>
        Delete
      </button>
    </div>
  );
};
```

## API Endpoints

The following API endpoints are available:

### 1. Get Admin's Notifications
```
GET /api/admin/notifications/my
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `type` (optional): Filter by notification type
- `priority` (optional): Filter by priority (LOW, NORMAL, HIGH, URGENT)
- `read` (optional): Filter by read status (true/false)
- `search` (optional): Search in title and message
- `fromDate` (optional): Filter from date (ISO 8601)
- `toDate` (optional): Filter to date (ISO 8601)
- `shipmentId` (optional): Filter by shipment ID

### 2. Get Unread Count
```
GET /api/admin/notifications/unread-count
```

### 3. Mark Notification as Read
```
PUT /api/admin/notifications/:id/read
```

### 4. Mark All Notifications as Read
```
PUT /api/admin/notifications/mark-all-read
```

### 5. Delete Notification
```
DELETE /api/admin/notifications/:id
```

## Notification Types

The system supports the following notification types:

### User Management Events
- `USER_REGISTERED`: New user registration
- `USER_EMAIL_VERIFIED`: User email verification completed
- `OPERATOR_NEEDS_APPROVAL`: Access/Car Operator needs admin approval
- `USER_STATUS_CHANGED`: User status changed by admin

### Security Events
- `SECURITY_ALERT`: Failed login attempts, suspicious activity
- `ADMIN_CREATED`: New admin account created

### System Events
- `SHIPMENT_CREATED`: New shipment created
- `SYSTEM_ERROR`: System errors requiring admin attention
- `BULK_OPERATION_COMPLETED`: Bulk operations completed
- `SYSTEM_MAINTENANCE`: System maintenance notifications

## Priority Levels

- `LOW`: Informational notifications
- `NORMAL`: Standard notifications
- `HIGH`: Important notifications requiring attention
- `URGENT`: Critical notifications requiring immediate action

## Testing

Run the tests with:

```bash
yarn test src/requests/admin-notifications/__tests__/
```

## Notes

- All API endpoints require admin authentication
- The system follows the same patterns as the existing `admin-users` structure
- Response transformers ensure consistent data format between frontend and backend
- React Query integration provides automatic caching and background updates
- TypeScript ensures type safety throughout the system
