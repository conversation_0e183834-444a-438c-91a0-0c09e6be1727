/* eslint-disable no-underscore-dangle */
import * as z from 'zod';
import { ApiResponseSchema, PaginationSchema } from '../common';
import { AdminNotificationType, AdminNotificationPriority } from './types';

// Admin notification schema (based on specification)
export const AdminNotificationSchema = z.object({
  id: z.string().uuid(),
  admin_id: z.string().uuid(),
  shipment_id: z.string().uuid().nullable(),
  notification_type: z.nativeEnum(AdminNotificationType),
  title: z.string(),
  message: z.string(),
  priority: z.nativeEnum(AdminNotificationPriority),
  read: z.boolean(),
  read_at: z.string().datetime().nullable(),
  metadata: z.record(z.any()).nullable(),
  expires_at: z.string().datetime().nullable(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  shipment: z.object({
    id: z.string().uuid(),
    tracking_number: z.string(),
    status: z.string(),
  }).nullable().optional(),
});

// Response schema for getting admin notifications
export const AdminNotificationsListResponse = ApiResponseSchema(
  z.object({
    notifications: z.array(AdminNotificationSchema),
    pagination: PaginationSchema,
    unreadCount: z.number(),
  }),
);

// Response schema for unread count
export const UnreadCountResponse = ApiResponseSchema(
  z.object({
    unreadCount: z.number(),
  }),
);

// Response schema for mark as read
export const MarkAsReadResponse = ApiResponseSchema(
  z.object({
    notification: z.object({
      id: z.string().uuid(),
      read: z.boolean(),
      read_at: z.string().datetime(),
    }),
  }),
);

// Response schema for mark all as read
export const MarkAllAsReadResponse = ApiResponseSchema(
  z.object({
    updatedCount: z.number(),
  }),
);

// Response schema for delete notification
export const DeleteNotificationResponse = ApiResponseSchema(z.object({}));

// Transform admin notification for frontend
export const transformAdminNotification = (
  item: z.infer<typeof AdminNotificationSchema>,
) => ({
  id: item.id,
  adminId: item.admin_id,
  shipmentId: item.shipment_id,
  notificationType: item.notification_type,
  title: item.title,
  message: item.message,
  priority: item.priority,
  read: item.read,
  readAt: item.read_at,
  metadata: item.metadata,
  expiresAt: item.expires_at,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  shipment: item.shipment ? {
    id: item.shipment.id,
    trackingNumber: item.shipment.tracking_number,
    status: item.shipment.status,
  } : null,
});

// Transform notifications list response
export const transformAdminNotificationsListResponse = (
  data: z.infer<typeof AdminNotificationsListResponse>,
) => ({
  success: data.success,
  message: data.message,
  data: {
    notifications: data.data.notifications.map(transformAdminNotification),
    pagination: data.data.pagination,
    unreadCount: data.data.unreadCount,
  },
});

// Transform unread count response
export const transformUnreadCountResponse = (
  data: z.infer<typeof UnreadCountResponse>,
) => ({
  success: data.success,
  message: data.message,
  data: {
    unreadCount: data.data.unreadCount,
  },
});

// Transform mark as read response
export const transformMarkAsReadResponse = (
  data: z.infer<typeof MarkAsReadResponse>,
) => ({
  success: data.success,
  message: data.message,
  data: {
    notification: data.data.notification,
  },
});

// Transform mark all as read response
export const transformMarkAllAsReadResponse = (
  data: z.infer<typeof MarkAllAsReadResponse>,
) => ({
  success: data.success,
  message: data.message,
  data: {
    updatedCount: data.data.updatedCount,
  },
});

// Transform delete response
export const transformDeleteResponse = (
  data: z.infer<typeof DeleteNotificationResponse>,
) => ({
  success: data.success,
  message: data.message,
});
