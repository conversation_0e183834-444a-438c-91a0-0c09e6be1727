import {
  userReportsResponseSchema,
  shipmentReportsResponseSchema,
  systemReportsResponseSchema,
  exportUsersResponseSchema,
  exportShipmentsResponseSchema,
  exportDownloadResponseSchema,
  UserReportsResponseType,
  ShipmentReportsResponseType,
  SystemReportsResponseType,
  ExportUsersResponseType,
  ExportShipmentsResponseType,
  ExportDownloadResponseType,
} from './types';

// Response transformers - convert backend data to frontend format
export const transformUserReportsResponse = (data: unknown): UserReportsResponseType => {
  const validated = userReportsResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      summary: {
        total_users: validated.data.summary.total_users,
        by_type: validated.data.summary.by_type.map((item) => ({
          type: item.type,
          count: item.count,
        })),
        by_status: validated.data.summary.by_status.map((item) => ({
          status: item.status,
          count: item.count,
        })),
      },
      recent_registrations: validated.data.recent_registrations.map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email.toLowerCase().trim(),
        user_type: user.user_type,
        status: user.status,
        created_at: user.created_at,
      })),
      generated_at: validated.data.generated_at,
      filters: {
        user_type: validated.data.filters.user_type,
        date_from: validated.data.filters.date_from,
        date_to: validated.data.filters.date_to,
      },
    },
  };
};

export const transformShipmentReportsResponse = (data: unknown): ShipmentReportsResponseType => {
  const validated = shipmentReportsResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      summary: {
        total_shipments: validated.data.summary.total_shipments,
        delivered_shipments: validated.data.summary.delivered_shipments,
        delivery_rate: validated.data.summary.delivery_rate,
        by_status: validated.data.summary.by_status.map((item) => ({
          status: item.status,
          count: item.count,
        })),
      },
      recent_shipments: validated.data.recent_shipments.map((shipment) => ({
        id: shipment.id,
        tracking_code: shipment.tracking_code,
        status: shipment.status,
        created_at: shipment.created_at,
        customer: shipment.customer,
        originAO: shipment.originAO,
        destAO: shipment.destAO,
      })),
      generated_at: validated.data.generated_at,
      filters: {
        status: validated.data.filters.status,
        date_from: validated.data.filters.date_from,
        date_to: validated.data.filters.date_to,
      },
    },
  };
};

export const transformSystemReportsResponse = (data: unknown): SystemReportsResponseType => {
  const validated = systemReportsResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      system_statistics: validated.data.system_statistics,
      performance_metrics: validated.data.performance_metrics,
      recent_errors: validated.data.recent_errors.map((error) => ({
        id: error.id,
        action: error.action,
        created_at: error.created_at,
        details: error.details,
      })),
      generated_at: validated.data.generated_at,
    },
  };
};

export const transformExportUsersResponse = (data: unknown): ExportUsersResponseType => {
  const validated = exportUsersResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      id: validated.data.id,
      type: validated.data.type,
      format: validated.data.format,
      filters: validated.data.filters,
      status: validated.data.status,
      created_by: validated.data.created_by,
      created_at: validated.data.created_at,
    },
  };
};

export const transformExportShipmentsResponse = (data: unknown): ExportShipmentsResponseType => {
  const validated = exportShipmentsResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      id: validated.data.id,
      type: validated.data.type,
      format: validated.data.format,
      filters: validated.data.filters,
      status: validated.data.status,
      created_by: validated.data.created_by,
      created_at: validated.data.created_at,
    },
  };
};

export const transformExportDownloadResponse = (data: unknown): ExportDownloadResponseType => exportDownloadResponseSchema.parse(data);
