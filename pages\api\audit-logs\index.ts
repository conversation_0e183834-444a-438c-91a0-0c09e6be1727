import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { auditLogsQuerySchema, AuditLogsResponse, normalizeAuditLogsResponse } from '../../../src/requests/audit-security';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === apiMethods.GET) {
    try {
      // Validate query parameters
      const validatedQuery = auditLogsQuerySchema.parse(req.query);

      const { data } = await BACKEND_API(req).get(API_ENDPOINT.audit.logs, {
        params: validatedQuery,
        headers: { Authorization: token },
      });

      // Normalize the response to handle both array and object responses
      const normalizedData = normalizeAuditLogsResponse(data);

      return createApiResponse(
        res,
        AuditLogsResponse,
        normalizedData,
      );
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: 'Method not allowed' });
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
}

export default handler;
