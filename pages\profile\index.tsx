import { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  TextInput,
  Stack,
  Badge,
  Text,
  Divider,
  Alert,
  LoadingOverlay,
} from '@mantine/core';
import {
  IconUser,
  IconMail,
  IconShield,
  IconCalendar,
  IconCheck,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  updateProfileMutation,
  UpdateProfileRequestType,
} from '../../src/requests/admin-management';
import { AdminRole, UserStatus, AdminUser } from '../../src/types/admin.types';
import { useAdminAuthContext } from '../../src/contexts/AdminAuthContext';

export default function ProfilePage() {
  const { admin, updateAdmin } = useAdminAuthContext();
  const [isEditing, setIsEditing] = useState(false);

  const form = useForm<UpdateProfileRequestType>({
    initialValues: {
      name: admin?.name || '',
      email: admin?.email || '',
    },
    validate: {
      name: (value) => {
        if (!value) return 'Name is required';
        if (value.length < 2) return 'Name must be at least 2 characters';
        return null;
      },
      email: (value) => {
        if (!value) return 'Email is required';
        if (!/\S+@\S+\.\S+/.test(value)) return 'Invalid email format';
        return null;
      },
    },
  });

  const updateMutation = useMutation({
    mutationKey: updateProfileMutation.mutationKey,
    mutationFn: updateProfileMutation.mutationFn,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'Profile updated successfully',
        color: 'green',
      });

      // Update the admin context with new data
      updateAdmin(data.data.admin as AdminUser);

      // Update form with new values
      form.setValues({
        name: data.data.admin.name,
        email: data.data.admin.email,
      });

      setIsEditing(false);
    },
    onError: (error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    },
  });

  // Update form when admin data changes
  useState(() => {
    if (admin) {
      form.setValues({
        name: admin.name,
        email: admin.email,
      });
    }
  });

  const handleSubmit = (values: UpdateProfileRequestType) => {
    updateMutation.mutate(values);
  };

  const handleCancel = () => {
    if (admin) {
      form.setValues({
        name: admin.name,
        email: admin.email,
      });
    }
    setIsEditing(false);
  };

  const getRoleColor = (role: AdminRole) => {
    switch (role) {
      case AdminRole.SUPER_ADMIN:
        return 'purple';
      case AdminRole.ADMIN:
        return 'blue';
      default:
        return 'gray';
    }
  };

  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE:
        return 'green';
      case UserStatus.INACTIVE:
        return 'red';
      case UserStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  if (!admin) {
    return (
      <Container size="md" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error"
          color="red"
          variant="light"
        >
          Unable to load profile information. Please try refreshing the page.
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="md" py="xl">
      <Title order={1} mb="lg">
        Profile Settings
      </Title>

      {/* Profile Information */}
      <Paper withBorder p="lg" mb="lg">
        <LoadingOverlay visible={updateMutation.isPending} />

        <Group justify="space-between" mb="md">
          <Title order={3}>Personal Information</Title>
          {!isEditing && (
            <Button
              variant="outline"
              onClick={() => setIsEditing(true)}
            >
              Edit Profile
            </Button>
          )}
        </Group>

        {isEditing ? (
          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Stack gap="md">
              <TextInput
                withAsterisk
                label="Full Name"
                placeholder="Enter your full name"
                leftSection={<IconUser size="1rem" />}
                {...form.getInputProps('name')}
              />
              <TextInput
                withAsterisk
                label="Email Address"
                placeholder="Enter your email"
                leftSection={<IconMail size="1rem" />}
                {...form.getInputProps('email')}
              />
              <Group justify="flex-end" mt="md">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={updateMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  loading={updateMutation.isPending}
                  leftSection={<IconCheck size="1rem" />}
                >
                  Save Changes
                </Button>
              </Group>
            </Stack>
          </form>
        ) : (
          <Stack gap="md">
            <Group>
              <IconUser size="1.2rem" color="gray" />
              <div>
                <Text size="sm" c="dimmed">Full Name</Text>
                <Text fw={500}>{admin.name}</Text>
              </div>
            </Group>

            <Group>
              <IconMail size="1.2rem" color="gray" />
              <div>
                <Text size="sm" c="dimmed">Email Address</Text>
                <Text fw={500}>{admin.email}</Text>
              </div>
            </Group>
          </Stack>
        )}
      </Paper>

      {/* Account Information */}
      <Paper withBorder p="lg" mb="lg">
        <Title order={3} mb="md">Account Information</Title>

        <Stack gap="md">
          <Group>
            <IconShield size="1.2rem" color="gray" />
            <div>
              <Text size="sm" c="dimmed">Role</Text>
              <Badge color={getRoleColor(admin.role)} size="lg">
                {admin.role?.replace('_', ' ') || 'Admin'}
              </Badge>
            </div>
          </Group>

          <Group>
            <IconCheck size="1.2rem" color="gray" />
            <div>
              <Text size="sm" c="dimmed">Status</Text>
              <Badge color={getStatusColor(admin.status)} size="lg">
                {admin.status}
              </Badge>
            </div>
          </Group>

          <Group>
            <IconCalendar size="1.2rem" color="gray" />
            <div>
              <Text size="sm" c="dimmed">Account Created</Text>
              <Text fw={500}>{formatDate(admin.created_at)}</Text>
            </div>
          </Group>

          <Group>
            <IconCalendar size="1.2rem" color="gray" />
            <div>
              <Text size="sm" c="dimmed">Last Updated</Text>
              <Text fw={500}>{formatDate(admin.updated_at)}</Text>
            </div>
          </Group>

          {admin.last_login && (
            <Group>
              <IconCalendar size="1.2rem" color="gray" />
              <div>
                <Text size="sm" c="dimmed">Last Login</Text>
                <Text fw={500}>{formatDate(admin.last_login)}</Text>
              </div>
            </Group>
          )}
        </Stack>
      </Paper>

      {/* Security Section */}
      <Paper withBorder p="lg">
        <Title order={3} mb="md">Security</Title>

        <Stack gap="md">
          <Group justify="space-between">
            <div>
              <Text fw={500}>Password</Text>
              <Text size="sm" c="dimmed">
                Change your account password
              </Text>
            </div>
            <Button
              variant="outline"
              component="a"
              href="/profile/change-password"
            >
              Change Password
            </Button>
          </Group>

          <Divider />

          <Alert
            icon={<IconShield size="1rem" />}
            title="Account Security"
            color="blue"
            variant="light"
          >
            <Text size="sm">
              Your account is secured with industry-standard encryption.
              For additional security, consider changing your password regularly
              and avoid sharing your login credentials.
            </Text>
          </Alert>
        </Stack>
      </Paper>
    </Container>
  );
}
