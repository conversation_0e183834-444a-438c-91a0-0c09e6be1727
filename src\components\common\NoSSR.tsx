/* eslint-disable react/require-default-props */
import dynamic from 'next/dynamic';
import { ReactNode } from 'react';
import { Box, Loader, Text } from '@mantine/core';

interface NoSSRProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * NoSSR component using Next.js dynamic imports
 * This prevents server-side rendering for components that cause hydration issues
 */
function NoSSR({ children, fallback }: NoSSRProps) {
  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {children}
    </>
  );
}

export default dynamic(() => Promise.resolve(NoSSR), {
  ssr: false,
  loading: () => (
    <Box
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px',
        flexDirection: 'column',
      }}
    >
      <Loader size="sm" />
      <Text size="sm" c="dimmed" mt="xs">Initializing...</Text>
    </Box>
  ),
});
