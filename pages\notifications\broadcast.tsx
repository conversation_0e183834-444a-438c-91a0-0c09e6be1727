// /* eslint-disable no-nested-ternary */
// /* eslint-disable max-lines */
// import React, { useState } from 'react';
// import {
//   Container,
//   Title,
//   Paper,
//   Group,
//   Button,
//   Stack,
//   Text,
//   TextInput,
//   Textarea,
//   Select,
//   MultiSelect,
//   Switch,
//   Alert,
//   LoadingOverlay,
//   Stepper,
//   Card,
//   Badge,
//   Divider,
// } from '@mantine/core';
// import {
//   IconArrowLeft,
//   IconSend,
//   IconEye,
//   IconClock,
//   IconUsers,
//   IconAlertCircle,
// } from '@tabler/icons-react';
// import { useRouter } from 'next/router';
// import { useForm } from '@mantine/form';
// import { DateTimePicker } from '@mantine/dates';
// import { notifications } from '@mantine/notifications';
// import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
// import AdminLayout from '../../src/components/layouts/AdminLayout';
// import { PermissionGuard } from '../../src/components/auth/PermissionGuard';
// import { Permission } from '../../src/utils/permissions';
// import { NotificationPriority } from '../../src/types/notification.types';
// import {
//   sendBroadcastMutationWithInvalidation,
//   scheduleBroadcastMutationWithInvalidation,
//   previewBroadcastMutation,
//   getRecipientOptionsQuery,
// } from '../../src/requests/notifications/broadcast-calls';

// interface BroadcastFormData {
//   title: string;
//   content: string;
//   priority: NotificationPriority;
//   recipients: string[];
//   scheduleDate?: Date;
//   isScheduled: boolean;
// }

// export default function BroadcastMessagePage() {
//   const router = useRouter();
//   const queryClient = useQueryClient();
//   const [activeStep, setActiveStep] = useState(0);
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   const [previewData, setPreviewData] = useState<any>(null);

//   const form = useForm<BroadcastFormData>({
//     initialValues: {
//       title: '',
//       content: '',
//       priority: NotificationPriority.NORMAL,
//       recipients: [],
//       scheduleDate: undefined,
//       isScheduled: false,
//     },
//     validate: {
//       title: (value) => (value.length < 3 ? 'management.titleTooShort' : null),
//       content: (value) => (value.length < 10 ? 'management.contentTooShort' : null),
//       recipients: (value) => (value.length === 0 ? 'management.selectRecipients' : null),
//       scheduleDate: (value, values) => (values.isScheduled && (!value || value <= new Date())
//         ? 'management.invalidScheduleDate'
//         : null),
//     },
//   });

//   const { data: recipientOptionsData } = useQuery(
//     getRecipientOptionsQuery({}),
//   );

//   const recipientOptions = recipientOptionsData?.options || [
//     { value: 'all_users', label: 'management.allUsers' },
//     { value: 'customers', label: 'management.customers' },
//     { value: 'access_operators', label: 'management.accessOperators' },
//     { value: 'car_operators', label: 'management.carOperators' },
//   ];

//   const priorityOptions = [
//     { value: NotificationPriority.LOW, label: 'management.lowPriority' },
//     { value: NotificationPriority.MEDIUM, label: 'management.mediumPriority' },
//     { value: NotificationPriority.HIGH, label: 'management.highPriority' },
//     { value: NotificationPriority.URGENT, label: 'management.urgentPriority' },
//   ];

//   const previewMutation = useMutation(previewBroadcastMutation());
//   const sendMutation = useMutation(
//     sendBroadcastMutationWithInvalidation(queryClient),
//   );
//   const scheduleMutation = useMutation(
//     scheduleBroadcastMutationWithInvalidation(queryClient),
//   );

//   const handlePreview = async () => {
//     if (form.validate().hasErrors) return;

//     try {
//       const previewResult = await previewMutation.mutateAsync({
//         title: form.values.title,
//         content: form.values.content,
//         priority: form.values.priority,
//         recipients: form.values.recipients,
//       });

//       setPreviewData({
//         ...form.values,
//         estimatedRecipients: previewResult.recipientCount,
//         renderedContent: previewResult.renderedContent,
//       });
//       setActiveStep(1);
//     } catch {
//       notifications.show({
//         title: 'error',
//         message: 'previewFailed',
//         color: 'red',
//       });
//     }
//   };

//   const handleSend = async () => {
//     if (form.validate().hasErrors) return;

//     try {
//       const broadcastData = {
//         title: form.values.title,
//         content: form.values.content,
//         priority: form.values.priority,
//         recipients: form.values.recipients,
//       };

//       if (form.values.isScheduled && form.values.scheduleDate) {
//         await scheduleMutation.mutateAsync({
//           ...broadcastData,
//           scheduleDate: form.values.scheduleDate,
//         });

//         notifications.show({
//           title: 'success',
//           message: 'broadcastScheduled',
//           color: 'green',
//         });
//       } else {
//         await sendMutation.mutateAsync(broadcastData);

//         notifications.show({
//           title: 'success',
//           message: 'broadcastSent',
//           color: 'green',
//         });
//       }

//       router.push('/notifications');
//     } catch {
//       notifications.show({
//         title: 'error',
//         message: 'broadcastFailed',
//         color: 'red',
//       });
//     }
//   };

//   const handleBack = () => {
//     if (activeStep > 0) {
//       setActiveStep(activeStep - 1);
//     } else {
//       router.push('/notifications');
//     }
//   };

//   const getPriorityColor = (priority: NotificationPriority) => {
//     switch (priority) {
//       case NotificationPriority.LOW:
//         return 'gray';
//       case NotificationPriority.MEDIUM:
//         return 'blue';
//       case NotificationPriority.HIGH:
//         return 'orange';
//       case NotificationPriority.URGENT:
//         return 'red';
//       default:
//         return 'blue';
//     }
//   };

//   return (
//     <AdminLayout>
//       <PermissionGuard permission={Permission.SEND_BROADCAST_MESSAGES}>
//         <Container size="md" py="md">
//           <Stack gap="lg">
//             <Group justify="space-between">
//               <div>
//                 <Title order={1} size="h2">
//                   management.sendBroadcastMessage
//                 </Title>
//                 <Text c="dimmed" size="sm" mt={4}>
//                   management.createAndSendBroadcastMessage
//                 </Text>
//               </div>
//               <Button
//                 variant="light"
//                 leftSection={<IconArrowLeft size="1rem" />}
//                 onClick={handleBack}
//               >
//                 management.back
//               </Button>
//             </Group>

//             <Stepper active={activeStep} onStepClick={setActiveStep}>
//               <Stepper.Step
//                 label="management.compose"
//                 description="management.createMessage"
//               >
//                 <Paper withBorder p="md" pos="relative">
//                   <LoadingOverlay visible={false} />
//                   <form>
//                     <Stack gap="md">
//                       <TextInput
//                         label="management.messageTitle"
//                         placeholder="management.enterMessageTitle"
//                         required
//                         {...form.getInputProps('title')}
//                       />

//                       <Textarea
//                         label="management.messageContent"
//                         placeholder="management.enterMessageContent"
//                         required
//                         minRows={4}
//                         {...form.getInputProps('content')}
//                       />

//                       <Select
//                         label="priority"
//                         data={priorityOptions}
//                         required
//                         {...form.getInputProps('priority')}
//                       />

//                       <MultiSelect
//                         label="management.recipients"
//                         placeholder="management.selectRecipients"
//                         data={recipientOptions}
//                         required
//                         {...form.getInputProps('recipients')}
//                       />

//                       <Switch
//                         label="management.scheduleForLater"
//                         description="management.scheduleDescription"
//                         {...form.getInputProps('isScheduled', { type: 'checkbox' })}
//                       />

//                       {form.values.isScheduled && (
//                         <DateTimePicker
//                           label="management.scheduleDate"
//                           placeholder="management.selectDateTime"
//                           required
//                           minDate={new Date()}
//                           {...form.getInputProps('scheduleDate')}
//                         />
//                       )}

//                       <Group justify="flex-end">
//                         <Button
//                           leftSection={<IconEye size="1rem" />}
//                           onClick={handlePreview}
//                         >
//                           management.preview
//                         </Button>
//                       </Group>
//                     </Stack>
//                   </form>
//                 </Paper>
//               </Stepper.Step>

//               <Stepper.Step
//                 label="management.preview"
//                 description="management.reviewAndSend"
//               >
//                 {previewData && (
//                   <Stack gap="md">
//                     <Card withBorder>
//                       <Stack gap="sm">
//                         <Group justify="space-between">
//                           <Text fw={600}>{previewData.title}</Text>
//                           <Badge color={getPriorityColor(previewData.priority)}>
//                             {priorityOptions.find(
//                               (p) => p.value === previewData.priority,
//                             )?.label}
//                           </Badge>
//                         </Group>
//                         <Text>{previewData.content}</Text>
//                         <Divider />
//                         <Group gap="xs">
//                           <IconUsers size="1rem" />
//                           <Text size="sm" c="dimmed">
//                             estimatedRecipients:
//                             {' '}
//                             {previewData.estimatedRecipients?.toLocaleString()}
//                           </Text>
//                         </Group>
//                         {previewData.isScheduled && previewData.scheduleDate && (
//                           <Group gap="xs">
//                             <IconClock size="1rem" />
//                             <Text size="sm" c="dimmed">
//                               scheduledFor:
//                               {' '}
//                               {previewData.scheduleDate.toLocaleString()}
//                             </Text>
//                           </Group>
//                         )}
//                       </Stack>
//                     </Card>

//                     <Alert icon={<IconAlertCircle size="1rem" />} color="yellow">
//                       {previewData.isScheduled
//                         ? 'scheduleConfirmation'
//                         : 'sendConfirmation'}
//                     </Alert>

//                     <Group justify="space-between">
//                       <Button variant="light" onClick={() => setActiveStep(0)}>
//                         editMessage
//                       </Button>
//                       <Button
//                         leftSection={
//                           previewData.isScheduled ? (
//                             <IconClock size="1rem" />
//                           ) : (
//                             <IconSend size="1rem" />
//                           )
//                         }
//                         onClick={handleSend}
//                       >
//                         {previewData.isScheduled ? 'schedule' : 'send'}
//                       </Button>
//                     </Group>
//                   </Stack>
//                 )}
//               </Stepper.Step>
//             </Stepper>
//           </Stack>
//         </Container>
//       </PermissionGuard>
//     </AdminLayout>
//   );
// }

// // Protect the page with authentication
// BroadcastMessagePage.auth = true;
