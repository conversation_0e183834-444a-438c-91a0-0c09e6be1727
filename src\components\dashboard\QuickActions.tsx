import {
  Paper, Title, SimpleGrid, Button, Text, Group, Badge,
} from '@mantine/core';
import {
  IconUsers,
  IconUserCheck,
  IconChartBar,
  IconSettings,
  IconPackage,
  IconAlertTriangle,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { QuickAction } from '../../types/admin.types';

interface QuickActionsProps {
  actions: QuickAction[];
}

const getActionIcon = (action: string) => {
  switch (action) {
    case 'navigate_to_users':
      return <IconUsers size="1.2rem" />;
    case 'navigate_to_approvals':
      return <IconUserCheck size="1.2rem" />;
    case 'navigate_to_reports':
      return <IconChartBar size="1.2rem" />;
    case 'navigate_to_settings':
      return <IconSettings size="1.2rem" />;
    case 'navigate_to_shipments':
      return <IconPackage size="1.2rem" />;
    case 'navigate_to_alerts':
      return <IconAlertTriangle size="1.2rem" />;
    default:
      return <IconSettings size="1.2rem" />;
  }
};

const getActionColor = (action: string) => {
  switch (action) {
    case 'navigate_to_users':
      return 'blue';
    case 'navigate_to_approvals':
      return 'orange';
    case 'navigate_to_reports':
      return 'green';
    case 'navigate_to_settings':
      return 'gray';
    case 'navigate_to_shipments':
      return 'indigo';
    case 'navigate_to_alerts':
      return 'red';
    default:
      return 'gray';
  }
};

const getActionRoute = (action: string) => {
  switch (action) {
    case 'navigate_to_users':
      return '/users';
    case 'navigate_to_approvals':
      return '/users?filter=pending';
    case 'navigate_to_reports':
      return '/reports';
    case 'navigate_to_settings':
      return '/settings';
    case 'navigate_to_shipments':
      return '/shipments';
    case 'navigate_to_alerts':
      return '/alerts';
    default:
      return '/dashboard';
  }
};

export function QuickActions({ actions }: QuickActionsProps) {
  const router = useRouter();

  const handleActionClick = (action: string) => {
    const route = getActionRoute(action);
    router.push(route);
  };

  return (
    <Paper withBorder p="md" radius="md" shadow="sm">
      <Title order={4} mb="md">
        Quick Actions
      </Title>

      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="sm">
        {actions.length === 0 ? (
          <Text c="dimmed" ta="center" py="xl" style={{ gridColumn: '1 / -1' }}>
            No quick actions available
          </Text>
        ) : (
          actions.map((action) => (
            <Button
              key={action.action}
              variant="light"
              color={getActionColor(action.action)}
              size="md"
              leftSection={getActionIcon(action.action)}
              onClick={() => handleActionClick(action.action)}
              style={{
                height: 'auto',
                padding: '12px 16px',
              }}
            >
              <div style={{ textAlign: 'left', width: '100%' }}>
                <Group justify="space-between" mb={4}>
                  <Text fw={500} size="sm">
                    {action.title}
                  </Text>
                  {action.count > 0 && (
                    <Badge
                      size="xs"
                      variant="filled"
                      color={getActionColor(action.action)}
                    >
                      {action.count}
                    </Badge>
                  )}
                </Group>
                <Text size="xs" c="dimmed" style={{ textAlign: 'left' }}>
                  {action.description}
                </Text>
              </div>
            </Button>
          ))
        )}
      </SimpleGrid>
    </Paper>
  );
}
