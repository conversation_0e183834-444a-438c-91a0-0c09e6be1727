import z from 'zod';
import {
  SendBroadcastResponse,
  ScheduleBroadcastResponse,
  PreviewBroadcastResponse,
  GetRecipientOptionsResponse,
} from '../../types/notification-api.types';

// Response validation schemas
export const sendBroadcastApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    notificationId: z.string(),
    recipientCount: z.number(),
    scheduledAt: z.string().optional(),
    estimatedDeliveryTime: z.number(),
  }),
});

export const scheduleBroadcastApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    notificationId: z.string(),
    recipientCount: z.number(),
    scheduledAt: z.string(),
    estimatedDeliveryTime: z.number(),
  }),
});

export const previewBroadcastApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    renderedContent: z.string(),
    recipientCount: z.number(),
    estimatedDeliveryTime: z.number(),
    warnings: z.array(z.string()).optional(),
  }),
});

export const getRecipientOptionsApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    users: z.array(z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
      type: z.string(),
    })),
    roles: z.array(z.object({
      id: z.string(),
      name: z.string(),
      userCount: z.number(),
    })),
  }),
});

export const validateRecipientsApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    isValid: z.boolean(),
    recipientCount: z.number(),
    warnings: z.array(z.string()).optional(),
    errors: z.array(z.string()).optional(),
  }),
});

/**
 * Transform send broadcast API response
 */
export const transformSendBroadcastResponse = (response: unknown): SendBroadcastResponse => {
  const validated = sendBroadcastApiResponseSchema.parse(response);

  return {
    success: validated.success,
    message: validated.message,
    data: {
      notificationId: validated.data.notificationId,
      recipientCount: validated.data.recipientCount,
      scheduledAt: validated.data.scheduledAt,
      estimatedDeliveryTime: validated.data.estimatedDeliveryTime,
    },
  };
};

/**
 * Transform schedule broadcast API response
 */
export const transformScheduleBroadcastResponse = (response: unknown): ScheduleBroadcastResponse => {
  const validated = scheduleBroadcastApiResponseSchema.parse(response);

  return {
    success: validated.success,
    message: validated.message,
    data: {
      notificationId: validated.data.notificationId,
      recipientCount: validated.data.recipientCount,
      scheduledAt: validated.data.scheduledAt,
      estimatedDeliveryTime: validated.data.estimatedDeliveryTime,
    },
  };
};

/**
 * Transform preview broadcast API response
 */
export const transformPreviewBroadcastResponse = (response: unknown): PreviewBroadcastResponse => {
  const validated = previewBroadcastApiResponseSchema.parse(response);

  return {
    success: validated.success,
    message: validated.message,
    data: {
      renderedContent: validated.data.renderedContent,
      recipientCount: validated.data.recipientCount,
      estimatedDeliveryTime: validated.data.estimatedDeliveryTime,
      warnings: validated.data.warnings,
    },
  };
};

/**
 * Transform get recipient options API response
 */
export const transformGetRecipientOptionsResponse = (response: unknown): GetRecipientOptionsResponse => {
  const validated = getRecipientOptionsApiResponseSchema.parse(response);

  return {
    success: validated.success,
    message: validated.message,
    data: {
      users: validated.data.users.map((user) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        type: user.type,
      })),
      roles: validated.data.roles.map((role) => ({
        id: role.id,
        name: role.name,
        userCount: role.userCount,
      })),
    },
  };
};

/**
 * Transform validate recipients API response
 */
export const transformValidateRecipientsResponse = (response: unknown) => {
  const validated = validateRecipientsApiResponseSchema.parse(response);

  return {
    success: validated.success,
    message: validated.message,
    data: {
      isValid: validated.data.isValid,
      recipientCount: validated.data.recipientCount,
      warnings: validated.data.warnings || [],
      errors: validated.data.errors || [],
    },
  };
};

/**
 * Helper function to extract error messages from API responses
 */
export const extractBroadcastErrorMessage = (error: unknown): string => {
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;

    // Check for validation errors
    if (errorObj.errors && Array.isArray(errorObj.errors)) {
      return errorObj.errors.join(', ');
    }

    // Check for general error message
    if (typeof errorObj.message === 'string') {
      return errorObj.message;
    }

    // Check for nested error structure
    if (errorObj.data && typeof errorObj.data === 'object') {
      const dataObj = errorObj.data as Record<string, unknown>;
      if (typeof dataObj.message === 'string') {
        return dataObj.message;
      }
    }
  }

  return 'An unexpected error occurred while processing the broadcast request';
};

/**
 * Helper function to format recipient count for display
 */
export const formatRecipientCount = (count: number): string => {
  if (count === 0) return 'No recipients';
  if (count === 1) return '1 recipient';
  return `${count.toLocaleString()} recipients`;
};

/**
 * Helper function to format estimated delivery time
 */
export const formatEstimatedDeliveryTime = (seconds: number): string => {
  if (seconds < 60) return `${seconds} seconds`;
  if (seconds < 3600) return `${Math.round(seconds / 60)} minutes`;
  return `${Math.round(seconds / 3600)} hours`;
};
