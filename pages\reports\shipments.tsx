/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Select,
  Grid,
  Card,
  Text,
  Badge,
  Alert,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconDownload,
  IconRefresh,
  IconPackage,
  IconTruck,
  IconCheck,
  IconClock,
  IconAlertCircle,
  IconX,
} from '@tabler/icons-react';
import { DatePickerInput } from '@mantine/dates';
import { useQuery } from '@tanstack/react-query';
import { getShipmentReportsQuery } from '../../src/requests/reports';
import { DataTable } from '../../src/components/common/DataTable';
import { useLoading } from '../../src/contexts/LoadingContext';
import { ShipmentStatus } from '../../src/types/admin.types';

export default function ShipmentReportsPage() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();

  // Filter states
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    new Date(), // today
  ]);
  const [status, setStatus] = useState<ShipmentStatus | ''>('');

  // Build query parameters
  const queryParams = {
    status: status || undefined,
    date_from: dateRange[0]?.toISOString(),
    date_to: dateRange[1]?.toISOString(),
  };

  // Fetch shipment reports data
  const {
    data: reportsData,
    isLoading,
    error,
    refetch,
  } = useQuery(getShipmentReportsQuery(queryParams));

  const shipments = reportsData?.data?.recent_shipments || [];

  // Calculate statistics from API data
  const stats = {
    total: reportsData?.data?.summary?.total_shipments || 0,
    pending: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === ShipmentStatus.PENDING)?.count || 0,
    assigned: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === ShipmentStatus.ASSIGNED)?.count || 0,
    inTransit: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === ShipmentStatus.IN_TRANSIT)?.count || 0,
    delivered: reportsData?.data?.summary?.delivered_shipments || 0,
    cancelled: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === ShipmentStatus.CANCELLED)?.count || 0,
    expired: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === ShipmentStatus.EXPIRED)?.count || 0,
  };

  // Set loading state
  useEffect(() => {
    if (isLoading) {
      showLoading('Loading shipment reports...');
    } else if (!isLoading || error) {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading, error]);

  // Handle export
  const handleExport = async (exportFormat: 'csv' | 'excel') => {
    try {
      const response = await fetch('/api/export/shipments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: exportFormat,
          filters: {
            ...(status && { status }),
            ...(dateRange[0] && { date_from: dateRange[0].toISOString() }),
            ...(dateRange[1] && { date_to: dateRange[1].toISOString() }),
          },
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const filename = `shipments_export_${new Date().toISOString().split('T')[0]}.${exportFormat}`;

        // Download file
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Show success notification (you can customize this based on your notification system)
      } else {
        throw new Error('Export failed');
      }
    } catch {
      // Show error notification (you can customize this based on your notification system)
    }
  };

  // Get status badge color
  const getStatusColor = (s: string) => {
    switch (s) {
      case ShipmentStatus.PENDING: return 'orange';
      case ShipmentStatus.ASSIGNED: return 'cyan';
      case ShipmentStatus.IN_TRANSIT: return 'blue';
      case ShipmentStatus.DELIVERED: return 'green';
      case ShipmentStatus.CANCELLED: return 'red';
      case ShipmentStatus.EXPIRED: return 'gray';
      default: return 'gray';
    }
  };

  // Table columns
  const columns = [
    {
      accessor: 'id',
      label: 'Shipment ID',
      sortable: true,
    },
    {
      accessor: 'tracking_code',
      label: 'Tracking Code',
      sortable: true,
    },
    {
      accessor: 'customer',
      label: 'Customer',
      sortable: true,
      render: (shipment: any) => shipment.customer?.name || 'N/A',
    },
    {
      accessor: 'originAO',
      label: 'Origin',
      sortable: true,
      render: (shipment: any) => shipment.originAO?.business_name || 'N/A',
    },
    {
      accessor: 'destAO',
      label: 'Destination',
      sortable: true,
      render: (shipment: any) => shipment.destAO?.business_name || 'N/A',
    },
    {
      accessor: 'status',
      label: 'Status',
      sortable: true,
      render: (shipment: any) => (
        <Badge color={getStatusColor(shipment.status)} variant="light">
          {shipment.status.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      accessor: 'created_at',
      label: 'Created',
      sortable: true,
      render: (shipment: any) => new Date(shipment.created_at).toLocaleDateString(),
    },
  ];

  return (
    <div>
      <Head>
        <title>Shipment Reports | NAQALAT Admin</title>
      </Head>

      <Container size="xl" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <Group justify="space-between">
            <Group>
              <ActionIcon
                variant="subtle"
                size="lg"
                onClick={() => router.push('/reports')}
              >
                <IconArrowLeft size="1.2rem" />
              </ActionIcon>
              <div>
                <Title order={1}>Shipment Reports</Title>
                <Text c="dimmed" size="lg">
                  Detailed shipment tracking and analytics
                </Text>
              </div>
            </Group>
            <Group>
              <Tooltip label="Refresh data">
                <ActionIcon variant="light" onClick={() => refetch()}>
                  <IconRefresh size="1rem" />
                </ActionIcon>
              </Tooltip>
              <Button
                leftSection={<IconDownload size="1rem" />}
                onClick={() => handleExport('excel')}
              >
                Export Excel
              </Button>
              <Button
                variant="outline"
                leftSection={<IconDownload size="1rem" />}
                onClick={() => handleExport('csv')}
              >
                Export CSV
              </Button>
            </Group>
          </Group>

          {/* Statistics Cards */}
          <Grid>
            <Grid.Col span={{ base: 6, sm: 2 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Total
                    </Text>
                    <Text fw={700} size="xl">
                      {stats.total}
                    </Text>
                  </div>
                  <IconPackage size="2rem" color="var(--mantine-color-blue-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 2 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Pending
                    </Text>
                    <Text fw={700} size="xl" c="orange">
                      {stats.pending}
                    </Text>
                  </div>
                  <IconClock size="2rem" color="var(--mantine-color-orange-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 2 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      In Transit
                    </Text>
                    <Text fw={700} size="xl" c="blue">
                      {stats.inTransit}
                    </Text>
                  </div>
                  <IconTruck size="2rem" color="var(--mantine-color-blue-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 2 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Delivered
                    </Text>
                    <Text fw={700} size="xl" c="green">
                      {stats.delivered}
                    </Text>
                  </div>
                  <IconCheck size="2rem" color="var(--mantine-color-green-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 2 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Cancelled
                    </Text>
                    <Text fw={700} size="xl" c="red">
                      {stats.cancelled}
                    </Text>
                  </div>
                  <IconX size="2rem" color="var(--mantine-color-red-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 2 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Expired
                    </Text>
                    <Text fw={700} size="xl" c="gray">
                      {stats.expired}
                    </Text>
                  </div>
                  <IconClock size="2rem" color="var(--mantine-color-gray-6)" />
                </Group>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Filters */}
          <Paper withBorder p="md">
            <Group gap="md" align="end">
              <DatePickerInput
                type="range"
                label="Date Range"
                placeholder="Select date range"
                value={dateRange}
                onChange={(value) => setDateRange(value as [Date | null, Date | null])}
                style={{ flex: 1 }}
              />
              <Select
                label="Status"
                placeholder="All statuses"
                data={[
                  { value: '', label: 'All Statuses' },
                  { value: ShipmentStatus.PENDING, label: 'Pending' },
                  { value: ShipmentStatus.ASSIGNED, label: 'Assigned' },
                  { value: ShipmentStatus.IN_TRANSIT, label: 'In Transit' },
                  { value: ShipmentStatus.DELIVERED, label: 'Delivered' },
                  { value: ShipmentStatus.CANCELLED, label: 'Cancelled' },
                  { value: ShipmentStatus.EXPIRED, label: 'Expired' },
                ]}
                value={status}
                onChange={(value) => setStatus((value as ShipmentStatus) || '')}
                clearable
                w={200}
              />
            </Group>
          </Paper>

          {/* Error Alert */}
          {error && (
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title="Error loading shipment reports"
              color="red"
              variant="light"
            >
              {typeof error === 'string' ? error : 'Failed to load shipment reports. Please try again.'}
            </Alert>
          )}

          {/* Data Table */}
          <Paper withBorder>
            <DataTable
              columns={columns}
              data={shipments}
              loading={isLoading}
              error={typeof error === 'string' ? error : null}
              emptyMessage="No shipment data found for the selected criteria"
            />
          </Paper>
        </Stack>
      </Container>
    </div>
  );
}
