/* eslint-disable @typescript-eslint/no-explicit-any */
declare module 'leaflet' {
  export type LatLngExpression = [number, number] | { lat: number; lng: number } | { lat: number; lon: number };

  export class Icon {
    constructor(options: any);

    static Default: {
      prototype: any;
      mergeOptions: (options: any) => void;
    };
  }

  export interface MapEvent {
    latlng: {
      lat: number;
      lng: number;
    };
  }
}
