/* eslint-disable no-console */
import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, getJwt } from '../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { exportShipmentsRequestSchema } from '../../../src/requests/export/request-transformer';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === apiMethods.POST) {
    try {
      // Validate request body
      const validatedBody = exportShipmentsRequestSchema.parse(req.body);

      // Request file from backend with responseType 'stream'
      const response = await BACKEND_API(req).post(API_ENDPOINT.export.shipments(), validatedBody, {
        headers: {
          authorization: token,
          'Content-Type': 'application/json',
        },
        responseType: 'stream', // This tells axios to handle binary data
      });

      // Get filename from response headers or create default
      const contentDisposition = response.headers['content-disposition'];
      let filename = `shipments_export_${new Date().toISOString().split('T')[0]}.${validatedBody.format}`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          [, filename] = filenameMatch;
        }
      }

      // Set appropriate headers for file download
      const contentType = validatedBody.format === 'csv'
        ? 'text/csv'
        : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');

      // Pipe the response directly to the client
      response.data.pipe(res);
    } catch (e) {
      const error = createApiError({ error: e });
      res.status(error.code).json(error);
    }
  } else {
    const error = createApiError({ error: 'Method not allowed' });
    res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
  }
}

export default handler;
