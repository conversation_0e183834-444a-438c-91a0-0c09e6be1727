/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useCallback } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { AdminUser } from '../types/admin.types';

interface AdminAuthState {
  admin: AdminUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AdminAuthActions {
  login: (admin: AdminUser, token: string) => void;
  logout: () => void;
  updateAdmin: (admin: AdminUser) => void;
}

type AdminAuthHook = AdminAuthState & AdminAuthActions;

/**
 * Custom hook for admin authentication state management using NextAuth
 */
export const useAdminAuth = (): AdminAuthHook => {
  const { data: session, status } = useSession();
  const [state, setState] = useState<AdminAuthState>({
    admin: null,
    token: null,
    isAuthenticated: false,
    isLoading: true,
  });

  // Update state based on NextAuth session
  useEffect(() => {
    if (status === 'loading') {
      setState((prev) => ({ ...prev, isLoading: true }));
      return;
    }

    if (status === 'authenticated' && session?.user) {
      const admin: AdminUser = {
        id: session.user.id as string,
        name: session.user.name || '',
        email: session.user.email || '',
        role: (session.user as any).role,
        status: (session.user as any).status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setState({
        admin,
        token: (session as any).accessToken || '',
        isAuthenticated: true,
        isLoading: false,
      });
    } else if (status === 'unauthenticated') {
      setState({
        admin: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      });
    }
    // Don't update state if status is still loading or in transition
  }, [session, status]);

  // Login function - triggers NextAuth signIn
  const login = useCallback(async (_admin: AdminUser, _token: string) => {
    // This is handled by NextAuth, but we keep the interface for compatibility
    // The actual login happens through the NextAuth signIn function
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    await signOut({
      callbackUrl: '/auth/login',
      redirect: true,
    });
  }, []);

  // Update admin data
  const updateAdmin = useCallback((admin: AdminUser) => {
    setState((prevState) => ({
      ...prevState,
      admin,
    }));
  }, []);

  return {
    ...state,
    login,
    logout,
    updateAdmin,
  };
};

export default useAdminAuth;
