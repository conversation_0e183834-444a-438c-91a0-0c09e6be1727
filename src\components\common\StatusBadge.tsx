import { Badge } from '@mantine/core';

const STATUS_COLORS: Record<string, string> = {
  PENDING: 'orange',
  AWAITING_PICKUP: 'blue',
  IN_TRANSIT: 'cyan',
  ARRIVED_AT_DESTINATION: 'grape',
  DELIVERED: 'green',
  CANCELLED: 'red',
};

const getStatusLabel = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'PENDING';
    case 'AWAITING_PICKUP':
      return 'AWAITING PICKUP';
    case 'IN_TRANSIT':
      return 'IN TRANSIT';
    case 'ARRIVED_ATDESTINATION':
      return 'ARRIVED AT DESTINATION';
    case 'DELIVERED':
      return 'DELIVERED';
    case 'CANCELLED':
      return 'CANCELLED';
    default:
      return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, (l) => l.toUpperCase());
  }
};

export function StatusBadge({ status }: { status: string }) {
  return (
    <Badge color={STATUS_COLORS[status] || 'gray'} variant="light">
      {getStatusLabel(status)}
    </Badge>
  );
}
