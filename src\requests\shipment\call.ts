import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { getShipmentsQueryProps } from './type';

enum queryKeys {
  shipments = 'shipments',
  shipment = 'shipment',
  update = 'update',
}

const getShipmentsRequest = (props: getShipmentsQueryProps) => {
  const {
    filters, sort, pagination,
  } = props;
  return CLIENT_API.get(API_ENDPOINT.shipments.list(), {
    params: {
      search: filters?.search,
      status: filters?.status,
      originAoId: filters?.originAoId,
      destAoId: filters?.destAoId,
      customerId: filters?.customerId,
      sort,
      limit: pagination?.limit || pagination?.pageSize,
      page: pagination?.page,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};
export const getShipmentsQuery = (props: getShipmentsQueryProps) => ({
  queryKey: [queryKeys.shipments, props?.filters, props?.pagination, props?.sort],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  queryFn: (params: any) => getShipmentsRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

const getShipmentRequest = (id: string) => CLIENT_API.get(API_ENDPOINT.shipments.byId(id))
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err.response.data;
  });
// get user request query
export const getShipmentQuery = (
  id: string,
  { isAuth }: { isAuth: boolean },
) => ({
  queryKey: [queryKeys.shipment, id],
  queryFn: () => getShipmentRequest(id),
  enabled: isAuth,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});
