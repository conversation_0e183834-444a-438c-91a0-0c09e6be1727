/* eslint-disable react/jsx-no-constructed-context-values */
import React, { createContext, useContext, ReactNode } from 'react';
import { useNotificationPolling } from '../../hooks/useNotificationPolling';

interface NotificationContextValue {
  unreadCount: number;
  isLoading: boolean;
  refetch: () => void;
}

const NotificationContext = createContext<NotificationContextValue | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
  /**
   * Polling interval in milliseconds
   * @default 30000 (30 seconds)
   */
  // eslint-disable-next-line react/require-default-props
  pollingInterval?: number;

  /**
   * Whether to show toast notifications for new notifications
   * @default true
   */
  // eslint-disable-next-line react/require-default-props
  showToasts?: boolean;

  /**
   * Callback when new notifications are detected
   */
  // eslint-disable-next-line react/require-default-props
  onNewNotifications?: (newCount: number, previousCount: number) => void;
}

export function NotificationProvider({
  children,
  pollingInterval = 30000,
  showToasts = true,
  onNewNotifications,
}: NotificationProviderProps) {
  const { unreadCount, isLoading, refetch } = useNotificationPolling({
    interval: pollingInterval,
    enabled: true,
    showToasts,
    onNewNotifications,
  });

  const contextValue: NotificationContextValue = {
    unreadCount: unreadCount || 0,
    isLoading,
    refetch,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotificationContext() {
  const context = useContext(NotificationContext);

  if (context === undefined) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }

  return context;
}

export default NotificationProvider;
