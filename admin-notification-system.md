# Admin Notification System

The admin notification system provides comprehensive notifications to administrators about important system events, user activities, and security alerts.

## Overview

The system consists of:
- **AdminNotification Model**: Database model for storing admin notifications
- **AdminNotificationService**: Service layer for managing admin notifications
- **Admin Notification Endpoints**: RESTful API endpoints for admin notification management
- **Automatic Triggers**: System events that automatically create admin notifications

## Database Model

### AdminNotification Table
```sql
CREATE TABLE admin_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID NOT NULL REFERENCES admins(id) ON DELETE CASCADE,
  shipment_id UUID REFERENCES shipments(id) ON DELETE CASCADE,
  notification_type VARCHAR NOT NULL,
  title VARCHAR NOT NULL,
  message VARCHAR NOT NULL,
  priority VARCHAR NOT NULL DEFAULT 'NORMAL',
  read BOOLEAN NOT NULL DEFAULT false,
  read_at TIMESTAMP,
  metadata JSONB,
  expires_at TIMESTAMP,
  created_at TIMESTAMP NOT NULL DEFAULT now(),
  updated_at TIMESTAMP NOT NULL DEFAULT now()
);

CREATE INDEX idx_admin_notifications_admin_read ON admin_notifications(admin_id, read);
CREATE INDEX idx_admin_notifications_admin_created ON admin_notifications(admin_id, created_at);
CREATE INDEX idx_admin_notifications_shipment ON admin_notifications(shipment_id);
```

## Notification Types

### User Management Events
- `USER_REGISTERED`: New user registration
- `USER_EMAIL_VERIFIED`: User email verification completed
- `OPERATOR_NEEDS_APPROVAL`: Access/Car Operator needs admin approval
- `USER_STATUS_CHANGED`: User status changed by admin

### Security Events
- `SECURITY_ALERT`: Failed login attempts, suspicious activity
- `ADMIN_CREATED`: New admin account created

### System Events
- `SHIPMENT_CREATED`: New shipment created (optional, can be high volume)
- `SYSTEM_ERROR`: System errors requiring admin attention
- `BULK_OPERATION_COMPLETED`: Bulk operations completed
- `SYSTEM_MAINTENANCE`: System maintenance notifications

## Priority Levels
- `LOW`: Informational notifications
- `NORMAL`: Standard notifications
- `HIGH`: Important notifications requiring attention
- `URGENT`: Critical notifications requiring immediate action

## API Endpoints

### Get Admin's Notifications
```http
GET /api/admin/notifications/my
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `type` (optional): Filter by notification type
- `priority` (optional): Filter by priority (LOW, NORMAL, HIGH, URGENT)
- `read` (optional): Filter by read status (true/false)
- `search` (optional): Search in title and message
- `from_date` (optional): Filter from date (ISO 8601)
- `to_date` (optional): Filter to date (ISO 8601)
- `shipment_id` (optional): Filter by shipment ID

**Response:**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "admin_id": "uuid",
        "shipment_id": "uuid",
        "notification_type": "USER_REGISTERED",
        "title": "New User Registration",
        "message": "A new customer has registered: John Doe (<EMAIL>)",
        "priority": "NORMAL",
        "read": false,
        "read_at": null,
        "metadata": {
          "user_id": "uuid",
          "user_name": "John Doe",
          "user_email": "<EMAIL>",
          "user_type": "CUSTOMER"
        },
        "expires_at": null,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "shipment": null
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    },
    "unreadCount": 12
  }
}
```

### Get Unread Count
```http
GET /api/admin/notifications/unread-count
```

**Response:**
```json
{
  "success": true,
  "data": {
    "unreadCount": 12
  }
}
```

### Mark Notification as Read
```http
PUT /api/admin/notifications/:id/read
```

**Response:**
```json
{
  "success": true,
  "message": "Notification marked as read successfully",
  "data": {
    "notification": {
      "id": "uuid",
      "read": true,
      "read_at": "2024-01-15T11:00:00Z"
    }
  }
}
```

### Mark All Notifications as Read
```http
PUT /api/admin/notifications/mark-all-read
```

**Response:**
```json
{
  "success": true,
  "message": "Marked 12 notifications as read",
  "data": {
    "updatedCount": 12
  }
}
```

### Delete Notification
```http
DELETE /api/admin/notifications/:id
```

**Response:**
```json
{
  "success": true,
  "message": "Notification deleted successfully"
}
```

## Automatic Notification Triggers

### User Registration
- **Trigger**: When a new user registers
- **Type**: `USER_REGISTERED`
- **Priority**: `NORMAL`
- **Recipients**: All active admins

### Email Verification
- **Trigger**: When user verifies email
- **Type**: `USER_EMAIL_VERIFIED`
- **Priority**: `LOW`
- **Recipients**: All active admins

### Operator Approval Needed
- **Trigger**: When Access/Car Operator completes registration
- **Type**: `OPERATOR_NEEDS_APPROVAL`
- **Priority**: `HIGH`
- **Recipients**: All active admins

### User Status Changes
- **Trigger**: When admin changes user status
- **Type**: `USER_STATUS_CHANGED`
- **Priority**: `NORMAL` (or `HIGH` for suspensions)
- **Recipients**: All active admins

### Security Alerts
- **Trigger**: Failed admin login attempts
- **Type**: `SECURITY_ALERT`
- **Priority**: `HIGH`
- **Recipients**: All active admins

### New Admin Created
- **Trigger**: When new admin account is created
- **Type**: `ADMIN_CREATED`
- **Priority**: `HIGH`
- **Recipients**: All active admins

### Shipment Creation (Optional)
- **Trigger**: When new shipment is created
- **Type**: `SHIPMENT_CREATED`
- **Priority**: `LOW`
- **Recipients**: All active admins
- **Note**: Can be disabled if too frequent

## Service Usage

### Creating Admin Notifications

```typescript
import { ServiceFactory } from '../services/ServiceFactory';

const adminNotificationService = ServiceFactory.getAdminNotificationService();

// Notify all admins
await adminNotificationService.notifyAllAdmins(
  'USER_REGISTERED',
  'New User Registration',
  'A new customer has registered: John Doe (<EMAIL>)',
  'NORMAL',
  {
    user_id: 'uuid',
    user_name: 'John Doe',
    user_email: '<EMAIL>',
    user_type: 'CUSTOMER'
  }
);

// Create notification for specific admin
await adminNotificationService.createAdminNotification({
  adminId: 'admin-uuid',
  type: 'SYSTEM_ERROR',
  title: 'Database Connection Error',
  message: 'Database connection failed at 10:30 AM',
  priority: 'URGENT',
  metadata: {
    error_code: 'DB_CONN_001',
    timestamp: new Date().toISOString()
  }
});
```

## Frontend Integration

### Polling for Unread Count
```javascript
// Poll every 30 seconds for unread count
setInterval(async () => {
  const response = await fetch('/api/admin/notifications/unread-count', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const data = await response.json();
  updateNotificationBadge(data.data.unreadCount);
}, 30000);
```

### Real-time Updates (WebSocket)
Consider implementing WebSocket connections for real-time notification updates to provide immediate feedback to admins.

## Security Considerations

1. **Authentication**: All endpoints require admin authentication
2. **Authorization**: Admins can only access their own notifications
3. **Data Privacy**: Sensitive information in metadata should be carefully managed
4. **Rate Limiting**: Consider rate limiting notification creation to prevent spam

## Performance Considerations

1. **Indexing**: Database indexes on admin_id, read status, and created_at
2. **Pagination**: All list endpoints support pagination
3. **Cleanup**: Consider implementing automatic cleanup of old notifications
4. **Caching**: Consider caching unread counts for better performance

## Monitoring and Maintenance

1. **Metrics**: Track notification creation rates and read rates
2. **Cleanup**: Implement periodic cleanup of old read notifications
3. **Performance**: Monitor query performance and optimize as needed
4. **Storage**: Monitor database storage usage for notification data
