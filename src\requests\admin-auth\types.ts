import { z } from 'zod';

// Constants for validation messages
const EMAIL_INVALID_FORMAT = 'Invalid email format';
const PASSWORD_MIN_LENGTH = 'Password must be at least 6 characters';
const NAME_MIN_LENGTH = 'Name must be at least 2 characters';

// Zod schemas for request validation
export const adminLoginRequestSchema = z.object({
  email: z.string().email(EMAIL_INVALID_FORMAT),
  password: z.string().min(6, PASSWORD_MIN_LENGTH),
});

export const adminRegisterRequestSchema = z.object({
  name: z.string().min(2, NAME_MIN_LENGTH),
  email: z.string().email(EMAIL_INVALID_FORMAT),
  password: z.string().min(6, PASSWORD_MIN_LENGTH),
  role: z.enum(['ADMIN', 'SUPER_ADMIN']),
});

export const otpVerificationRequestSchema = z.object({
  email: z.string().email(EMAIL_INVALID_FORMAT),
  otp: z.string().length(6, 'OTP must be 6 digits'),
});

export const forgotPasswordRequestSchema = z.object({
  email: z.string().email(EMAIL_INVALID_FORMAT),
});

export const resetPasswordRequestSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  email: z.string().email(EMAIL_INVALID_FORMAT),
  newPassword: z.string().min(6, PASSWORD_MIN_LENGTH),
});

export const changePasswordRequestSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
});

// Zod schemas for response validation
export const adminUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  role: z.enum(['ADMIN', 'SUPER_ADMIN']),
  status: z.enum(['ACTIVE', 'SUSPENDED', 'PENDING']),
  created_at: z.string(),
  updated_at: z.string(),
  last_login: z.string().optional(),
});

export const adminLoginResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: adminUserSchema,
    token: z.string(),
  }),
});

export const adminRegisterResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: adminUserSchema,
  }),
});

export const otpVerificationResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    admin: z.object({
      id: z.string(),
      status: z.enum(['ACTIVE', 'SUSPENDED', 'PENDING']),
    }),
    token: z.string(),
  }),
});

export const forgotPasswordResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

export const resetPasswordResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

export const changePasswordResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

// Type inference from schemas
export type AdminLoginRequestType = z.infer<typeof adminLoginRequestSchema>;
export type AdminRegisterRequestType = z.infer<typeof adminRegisterRequestSchema>;
export type OtpVerificationRequestType = z.infer<typeof otpVerificationRequestSchema>;
export type ForgotPasswordRequestType = z.infer<typeof forgotPasswordRequestSchema>;
export type ResetPasswordRequestType = z.infer<typeof resetPasswordRequestSchema>;
export type ChangePasswordRequestType = z.infer<typeof changePasswordRequestSchema>;

export type AdminLoginResponseType = z.infer<typeof adminLoginResponseSchema>;
export type AdminRegisterResponseType = z.infer<typeof adminRegisterResponseSchema>;
export type OtpVerificationResponseType = z.infer<typeof otpVerificationResponseSchema>;
export type ForgotPasswordResponseType = z.infer<typeof forgotPasswordResponseSchema>;
export type ResetPasswordResponseType = z.infer<typeof resetPasswordResponseSchema>;
export type ChangePasswordResponseType = z.infer<typeof changePasswordResponseSchema>;
