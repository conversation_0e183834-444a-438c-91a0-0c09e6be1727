/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  adminListParamsSchema,
  adminStatusChangeRequestSchema,
  createAdminRequestSchema,
  updateAdminRequestSchema,
  updateProfileRequestSchema,
  changePasswordRequestSchema,
  AdminStatusChangeRequestType,
  CreateAdminRequestType,
  UpdateAdminRequestType,
  UpdateProfileRequestType,
  ChangePasswordRequestType,
} from './types';

// Request transformers - convert frontend data to backend format
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const transformAdminListParams = (params: any) => {
  // Convert string parameters to appropriate types before validation
  const processedParams: any = { ...params };

  // Convert page and limit from strings to numbers if they exist
  if (processedParams.page !== undefined && processedParams.page !== '') {
    const pageNum = parseInt(processedParams.page, 10);
    processedParams.page = Number.isNaN(pageNum) ? undefined : pageNum;
  }

  if (processedParams.limit !== undefined && processedParams.limit !== '') {
    const limitNum = parseInt(processedParams.limit, 10);
    processedParams.limit = Number.isNaN(limitNum) ? undefined : limitNum;
  }

  const validated = adminListParamsSchema.parse(processedParams);

  // Remove undefined values and create clean params object
  const cleanParams: Record<string, unknown> = {};

  if (validated.page !== undefined) cleanParams.page = validated.page;
  if (validated.limit !== undefined) cleanParams.limit = validated.limit;

  return cleanParams;
};

export const transformAdminStatusChangeRequest = (data: AdminStatusChangeRequestType) => {
  const validated = adminStatusChangeRequestSchema.parse(data);
  return {
    adminId: validated.adminId,
    status: validated.status,
  };
};

export const transformCreateAdminRequest = (data: CreateAdminRequestType) => {
  const validated = createAdminRequestSchema.parse(data);
  return {
    name: validated.name.trim(),
    email: validated.email.toLowerCase().trim(),
    password: validated.password,
    role: validated.role,
  };
};

export const transformUpdateAdminRequest = (data: UpdateAdminRequestType) => {
  const validated = updateAdminRequestSchema.parse(data);

  const cleanData: Record<string, unknown> = {
    adminId: validated.adminId,
  };

  if (validated.name !== undefined) cleanData.name = validated.name.trim();
  if (validated.email !== undefined) cleanData.email = validated.email.toLowerCase().trim();
  if (validated.role !== undefined) cleanData.role = validated.role;

  return cleanData;
};

export const transformUpdateProfileRequest = (data: UpdateProfileRequestType) => {
  const validated = updateProfileRequestSchema.parse(data);

  const cleanData: Record<string, unknown> = {};

  if (validated.name !== undefined) cleanData.name = validated.name.trim();
  if (validated.email !== undefined) cleanData.email = validated.email.toLowerCase().trim();

  return cleanData;
};

export const transformChangePasswordRequest = (data: ChangePasswordRequestType) => {
  const validated = changePasswordRequestSchema.parse(data);
  return {
    currentPassword: validated.currentPassword,
    newPassword: validated.newPassword,
  };
};
