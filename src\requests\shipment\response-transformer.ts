import { z } from 'zod';
import { ApiResponseSchema, PaginationSchema } from '../common';

export const ShipmentBackendSchema = z.object({
  id: z.string().uuid(),
  tracking_code: z.string(),
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'EXPIRED']),
  description: z.string(),
  receiver_name: z.string(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  customer: z.object({
    id: z.string().uuid(),
    name: z.string(),
    email: z.string().email(),
  }).nullable(),
  originAO: z.object({
    id: z.string().uuid(),
    business_name: z.string(),
  }).nullable(),
  destAO: z.object({
    id: z.string().uuid(),
    business_name: z.string(),
  }).nullable(),
});

// Detailed shipment schema for individual shipment view
export const ShipmentDetailBackendSchema = z.object({
  id: z.string().uuid(),
  tracking_code: z.string(),
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'EXPIRED']),
  description: z.string(),
  receiver_name: z.string(),
  receiver_phone: z.string().optional(),
  receiver_address: z.string().optional(),
  weight: z.number().optional(),
  dimensions: z.string().optional(),
  special_instructions: z.string().optional(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  customer: z.object({
    id: z.string().uuid(),
    name: z.string(),
    email: z.string().email(),
    phone: z.string().optional(),
  }).nullable(),
  originAO: z.object({
    id: z.string().uuid(),
    business_name: z.string(),
    address: z.string().optional(),
    geo_latitude: z.number().optional(),
    geo_longitude: z.number().optional(),
  }).nullable(),
  destAO: z.object({
    id: z.string().uuid(),
    business_name: z.string(),
    address: z.string().optional(),
    geo_latitude: z.number().optional(),
    geo_longitude: z.number().optional(),
  }).nullable(),
  auditLogs: z.array(z.object({
    id: z.string().uuid(),
    action: z.string(),
    details: z.string().optional(),
    created_at: z.string().datetime(),
    user: z.object({
      name: z.string(),
      email: z.string().email(),
    }).nullable(),
    admin: z.object({
      name: z.string(),
      email: z.string().email(),
    }).nullable(),
  })).optional(),
  qrLabels: z.array(z.object({
    id: z.string().uuid(),
    qr_code: z.string(),
    status: z.string(),
    created_at: z.string().datetime(),
  })).optional(),
});

export const ShipmentsListResponse = ApiResponseSchema(
  z.object({
    shipments: z.array(ShipmentBackendSchema),
    pagination: PaginationSchema,
  }),
);

export const ShipmentDetailResponse = ApiResponseSchema(ShipmentDetailBackendSchema);

export const ShipmentsApiResponse = (
  item: z.infer<typeof ShipmentBackendSchema>,
) => ({
  id: item.id,
  trackingCode: item.tracking_code,
  status: item.status,
  description: item.description,
  receiverName: item.receiver_name,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  customer: item.customer
    ? {
      id: item.customer.id,
      name: item.customer.name,
      email: item.customer.email,
    }
    : null,
  originAO: item.originAO
    ? {
      id: item.originAO.id,
      businessName: item.originAO.business_name,
    }
    : null,
  destAO: item.destAO
    ? {
      id: item.destAO.id,
      businessName: item.destAO.business_name,
    }
    : null,
});

export const ShipmentDetailApiResponse = (
  item: z.infer<typeof ShipmentDetailBackendSchema>,
) => ({
  id: item.id,
  trackingCode: item.tracking_code,
  status: item.status,
  description: item.description,
  receiverName: item.receiver_name,
  receiverPhone: item.receiver_phone,
  receiverAddress: item.receiver_address,
  weight: item.weight,
  dimensions: item.dimensions,
  specialInstructions: item.special_instructions,
  createdAt: item.created_at,
  updatedAt: item.updated_at,
  customer: item.customer
    ? {
      id: item.customer.id,
      name: item.customer.name,
      email: item.customer.email,
      phone: item.customer.phone,
    }
    : null,
  originAO: item.originAO
    ? {
      id: item.originAO.id,
      businessName: item.originAO.business_name,
      address: item.originAO.address,
      geoLatitude: item.originAO.geo_latitude,
      geoLongitude: item.originAO.geo_longitude,
    }
    : null,
  destAO: item.destAO
    ? {
      id: item.destAO.id,
      businessName: item.destAO.business_name,
      address: item.destAO.address,
      geoLatitude: item.destAO.geo_latitude,
      geoLongitude: item.destAO.geo_longitude,
    }
    : null,
  auditLogs: item.auditLogs?.map((log) => ({
    id: log.id,
    action: log.action,
    details: log.details,
    createdAt: log.created_at,
    user: log.user
      ? {
        name: log.user.name,
        email: log.user.email,
      }
      : null,
    admin: log.admin
      ? {
        name: log.admin.name,
        email: log.admin.email,
      }
      : null,
  })) || [],
  qrLabels: item.qrLabels?.map((qr) => ({
    id: qr.id,
    qrCode: qr.qr_code,
    status: qr.status,
    createdAt: qr.created_at,
  })) || [],
});
