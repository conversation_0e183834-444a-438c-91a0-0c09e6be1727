import { NextApiRequest } from 'next';
import axios from 'axios';

const API_BASE_URL = process.env.BACKEND_API_URL || 'http://localhost:8000';

const CLIENT_API = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  },
});

const BACKEND_API = (req: NextApiRequest) => axios.create({
  baseURL: `${API_BASE_URL}/api/admin`,
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    Pragma: 'no-cache',
    Expires: '0',
  },
});

CLIENT_API.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      const { signOut } = await import('next-auth/react');

      // Sign out the user
      await signOut({ redirect: false });

      // Redirect to login page
      window.location.href = '/auth/login';

      return Promise.reject(error);
    }

    return Promise.reject(error);
  },
);

export { CLIENT_API, BACKEND_API };
