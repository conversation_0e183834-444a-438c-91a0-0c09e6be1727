import { NextApiRequest, NextApiResponse } from 'next';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { BACKEND_API } from '../../../src/lib/axios';
import { DeleteNotificationResponse } from '../../../src/requests/admin-notifications/response-transformer';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== apiMethods.DELETE) {
    return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    const { id } = req.query;
    if (!id || typeof id !== 'string') {
      return res.status(HTTP_CODE.BAD_REQUEST).json({
        success: false,
        message: 'Notification ID is required',
      });
    }

    const { token } = await getJwt(req);

    const { data } = await BACKEND_API(req).delete(API_ENDPOINT.adminNotifications.delete(id), {
      headers: { authorization: token },
    });

    return createApiResponse(
      res,
      DeleteNotificationResponse,
      {
        success: data.success,
        message: data.message,
        data: data.data || {},
      },
    );
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}

export default handler;
