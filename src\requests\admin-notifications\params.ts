/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-underscore-dangle */
import { NextApiRequest } from 'next';
import { AdminNotificationType, AdminNotificationPriority } from './types';

// This map is used to convert front-end keys to back-end keys for the sort parameter
const sortKeysMapping = new Map<string, string>([
  ['title', 'title'],
  ['message', 'message'],
  ['notificationType', 'notification_type'],
  ['priority', 'priority'],
  ['read', 'read'],
  ['createdAt', 'created_at'],
  ['updatedAt', 'updated_at'],
  ['readAt', 'read_at'],
]);

// eslint-disable-next-line complexity
export const returnAdminNotificationListParams = (req: NextApiRequest) => {
  const params = req.query;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const cleanParams: Record<string, any> = {};

  // Handle pagination - both frontend and backend use 1-based pagination
  // Always set page parameter with default value of 1
  const pageNum = params.page ? parseInt(params.page as string, 10) : 1;
  cleanParams.page = Number.isNaN(pageNum) ? 1 : Math.max(1, pageNum);

  // Always set limit parameter with default value of 20
  let limitNum = 20;
  if (params.limit) {
    limitNum = parseInt(params.limit as string, 10);
  } else if (params.pageSize) {
    limitNum = parseInt(params.pageSize as string, 10);
  }
  cleanParams.limit = Number.isNaN(limitNum) ? 20 : limitNum;

  // Handle search
  if (params.search) {
    cleanParams.search = params.search;
  }

  // Handle filters - map frontend parameter names to backend parameter names
  if (params.type && Object.values(AdminNotificationType).includes(params.type as AdminNotificationType)) {
    // Validate notification type
    cleanParams.type = params.type;
  }

  if (params.priority && Object.values(AdminNotificationPriority).includes(params.priority as AdminNotificationPriority)) {
    // Validate priority
    cleanParams.priority = params.priority;
  }

  if (params.read !== undefined) {
    // Convert string to boolean
    if (params.read === 'true') {
      cleanParams.read = true;
    } else if (params.read === 'false') {
      cleanParams.read = false;
    }
  }

  // Handle date filters
  if (params.fromDate) {
    cleanParams.from_date = params.fromDate;
  }
  if (params.from_date) {
    cleanParams.from_date = params.from_date;
  }

  if (params.toDate) {
    cleanParams.to_date = params.toDate;
  }
  if (params.to_date) {
    cleanParams.to_date = params.to_date;
  }

  // Handle shipment ID filter
  if (params.shipmentId) {
    cleanParams.shipment_id = params.shipmentId;
  }
  if (params.shipment_id) {
    cleanParams.shipment_id = params.shipment_id;
  }

  // Handle sorting - backend expects 'sort' and 'order' parameters
  if (params.sort) {
    const sortParam = params.sort as string;
    if (sortParam.includes(':')) {
      const [field, direction] = sortParam.split(':');
      // Map frontend field names to backend field names
      const mappedField = sortKeysMapping.get(field) || field;
      cleanParams.sort = mappedField;
      cleanParams.order = direction;
    } else {
      // Fallback for simple field names
      const mappedField = sortKeysMapping.get(sortParam) || sortParam;
      cleanParams.sort = mappedField;
      cleanParams.order = 'asc';
    }
  }

  // Handle notification ID for detail requests
  if (params.id) {
    cleanParams.id = params.id;
  }

  // Add cache buster for fresh data
  if (params._t) {
    cleanParams._t = params._t;
  }

  return cleanParams;
};

// Helper function to transform sort parameter using the mapping
export const transformSortParam = (sort: string): string => {
  if (!sort) return sort;

  // Handle descending sort (starts with -)
  const isDescending = sort.startsWith('-');
  const sortKey = isDescending ? sort.substring(1) : sort;

  // Map the sort key if it exists in our mapping
  const mappedKey = sortKeysMapping.get(sortKey) || sortKey;

  // Return with proper prefix
  return isDescending ? `-${mappedKey}` : mappedKey;
};

// Export the mapping for external use
export { sortKeysMapping };
