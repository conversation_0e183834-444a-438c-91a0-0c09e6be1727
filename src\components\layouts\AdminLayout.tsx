/* eslint-disable sonarjs/no-duplicate-string */
import { useState, ReactNode } from 'react';
import {
  AppShell,
  Burger,
  Group,
  NavLink,
  Text,
  useMantineColorScheme,
  ActionIcon,
  Title,
  Avatar,
  Menu,
  Badge,
  Divider,
} from '@mantine/core';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  IconDashboard,
  IconUsers,
  IconUserCircle,
  IconLogout,
  IconSun,
  IconMoonStars,
  IconChevronDown,
  IconSettings,
  IconLock,
  IconPackage,
  IconChartBar,
  IconFileExport,
  IconShield,
  IconEye,
  IconBell,
} from '@tabler/icons-react';
import { useAdminAuthContext } from '../../contexts/AdminAuthContext';
import { PermissionGuard } from '../auth/PermissionGuard';
import { Permission } from '../../utils/permissions';
import { NotificationDropdown } from '../notifications';
import { AdminRole } from '../../types/admin.types';

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [opened, setOpened] = useState(false);
  const { colorScheme, toggleColorScheme } = useMantineColorScheme();
  const { admin, logout } = useAdminAuthContext();
  const router = useRouter();

  const navigationItems = [
    {
      label: 'Dashboard',
      icon: IconDashboard,
      href: '/dashboard',
      permission: Permission.VIEW_DASHBOARD,
    },
    {
      label: 'User Management',
      icon: IconUsers,
      href: '/users',
      permission: Permission.VIEW_USERS,
    },
    {
      label: 'Shipment Management',
      icon: IconPackage,
      href: '/shipments',
      permission: Permission.VIEW_USERS,
    },
    {
      label: 'Reports',
      icon: IconChartBar,
      href: '/reports',
      permission: Permission.VIEW_USERS,
    },
    {
      label: 'Export Center',
      icon: IconFileExport,
      href: '/export',
      permission: Permission.VIEW_USERS,
    },
    {
      label: 'Settings Management',
      icon: IconSettings,
      href: '/setting',
      permission: Permission.VIEW_USERS,
    },
    {
      label: 'Audit Logs',
      icon: IconEye,
      href: '/audit-logs',
      permission: Permission.VIEW_AUDIT_LOGS,
    },
    {
      label: 'Admin Notifications',
      icon: IconBell,
      href: '/admin-notifications',
      permission: Permission.VIEW_DASHBOARD, // Using basic permission for now
    },
    {
      label: 'Security Monitoring',
      icon: IconShield,
      href: '/security',
      permission: Permission.VIEW_SECURITY_MONITORING,
    },
    // {
    //   label: 'Notifications',
    //   icon: IconBell,
    //   href: '/notifications',
    //   permission: Permission.VIEW_NOTIFICATIONS,
    // },
  ];

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return router.pathname === '/dashboard' || router.pathname === '/dashboard/index';
    }
    return router.pathname.startsWith(href);
  };

  const getRoleColor = (role: AdminRole | undefined) => {
    if (!role) return 'gray';

    switch (role) {
      case AdminRole.SUPER_ADMIN:
        return 'purple';
      case AdminRole.ADMIN:
        return 'blue';
      default:
        return 'gray';
    }
  };

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 250,
        breakpoint: 'lg',
        collapsed: { mobile: !opened },
      }}
      padding="md"
    >
      {/* Header */}
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger
              opened={opened}
              onClick={() => setOpened(!opened)}
              hiddenFrom="lg"
              size="sm"
            />
            <Title order={3} c="blue">
              NAQALAT Admin
            </Title>
          </Group>

          <Group>
            {/* Notification Bell */}
            <NotificationDropdown />

            <ActionIcon
              variant="outline"
              color={colorScheme === 'dark' ? 'yellow' : 'blue'}
              onClick={() => toggleColorScheme()}
              title="Toggle color scheme"
            >
              {colorScheme === 'dark' ? (
                <IconSun size="1.1rem" />
              ) : (
                <IconMoonStars size="1.1rem" />
              )}
            </ActionIcon>

            {admin && (
              <Menu shadow="md" width={200}>
                <Menu.Target>
                  <Group style={{ cursor: 'pointer' }}>
                    <Avatar size="sm" radius="xl">
                      {admin.name?.charAt(0).toUpperCase() || 'A'}
                    </Avatar>
                    <div style={{ flex: 1 }}>
                      <Text size="sm" fw={500}>
                        {admin.name || 'Admin'}
                      </Text>
                      <Badge size="xs" color={getRoleColor(admin.role)}>
                        {admin.role?.replace('_', ' ') || 'Admin'}
                      </Badge>
                    </div>
                    <IconChevronDown size="0.9rem" />
                  </Group>
                </Menu.Target>

                <Menu.Dropdown>
                  <Menu.Label>Account</Menu.Label>
                  <Menu.Item
                    leftSection={<IconUserCircle size="0.9rem" />}
                    component={Link}
                    href="/profile"
                  >
                    Profile Settings
                  </Menu.Item>
                  <Menu.Item
                    leftSection={<IconLock size="0.9rem" />}
                    component={Link}
                    href="/profile/change-password"
                  >
                    Change Password
                  </Menu.Item>

                  <PermissionGuard permission={Permission.VIEW_SYSTEM_SETTINGS}>
                    <Menu.Divider />
                    <Menu.Label>System</Menu.Label>
                    <Menu.Item
                      leftSection={<IconSettings size="0.9rem" />}
                      component={Link}
                      href="/settings"
                    >
                      System Settings
                    </Menu.Item>
                  </PermissionGuard>

                  <Menu.Divider />
                  <Menu.Item
                    leftSection={<IconLogout size="0.9rem" />}
                    color="red"
                    onClick={logout}
                  >
                    Logout
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            )}
          </Group>
        </Group>
      </AppShell.Header>

      {/* Navbar */}
      <AppShell.Navbar p="md">
        <div>
          {navigationItems.map((item) => (
            <PermissionGuard key={item.href} permission={item.permission}>
              <NavLink
                component={Link}
                href={item.href}
                label={item.label}
                leftSection={<item.icon size="1rem" />}
                active={isActiveRoute(item.href)}
                onClick={() => setOpened(false)}
                mb="xs"
              />
            </PermissionGuard>
          ))}
        </div>

        {/* User Info at Bottom */}
        {admin && (
          <div style={{ marginTop: 'auto', paddingTop: '1rem' }}>
            <Divider mb="md" />
            <Group>
              <Avatar size="sm" radius="xl">
                {admin.name?.charAt(0).toUpperCase() || 'A'}
              </Avatar>
              <div style={{ flex: 1 }}>
                <Text size="xs" fw={500}>
                  {admin.name || 'Admin'}
                </Text>
                <Text size="xs" c="dimmed">
                  {admin.email || '<EMAIL>'}
                </Text>
                <Badge size="xs" color={getRoleColor(admin.role)} mt={2}>
                  {admin.role?.replace('_', ' ') || 'Admin'}
                </Badge>
              </div>
            </Group>
          </div>
        )}
      </AppShell.Navbar>

      {/* Main Content */}
      <AppShell.Main>{children}</AppShell.Main>
    </AppShell>
  );
}
