// /* eslint-disable @typescript-eslint/no-explicit-any */
// /* eslint-disable react/no-array-index-key */
// /* eslint-disable max-lines */
// import React, { useState, useEffect } from 'react';
// import {
//   Container,
//   Title,
//   Paper,
//   Group,
//   Button,
//   Stack,
//   Text,
//   TextInput,
//   Textarea,
//   Select,
//   Switch,
//   Alert,
//   LoadingOverlay,
//   Tabs,
//   Card,
//   Badge,
//   Divider,
//   ActionIcon,
//   Tooltip,
// } from '@mantine/core';
// import {
//   IconArrowLeft,
//   IconDeviceFloppy,
//   IconEye,
//   IconPlus,
//   IconTrash,
//   IconInfoCircle,
// } from '@tabler/icons-react';
// import { useRouter } from 'next/router';
// import { useForm } from '@mantine/form';
// import { notifications } from '@mantine/notifications';
// import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// import AdminLayout from '../../../src/components/layouts/AdminLayout';
// import { PermissionGuard } from '../../../src/components/auth/PermissionGuard';
// import { Permission } from '../../../src/utils/permissions';
// import { NotificationType } from '../../../src/types/notification.types';
// import {
//   getTemplateQuery,
//   createTemplateMutationWithInvalidation,
//   updateTemplateMutationWithInvalidation,
//   previewTemplateMutation,
// } from '../../../src/requests/notifications/template-calls';

// interface TemplateVariable {
//   name: string;
//   description: string;
//   required: boolean;
//   defaultValue?: string;
//   type: 'text' | 'number' | 'date' | 'boolean';
// }

// interface TemplateFormData {
//   name: string;
//   description: string;
//   subject: string;
//   content: string;
//   type: NotificationType;
//   variables: TemplateVariable[];
//   isActive: boolean;
// }

// export default function TemplateEditorPage() {
//   const router = useRouter();
//   const queryClient = useQueryClient();
//   const { id } = router.query;
//   const isNew = id === 'new';
//   const [activeTab, setActiveTab] = useState<string>('editor');
//   const [previewData, setPreviewData] = useState<any>(null);

//   const form = useForm<TemplateFormData>({
//     initialValues: {
//       name: '',
//       description: '',
//       subject: '',
//       content: '',
//       type: NotificationType.SYSTEM_ALERT,
//       variables: [],
//       isActive: true,
//     },
//     validate: {
//       name: (v) => (v.length < 3 ? 'templates.nameRequired' : null),
//       subject: (v) => (v.length < 3 ? 'templates.subjectRequired' : null),
//       content: (v) => (v.length < 10 ? 'templates.contentRequired' : null),
//     },
//   });

//   const { data: templateData, isLoading } = useQuery({
//     ...getTemplateQuery({ id: id as string }),
//     enabled: !isNew && !!id,
//   });

//   const createMutation = useMutation(createTemplateMutationWithInvalidation(queryClient));
//   const updateMutation = useMutation(updateTemplateMutationWithInvalidation(queryClient));
//   const previewMutation = useMutation(previewTemplateMutation());

//   useEffect(() => {
//     if (templateData?.data) {
//       const t = templateData.data;
//       form.setValues({
//         name: t.name,
//         description: t.description || '',
//         subject: t.subject,
//         content: t.content,
//         type: t.type,
//         variables: t.variables,
//         isActive: t.isActive,
//       });
//     }
//   // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [templateData]);

//   const notificationTypeOptions = [
//     { value: NotificationType.SYSTEM_ALERT, label: 'notificationTypesOptions.SYSTEM_ALERT' },
//     { value: NotificationType.SHIPMENT_DELIVERED, label: 'notificationTypesOptions.SHIPMENT_DELIVERED' },
//     { value: NotificationType.DELIVERY_REMINDER, label: 'notificationTypesOptions.DELIVERY_REMINDER' },
//   ];
//   const variableTypeOptions = [
//     { value: 'text', label: 'templates.textType' },
//     { value: 'number', label: 'templates.numberType' },
//     { value: 'date', label: 'templates.dateType' },
//     { value: 'boolean', label: 'templates.booleanType' },
//   ];

//   const handleSave = async () => {
//     if (form.validate().hasErrors) return;

//     try {
//       if (isNew) {
//         await createMutation.mutateAsync(form.values);
//         notifications.show({ title: 'success', message: 'templates.templateCreated', color: 'green' });
//       } else {
//         await updateMutation.mutateAsync({ id: id as string, ...form.values });
//         notifications.show({ title: 'success', message: 'templates.templateUpdated', color: 'green' });
//       }
//       router.push('/notifications/templates');
//     } catch {
//       notifications.show({
//         title: 'error',
//         message: isNew ? 'templates.createFailed' : 'templates.updateFailed',
//         color: 'red',
//       });
//     }
//   };

//   const handlePreview = async () => {
//     if (form.validate().hasErrors) return;

//     try {
//       if (isNew) {
//         setPreviewData({
//           renderedSubject: form.values.subject,
//           renderedContent: form.values.content,
//           missingVariables: [],
//         });
//       } else {
//         const result = await previewMutation.mutateAsync({
//           id: id as string,
//           variables: {},
//         });
//         setPreviewData(result.data);
//       }
//       setActiveTab('preview');
//     } catch {
//       notifications.show({ title: 'error', message: 'templates.previewFailed', color: 'red' });
//     }
//   };

//   const handleBack = () => router.push('/notifications/templates');
//   const addVariable = () => form.setFieldValue('variables', [
//     ...form.values.variables,
//     {
//       name: '', description: '', required: false, defaultValue: '', type: 'text',
//     },
//   ]);
//   const removeVariable = (idx: number) => form.setFieldValue(
//     'variables',
//     form.values.variables.filter((_, i) => i !== idx),
//   );
//   const updateVariable = (idx: number, field: keyof TemplateVariable, value: any) => {
//     const vars = [...form.values.variables];
//     vars[idx] = { ...vars[idx], [field]: value };
//     form.setFieldValue('variables', vars);
//   };

//   return (
//     <AdminLayout>
//       <PermissionGuard permission={Permission.MANAGE_NOTIFICATION_TEMPLATES}>
//         <Container size="lg" py="md">
//           <Stack gap="lg">
//             {/* Header */}
//             <Group justify="space-between">
//               <div>
//                 <Title order={1} size="h2">
//                   {isNew ? 'templates.createTemplate' : 'templates.editTemplate'}
//                 </Title>
//                 <Text c="dimmed" size="sm" mt={4}>
//                   {isNew
//                     ? 'templates.createTemplateDescription'
//                     : 'templates.editTemplateDescription'}
//                 </Text>
//               </div>
//               <Group gap="sm">
//                 <Button
//                   variant="light"
//                   leftSection={<IconArrowLeft size="1rem" />}
//                   onClick={handleBack}
//                 >
//                   management.back
//                 </Button>
//                 <Button
//                   variant="light"
//                   leftSection={<IconEye size="1rem" />}
//                   onClick={handlePreview}
//                 >
//                   management.preview
//                 </Button>
//                 <Button
//                   leftSection={<IconDeviceFloppy size="1rem" />}
//                   onClick={handleSave}
//                 >
//                   {isNew ? 'templates.create' : 'templates.save'}
//                 </Button>
//               </Group>
//             </Group>

//             {/* Form */}
//             <Paper withBorder pos="relative">
//               <LoadingOverlay visible={isLoading} />
//               <Tabs value={activeTab} onChange={setActiveTab}>
//                 <Tabs.List>
//                   <Tabs.Tab value="editor">templates.editor</Tabs.Tab>
//                   <Tabs.Tab value="variables">templates.variables</Tabs.Tab>
//                   <Tabs.Tab value="preview">templates.preview</Tabs.Tab>
//                 </Tabs.List>

//                 {/* Editor */}
//                 <Tabs.Panel value="editor" p="md">
//                   <Stack gap="md">
//                     <Group grow>
//                       <TextInput
//                         label="templates.templateName"
//                         placeholder="templates.enterTemplateName"
//                         required
//                         {...form.getInputProps('name')}
//                       />
//                       <Select
//                         label="templates.notificationType"
//                         data={notificationTypeOptions}
//                         required
//                         {...form.getInputProps('type')}
//                       />
//                     </Group>
//                     <TextInput
//                       label="templates.description"
//                       placeholder="templates.enterDescription"
//                       {...form.getInputProps('description')}
//                     />
//                     <TextInput
//                       label="templates.subject"
//                       placeholder="templates.enterSubject"
//                       required
//                       {...form.getInputProps('subject')}
//                     />
//                     <Textarea
//                       label="templates.content"
//                       placeholder="templates.enterContent"
//                       required
//                       minRows={6}
//                       {...form.getInputProps('content')}
//                     />
//                     <Switch
//                       label="templates.activeTemplate"
//                       description="templates.activeTemplateDescription"
//                       {...form.getInputProps('isActive', { type: 'checkbox' })}
//                     />
//                   </Stack>
//                 </Tabs.Panel>

//                 {/* Variables */}
//                 <Tabs.Panel value="variables" p="md">
//                   <Stack gap="md">
//                     <Group justify="space-between">
//                       <div>
//                         <Text fw={500}>templates.templateVariables</Text>
//                         <Text size="sm" c="dimmed">
//                           templates.variablesDescription
//                         </Text>
//                       </div>
//                       <Button leftSection={<IconPlus size="1rem" />} onClick={addVariable} size="sm">
//                         templates.addVariable
//                       </Button>
//                     </Group>

//                     {form.values.variables.length === 0 ? (
//                       <Alert icon={<IconInfoCircle size="1rem" />} color="blue">
//                         templates.noVariables
//                       </Alert>
//                     ) : (
//                       <Stack gap="sm">
//                         {form.values.variables.map((variable, index) => (
//                           <Card key={index} withBorder>
//                             <Group justify="space-between" mb="sm">
//                               <Text fw={500} size="sm">
//                                 {`templates.variable ${index + 1}`}
//                               </Text>
//                               <Tooltip label="templates.removeVariable">
//                                 <ActionIcon
//                                   color="red"
//                                   variant="light"
//                                   size="sm"
//                                   onClick={() => removeVariable(index)}
//                                 >
//                                   <IconTrash size="0.8rem" />
//                                 </ActionIcon>
//                               </Tooltip>
//                             </Group>

//                             <Group grow>
//                               <TextInput
//                                 label="templates.variableName"
//                                 placeholder="variable_name"
//                                 value={variable.name}
//                                 onChange={(e) => updateVariable(index, 'name', e.target.value)}
//                               />
//                               <Select
//                                 label="templates.variableType"
//                                 data={variableTypeOptions}
//                                 value={variable.type}
//                                 onChange={(value) => updateVariable(index, 'type', value!)}
//                               />
//                             </Group>

//                             <TextInput
//                               label="templates.variableDescription"
//                               placeholder="templates.enterVariableDescription"
//                               value={variable.description}
//                               onChange={(e) => updateVariable(index, 'description', e.target.value)}
//                               mt="sm"
//                             />

//                             <Group mt="sm">
//                               <Switch
//                                 label="templates.required"
//                                 checked={variable.required}
//                                 onChange={(e) => updateVariable(index, 'required', e.target.checked)}
//                               />
//                               <TextInput
//                                 label="templates.defaultValue"
//                                 placeholder="templates.enterDefaultValue"
//                                 value={variable.defaultValue || ''}
//                                 onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
//                                 style={{ flex: 1 }}
//                               />
//                             </Group>
//                           </Card>
//                         ))}
//                       </Stack>
//                     )}
//                   </Stack>
//                 </Tabs.Panel>

//                 {/* Preview */}
//                 <Tabs.Panel value="preview" p="md">
//                   {previewData ? (
//                     <Stack gap="md">
//                       <Card withBorder>
//                         <Stack gap="sm">
//                           <Group justify="space-between">
//                             <Text fw={600} size="lg">
//                               templates.previewTitle
//                             </Text>
//                             <Badge color="blue">{form.values.type}</Badge>
//                           </Group>
//                           <Divider />
//                           <div>
//                             <Text fw={500} size="sm" c="dimmed" mb={4}>
//                               templates.subject:
//                             </Text>
//                             <Text>{previewData.renderedSubject}</Text>
//                           </div>
//                           <div>
//                             <Text fw={500} size="sm" c="dimmed" mb={4}>
//                               templates.content:
//                             </Text>
//                             <Text style={{ whiteSpace: 'pre-wrap' }}>
//                               {previewData.renderedContent}
//                             </Text>
//                           </div>
//                         </Stack>
//                       </Card>

//                       {previewData.missingVariables?.length > 0 && (
//                         <Alert color="yellow">
//                           <Text fw={500} mb="xs">
//                             templates.missingVariables:
//                           </Text>
//                           <Text size="sm">
//                             {previewData.missingVariables.join(', ')}
//                           </Text>
//                         </Alert>
//                       )}
//                     </Stack>
//                   ) : (
//                     <Alert icon={<IconInfoCircle size="1rem" />} color="blue">
//                       templates.clickPreviewToSee
//                     </Alert>
//                   )}
//                 </Tabs.Panel>
//               </Tabs>
//             </Paper>
//           </Stack>
//         </Container>
//       </PermissionGuard>
//     </AdminLayout>
//   );
// }

// TemplateEditorPage.auth = true;
