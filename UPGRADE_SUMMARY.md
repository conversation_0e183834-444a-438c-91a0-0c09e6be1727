# Reports & Export API Schema Upgrade Summary

## Overview
Updated the frontend API handlers, schemas, and UI pages to match the backend API specifications provided. The changes ensure type safety, proper validation, and alignment with the backend contract.

## Key Changes Made

### 1. Request Schema Updates

#### Reports Request Schemas (`src/requests/reports/request-transformer.ts`)
- **User Reports**: Added strict enum validation for `user_type` field
  - Now accepts only: `'CUSTOMER' | 'ACCESS_OPERATOR' | 'CAR_OPERATOR'`
- **Shipment Reports**: Added strict enum validation for `status` field  
  - Now accepts only: `'PENDING' | 'ASSIGNED' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED' | 'EXPIRED'`
- **Date Filters**: Removed `.datetime()` validation to allow flexible date string formats

#### Export Request Schemas (`src/requests/export/request-transformer.ts`)
- Created separate schemas for users and shipments exports
- Made `filters` field optional instead of required with default
- Updated download request to accept any string ID (not just UUID)

### 2. Response Schema Updates

#### Reports Response Schemas (`src/requests/reports/response-transformer.ts`)
- **User Reports**: 
  - Added strict enums for user types and statuses
  - Changed user ID validation to require UUID format
  - Updated filter date fields to accept any string format
- **Shipment Reports**:
  - Added strict enums for shipment statuses
  - Changed shipment ID validation to require UUID format
  - Made customer, originAO, and destAO fields nullable
  - Removed delivery rate percentage constraints
- **System Reports**:
  - Added UUID validation for error IDs
  - Added optional `details` field to recent errors

#### Export Response Schemas (`src/requests/export/response-transformer.ts`)
- Created separate response schemas for users and shipments
- Updated export job data to match backend format:
  - ID field uses timestamp (not UUID)
  - Added literal type constraints for export types
  - Status is always 'processing' for new exports
  - Created_by field requires UUID format
- Updated download response to handle both processing and completed states

### 3. API Handler Updates

#### Input Validation
- Added Zod schema validation for all request parameters and bodies
- Implemented proper error handling for validation failures
- Added specific error messages for validation errors

#### Updated Handlers
- `pages/api/reports/users.ts`: Added query parameter validation
- `pages/api/reports/shipments.ts`: Added query parameter validation  
- `pages/api/export/users.ts`: Added request body validation, updated response schema
- `pages/api/export/shipments.ts`: Added request body validation, updated response schema
- `pages/api/export/[id]/download.ts`: Added ID parameter validation

### 4. Frontend Pages Updates

#### Reports Pages
- **`pages/reports/shipments.tsx`**: 
  - Added missing status options (ASSIGNED, EXPIRED)
  - Updated status color mapping for all statuses
  - Added statistics cards for all status types
  - Updated grid layout to accommodate additional status cards

#### Export System
- **`src/requests/export/types.ts`**: Added new specific types for users and shipments exports
- **`src/requests/export/call.ts`**: Updated mutation functions to use specific request types

### 5. Type System Updates

#### New Types Added
- `ExportUsersRequest` and `ExportUsersResponse`
- `ExportShipmentsRequest` and `ExportShipmentsResponse`
- `ExportUsersJobData` and `ExportShipmentsJobData`
- Updated export mutation functions with proper typing

### 6. Error Handling Improvements
- Added specific handling for Zod validation errors
- Return 400 Bad Request for validation failures
- Include validation error details in response
- Maintain backward compatibility for other error types

## Backend Schema Compliance

The updated schemas now fully comply with the backend API specifications:

### User Reports Query
```typescript
{
  format?: 'json' | 'csv' | 'excel'
  date_from?: string
  date_to?: string  
  user_type?: 'CUSTOMER' | 'ACCESS_OPERATOR' | 'CAR_OPERATOR'
}
```

### Shipment Reports Query
```typescript
{
  status?: 'PENDING' | 'ASSIGNED' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED' | 'EXPIRED'
  date_from?: string
  date_to?: string
}
```

### Export Requests
```typescript
{
  format: 'csv' | 'excel' | 'json'
  filters?: Record<string, any>
}
```

## UI Improvements

### Shipment Reports Page
- Added support for all 6 shipment statuses (including ASSIGNED and EXPIRED)
- Updated statistics dashboard to show all status types
- Improved status color coding and visual indicators
- Better responsive grid layout for status cards

### Export System
- Enhanced type safety for export operations
- Separate handling for users vs shipments exports
- Improved error handling and user feedback

## Breaking Changes
- Query parameters for reports now have strict enum validation
- Export response format has changed to match backend structure
- Some fields that were previously strings now require specific enum values
- Date fields no longer require ISO datetime format (more flexible)
- Shipment status filters now include ASSIGNED and EXPIRED options

## Migration Notes
- Frontend components using these APIs may need updates if they pass invalid enum values
- Export response handling should be updated to use the new schema structure
- Error handling should account for new validation error format
- UI components now support all backend-defined status values

## TypeScript Fixes Applied

### Frontend Type Safety Improvements
- **User Reports Page**: Updated to use `UserType` enum from `src/types/admin.types.ts`
- **Shipment Reports Page**: Updated to use `ShipmentStatus` enum from `src/types/admin.types.ts`
- **Reports Index Page**: Updated status comparisons to use proper enum values
- **Export Types**: Enhanced type definitions for separate users and shipments export operations

### Resolved TypeScript Errors
- Fixed "string is not assignable to enum type" errors in query parameters
- Proper type casting for Select component onChange handlers
- Consistent use of existing enum definitions across all components
- Eliminated hardcoded string literals in favor of enum constants

## Testing Recommendations
1. Test all report endpoints with valid and invalid query parameters
2. Test export endpoints with various filter combinations
3. Verify error responses for validation failures
4. Test download endpoint with both valid and invalid export IDs
5. Test UI filters with all available enum values
6. Verify statistics display correctly for all status types
7. Confirm TypeScript compilation passes without errors
8. Test filter selections work correctly with enum values