/* eslint-disable max-lines */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { z } from 'zod';
import { ApiResponseSchema, PaginationSchema } from '../common';

// Audit Log Schema
export const AuditLogSchema = z.object({
  id: z.string().uuid(),
  action: z.string(),
  user_id: z.string().uuid().nullable(),
  admin_id: z.string().uuid().nullable(),
  details: z.record(z.any()),
  created_at: z.string().datetime(),
});

// Login Attempt Schema
export const LoginAttemptSchema = z.object({
  id: z.string().uuid(),
  admin_id: z.string().uuid(),
  ip_address: z.string(),
  successful: z.boolean(),
  attempted_at: z.string().datetime(),
});

// Security Event Schema
export const SecurityEventSchema = z.object({
  id: z.string().uuid(),
  event_type: z.string(),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  description: z.string(),
  created_at: z.string().datetime(),
});

// Response Schemas - Flexible schemas that accept the normalized response
export const AuditLogsResponse = ApiResponseSchema(
  z.object({
    logs: z.array(AuditLogSchema),
    pagination: PaginationSchema,
  }),
);

export const LoginAttemptsResponse = ApiResponseSchema(
  z.object({
    attempts: z.array(LoginAttemptSchema),
  }),
);

export const SecurityEventsResponse = ApiResponseSchema(
  z.object({
    events: z.array(SecurityEventSchema),
  }),
);

export const LockUserResponse = ApiResponseSchema(z.object({}));
export const UnlockUserResponse = ApiResponseSchema(z.object({}));

// Type definitions
type AuditLog = z.infer<typeof AuditLogSchema>;

// Helper function to validate audit logs array
const validateAuditLogsArray = (data: unknown[]): AuditLog[] => {
  try {
    return data.map((item) => AuditLogSchema.parse(item));
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.warn('Failed to validate audit logs array:', error);
    }
    return [];
  }
};

// Helper functions to normalize responses
export const normalizeAuditLogsResponse = (data: unknown) => {
  // If it's a direct array response
  if (Array.isArray(data)) {
    try {
      // Validate the array items
      const validatedLogs = data.map((item) => AuditLogSchema.parse(item));
      return {
        success: true,
        message: 'Success',
        data: {
          logs: validatedLogs,
          pagination: {
            page: 0, // 0-based for frontend
            limit: data.length,
            total: data.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
    } catch (error) {
      // If validation fails, return empty response
      // Log error in development only
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.warn('Failed to validate audit logs array:', error);
      }
      return {
        success: true,
        message: 'Success',
        data: {
          logs: [],
          pagination: {
            page: 0,
            limit: 20,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
    }
  }

  // If it's a wrapped response with data as array and pagination at root level (backend format)
  if (data && typeof data === 'object' && 'data' in data && 'pagination' in data && Array.isArray((data as Record<string, unknown>).data)) {
    const response = data as {
      success: boolean;
      message: string;
      data: unknown[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext?: boolean;
        hasPrev?: boolean;
      };
    };

    const validatedLogs = validateAuditLogsArray(response.data);
    return {
      success: response.success,
      message: response.message,
      data: {
        logs: validatedLogs,
        pagination: {
          page: Math.max(0, response.pagination.page - 1), // Convert to 0-based
          limit: response.pagination.limit,
          total: response.pagination.total,
          totalPages: response.pagination.totalPages,
          hasNext: response.pagination.hasNext ?? false,
          hasPrev: response.pagination.hasPrev ?? false,
        },
      },
    };
  }

  // If it's already a wrapped response with correct structure, ensure pagination is 0-based
  if (data && typeof data === 'object' && 'data' in data) {
    const response = data as {
      success: boolean;
      message: string;
      data: {
        logs?: unknown[];
        pagination?: {
          page: number;
          limit: number;
          total: number;
          totalPages: number;
          hasNext: boolean;
          hasPrev: boolean;
        };
      };
    };

    if (response.data.pagination && typeof response.data.pagination.page === 'number') {
      response.data.pagination.page = Math.max(0, response.data.pagination.page - 1);
    }

    return response;
  }

  // Fallback: wrap any other response
  return {
    success: true,
    message: 'Success',
    data: {
      logs: [],
      pagination: {
        page: 0,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    },
  };
};

export const normalizeLoginAttemptsResponse = (data: unknown) => {
  if (Array.isArray(data)) {
    try {
      const validatedAttempts = data.map((item) => LoginAttemptSchema.parse(item));
      return {
        success: true,
        message: 'Success',
        data: {
          attempts: validatedAttempts,
        },
      };
    } catch (error) {
      // If validation fails, return empty response
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.warn('Failed to validate login attempts array:', error);
      }
      return {
        success: true,
        message: 'Success',
        data: {
          attempts: [],
        },
      };
    }
  }

  // If it's already wrapped with correct structure, return as is
  if (data && typeof data === 'object' && 'data' in data) {
    const response = data as {
      success: boolean;
      message: string;
      data: {
        attempts?: unknown[];
        loginAttempts?: unknown[];
        pagination?: unknown;
      };
    };

    // Handle case where backend returns 'loginAttempts' instead of 'attempts'
    if (response.data.loginAttempts && !response.data.attempts) {
      try {
        const validatedAttempts = response.data.loginAttempts.map((item) => LoginAttemptSchema.parse(item));
        return {
          ...response,
          data: {
            attempts: validatedAttempts,
            pagination: response.data.pagination,
          },
        };
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.warn('Failed to validate login attempts from loginAttempts field:', error);
        }
        return {
          ...response,
          data: {
            attempts: [],
            pagination: response.data.pagination,
          },
        };
      }
    }

    return response;
  }

  // Fallback
  return {
    success: true,
    message: 'Success',
    data: {
      attempts: [],
    },
  };
};

export const normalizeSecurityEventsResponse = (data: unknown) => {
  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('normalizeSecurityEventsResponse input:', JSON.stringify(data, null, 2));
  }

  if (Array.isArray(data)) {
    try {
      const validatedEvents = data.map((item) => SecurityEventSchema.parse(item));
      const result = {
        success: true,
        message: 'Success',
        data: {
          events: validatedEvents,
        },
      };
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('normalizeSecurityEventsResponse output (array case):', JSON.stringify(result, null, 2));
      }
      return result;
    } catch (error) {
      // If validation fails, return empty response
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.warn('Failed to validate security events array:', error);
      }
      return {
        success: true,
        message: 'Success',
        data: {
          events: [],
        },
      };
    }
  }

  // If it's already wrapped with correct structure, return as is
  if (data && typeof data === 'object' && 'data' in data) {
    const response = data as {
      success: boolean;
      message: string;
      data: {
        events?: unknown[];
        securityEvents?: unknown[];
        loginAttempts?: unknown[];
        pagination?: unknown;
      };
    };

    // Handle case where backend returns 'securityEvents' instead of 'events'
    if (response.data.securityEvents && !response.data.events) {
      try {
        const validatedEvents = response.data.securityEvents.map((item) => SecurityEventSchema.parse(item));
        const result = {
          ...response,
          data: {
            events: validatedEvents,
            pagination: response.data.pagination,
          },
        };
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('normalizeSecurityEventsResponse output (securityEvents case):', JSON.stringify(result, null, 2));
        }
        return result;
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.warn('Failed to validate security events from securityEvents field:', error);
        }
        return {
          ...response,
          data: {
            events: [],
            pagination: response.data.pagination,
          },
        };
      }
    }

    // Handle case where this might be a login attempts response mistakenly sent to events endpoint
    if (response.data.loginAttempts) {
      // Return empty events but preserve the structure
      const result = {
        ...response,
        data: {
          events: [],
          pagination: response.data.pagination,
        },
      };
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('normalizeSecurityEventsResponse output (loginAttempts case, returning empty events):', JSON.stringify(result, null, 2));
      }
      return result;
    }

    // If data has events field, validate and return
    if (response.data.events) {
      try {
        const validatedEvents = Array.isArray(response.data.events)
          ? response.data.events.map((item) => SecurityEventSchema.parse(item))
          : [];
        const result = {
          ...response,
          data: {
            events: validatedEvents,
            pagination: response.data.pagination,
          },
        };
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('normalizeSecurityEventsResponse output (events case):', JSON.stringify(result, null, 2));
        }
        return result;
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.warn('Failed to validate security events from events field:', error);
        }
        return {
          ...response,
          data: {
            events: [],
            pagination: response.data.pagination,
          },
        };
      }
    }

    // If we reach here, it's wrapped but doesn't have the expected fields
    const result = {
      ...response,
      data: {
        events: [],
        pagination: response.data.pagination,
      },
    };
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('normalizeSecurityEventsResponse output (fallback wrapped case):', JSON.stringify(result, null, 2));
    }
    return result;
  }

  // Fallback
  const result = {
    success: true,
    message: 'Success',
    data: {
      events: [],
    },
  };
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('normalizeSecurityEventsResponse output (final fallback):', JSON.stringify(result, null, 2));
  }
  return result;
};
