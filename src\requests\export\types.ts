import { z } from 'zod';
import {
  exportRequestSchema,
  exportUsersRequestSchema,
  exportShipmentsRequestSchema,
  downloadExportRequestSchema,
} from './request-transformer';
import {
  exportUsersJobDataSchema,
  exportShipmentsJobDataSchema,
  exportUsersResponseSchema,
  exportShipmentsResponseSchema,
  downloadExportResponseSchema,
  exportResponseSchema,
} from './response-transformer';

// Legacy types for backward compatibility
export type ExportRequest = z.infer<typeof exportRequestSchema>;
export type ExportResponse = z.infer<typeof exportResponseSchema>;

// New specific types
export type ExportUsersRequest = z.infer<typeof exportUsersRequestSchema>;
export type ExportShipmentsRequest = z.infer<typeof exportShipmentsRequestSchema>;
export type DownloadExportRequest = z.infer<typeof downloadExportRequestSchema>;

export type ExportUsersResponse = z.infer<typeof exportUsersResponseSchema>;
export type ExportShipmentsResponse = z.infer<typeof exportShipmentsResponseSchema>;
export type DownloadExportResponse = z.infer<typeof downloadExportResponseSchema>;

export type ExportUsersJobData = z.infer<typeof exportUsersJobDataSchema>;
export type ExportShipmentsJobData = z.infer<typeof exportShipmentsJobDataSchema>;
