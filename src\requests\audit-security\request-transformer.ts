import { GetAuditLogsQueryProps, LockUserRequest, UnlockUserRequest } from './types';

// Transform audit logs query parameters
export const transformAuditLogsParams = (props: GetAuditLogsQueryProps) => {
  const { filters, pagination } = props;

  return {
    page: (pagination?.page || 0) + 1, // Convert 0-based to 1-based pagination
    limit: pagination?.limit || pagination?.pageSize || 20,
    action: filters?.action,
    user_id: filters?.user_id,
    admin_id: filters?.admin_id,
    date_from: filters?.date_from,
    date_to: filters?.date_to,
  };
};

// Transform lock user request
export const transformLockUserRequest = (request: LockUserRequest) => ({
  user_id: request.user_id,
  reason: request.reason,
});

// Transform unlock user request
export const transformUnlockUserRequest = (request: UnlockUserRequest) => ({
  user_id: request.user_id,
  reason: request.reason,
});
