import { UseQueryOptions } from '@tanstack/react-query';
import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { AdminDashboardResponse } from './types';

const getAdminDashboardRequest = (): Promise<AdminDashboardResponse> => CLIENT_API.get(API_ENDPOINT.dashboard)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err.response?.data;
  });

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getDashboardStatsQuery = (): UseQueryOptions<AdminDashboardResponse, any> => ({
  queryKey: ['adminDashboard'],
  queryFn: getAdminDashboardRequest,
  refetchOnWindowFocus: false,
  retry: (_failureCount, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});
