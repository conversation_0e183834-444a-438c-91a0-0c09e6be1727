import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { getUsersQueryProps, UserActiveApproval } from './types';

enum queryKeys {
  users = 'users',
  user = 'user',
  update = 'update',
}

const getUsersRequest = (props: getUsersQueryProps) => {
  const {
    filters, sort, pagination,
  } = props;
  return CLIENT_API.get(API_ENDPOINT.users.list(), {
    params: {
      search: filters?.search,
      createdAtGte: filters?.createdAtGte,
      userType: filters?.userType,
      status: filters?.status,
      approvalStatus: filters?.approvalStatus,
      sort,
      limit: pagination?.limit || pagination?.pageSize,
      page: pagination?.page,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};
export const getUsersQuery = (props: getUsersQueryProps) => ({
  queryKey: [queryKeys.users, props?.filters, props?.pagination, props?.sort],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  queryFn: (params: any) => getUsersRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

const getUserRequest = (id: string) => CLIENT_API.get(API_ENDPOINT.users.byId(id))
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err.response.data;
  });
// get user request query
export const getUserQuery = (
  id: string,
  { isAuth }: { isAuth: boolean },
) => ({
  queryKey: [queryKeys.user, id],
  queryFn: () => getUserRequest(id),
  enabled: isAuth,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

const updateUserActiveApprovalRequest = ({
  body,
  id,
}: {
  id: string;
  body: UserActiveApproval;
}) => CLIENT_API.put(API_ENDPOINT.users.activationApproval(id), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });
export const updateUserActiveApprovalMutation = () => ({
  mutationKey: [queryKeys.update],
  mutationFn: updateUserActiveApprovalRequest,
});
