/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
/* eslint-disable max-lines */
import { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Text,
  NumberInput,
  Switch,
  LoadingOverlay,
  Alert,
  Grid,
  Card,
  Divider,
  Badge,
} from '@mantine/core';
import {
  IconSettings,
  IconEdit,
  IconDeviceFloppy,
  IconX,
  IconAlertCircle,
  IconShield,
  IconTruck,
  IconMapPin,
  IconClock,
  IconRefresh,
} from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import AdminLayout from '../../src/components/layouts/AdminLayout';
import { getSettingsQuery, updateSettingsMutation, updateSettingsResetMutation } from '../../src/requests/admin-settings/call';
import { UpdateSettings } from '../../src/requests/admin-settings/type';
import { SystemSettingsData } from '../../src/requests/admin-settings/response-transformer';

// Constants
const VALIDATION_MESSAGES = {
  MUST_BE_AT_LEAST_1: 'Must be at least 1',
  MUST_BE_AT_LEAST_1_HOUR: 'Must be at least 1 hour',
  MUST_BE_AT_LEAST_1_METER: 'Must be at least 1 meter',
  MIN_DISTANCE_ERROR: 'Minimum distance must be at least 0.1 km',
  MAX_DISTANCE_ERROR: 'Maximum distance cannot exceed 100 km',
  CANNOT_EXCEED_1000: 'Cannot exceed 1000',
  CANNOT_EXCEED_10000: 'Cannot exceed 10000',
  CANNOT_EXCEED_100: 'Cannot exceed 100',
  CANNOT_EXCEED_20: 'Cannot exceed 20',
  CANNOT_EXCEED_168_HOURS: 'Cannot exceed 168 hours (1 week)',
  CANNOT_EXCEED_1000_METERS: 'Cannot exceed 1000 meters',
};

// Validation helper functions
const validateMinDistance = (value?: number) => {
  if (!value || value < 0.1) return VALIDATION_MESSAGES.MIN_DISTANCE_ERROR;
  if (value > 100) return VALIDATION_MESSAGES.MAX_DISTANCE_ERROR;
  return null;
};

const validatePositiveNumber = (max: number, maxMessage: string) => (value?: number) => {
  if (!value || value < 1) return VALIDATION_MESSAGES.MUST_BE_AT_LEAST_1;
  if (value > max) return maxMessage;
  return null;
};

const validateGpsTolerance = (value?: number) => {
  if (!value || value < 1) return VALIDATION_MESSAGES.MUST_BE_AT_LEAST_1_METER;
  if (value > 1000) return VALIDATION_MESSAGES.CANNOT_EXCEED_1000_METERS;
  return null;
};

const validateReviewPeriod = (value?: number) => {
  if (!value || value < 1) return VALIDATION_MESSAGES.MUST_BE_AT_LEAST_1_HOUR;
  if (value > 168) return VALIDATION_MESSAGES.CANNOT_EXCEED_168_HOURS;
  return null;
};

export default function SettingsPage() {
  const [isEditMode, setIsEditMode] = useState(false);
  const queryClient = useQueryClient();

  // Fetch settings data
  const {
    data: settingsResponse,
    isLoading,
    error,
  } = useQuery(getSettingsQuery({}));

  const settings = settingsResponse?.data as SystemSettingsData;

  // Form for editing settings
  const form = useForm<UpdateSettings>({
    initialValues: {
      minDistanceKm: 0,
      maxShipmentsPerDay: 0,
      maxShipmentsPerUser: 0,
      maxPendingShipments: 0,
      requirePhotoProof: false,
      maxFailedLogins: 0,
      reviewPeriodHours: 0,
      enable2fa: false,
      gpsToleranceMeters: 0,
    },
    validate: {
      minDistanceKm: validateMinDistance,
      maxShipmentsPerDay: validatePositiveNumber(1000, VALIDATION_MESSAGES.CANNOT_EXCEED_1000),
      maxShipmentsPerUser: validatePositiveNumber(10000, VALIDATION_MESSAGES.CANNOT_EXCEED_10000),
      maxPendingShipments: validatePositiveNumber(100, VALIDATION_MESSAGES.CANNOT_EXCEED_100),
      maxFailedLogins: validatePositiveNumber(20, VALIDATION_MESSAGES.CANNOT_EXCEED_20),
      reviewPeriodHours: validateReviewPeriod,
      gpsToleranceMeters: validateGpsTolerance,
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    ...updateSettingsMutation(),
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Settings updated successfully',
        color: 'green',
      });
      setIsEditMode(false);
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
    onError: (updateError: Error) => {
      notifications.show({
        title: 'Error',
        message: updateError?.message || 'Failed to update settings',
        color: 'red',
      });
    },
  });

  // Reset mutation
  const resetMutation = useMutation({
    ...updateSettingsResetMutation(),
    onSuccess: () => {
      notifications.show({
        title: 'Success',
        message: 'Settings reset to defaults successfully',
        color: 'green',
      });
      setIsEditMode(false);
      queryClient.invalidateQueries({ queryKey: ['settings'] });
    },
    onError: (resetError: Error) => {
      notifications.show({
        title: 'Error',
        message: resetError?.message || 'Failed to reset settings',
        color: 'red',
      });
    },
  });

  // Event handlers
  const handleEdit = () => {
    if (settings) {
      form.setValues({
        minDistanceKm: settings.minDistanceKm,
        maxShipmentsPerDay: settings.maxShipmentsPerDay,
        maxShipmentsPerUser: settings.maxShipmentsPerUser,
        maxPendingShipments: settings.maxPendingShipments,
        requirePhotoProof: settings.requirePhotoProof,
        maxFailedLogins: settings.maxFailedLogins,
        reviewPeriodHours: settings.reviewPeriodHours,
        enable2fa: settings.enable2fa,
        gpsToleranceMeters: settings.gpsToleranceMeters,
      });
    }
    setIsEditMode(true);
  };

  const handleCancel = () => {
    form.reset();
    setIsEditMode(false);
  };

  const handleSave = (values: UpdateSettings) => {
    updateMutation.mutate(values);
  };

  const handleReset = () => {
    // eslint-disable-next-line no-alert
    const confirmed = window.confirm(
      'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
    );

    if (confirmed) {
      resetMutation.mutate();
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Container size="lg" py="xl">
          <LoadingOverlay visible />
        </Container>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Container size="lg" py="xl">
          <Alert
            icon={<IconAlertCircle size="1rem" />}
            title="Error Loading Settings"
            color="red"
            variant="light"
          >
            {(error as unknown as Error)?.message || 'Failed to load system settings'}
          </Alert>
        </Container>
      </AdminLayout>
    );
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between" align="center">
          <Group>
            <IconSettings size={32} />
            <Title order={1}>System Settings</Title>
          </Group>
          <Group>
            {!isEditMode ? (
              <Group>
                <Button
                  leftSection={<IconRefresh size="1rem" />}
                  onClick={handleReset}
                  variant="outline"
                  color="red"
                  loading={resetMutation.isPending}
                >
                  Reset to Defaults
                </Button>
                <Button
                  leftSection={<IconEdit size="1rem" />}
                  onClick={handleEdit}
                  variant="filled"
                >
                  Edit Settings
                </Button>
              </Group>
            ) : (
              <Group>
                <Button
                  variant="outline"
                  leftSection={<IconX size="1rem" />}
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  leftSection={<IconDeviceFloppy size="1rem" />}
                  onClick={() => form.onSubmit(handleSave)()}
                  loading={updateMutation.isPending}
                >
                  Save Changes
                </Button>
              </Group>
            )}
          </Group>
        </Group>

        {/* Settings Form/Display */}
        <form onSubmit={form.onSubmit(handleSave)}>
          <Grid>
            {/* Distance & Location Settings */}
            <Grid.Col span={12}>
              <Card withBorder shadow="sm" p="lg">
                <Group mb="md">
                  <IconMapPin size={20} />
                  <Text fw={600} size="lg">Distance & Location Settings</Text>
                </Group>
                <Divider mb="md" />
                <Grid>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="Minimum Distance (km)"
                        description="Minimum distance required between pickup and delivery"
                        placeholder="Enter minimum distance"
                        min={0.1}
                        max={100}
                        step={0.1}
                        decimalScale={1}
                        {...form.getInputProps('minDistanceKm')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>Minimum Distance (km)</Text>
                        <Text size="lg">
                          {settings?.minDistanceKm || 0}
                          {' '}
                          km
                        </Text>
                        <Text size="xs" c="dimmed">Minimum distance required between pickup and delivery</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="GPS Tolerance (meters)"
                        description="Acceptable GPS accuracy tolerance"
                        placeholder="Enter GPS tolerance"
                        min={1}
                        max={1000}
                        {...form.getInputProps('gpsToleranceMeters')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>GPS Tolerance (meters)</Text>
                        <Text size="lg">
                          {settings?.gpsToleranceMeters || 0}
                          {' '}
                          meters
                        </Text>
                        <Text size="xs" c="dimmed">Acceptable GPS accuracy tolerance</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                </Grid>
              </Card>
            </Grid.Col>

            {/* Shipment Limits */}
            <Grid.Col span={12}>
              <Card withBorder shadow="sm" p="lg">
                <Group mb="md">
                  <IconTruck size={20} />
                  <Text fw={600} size="lg">Shipment Limits</Text>
                </Group>
                <Divider mb="md" />
                <Grid>
                  <Grid.Col span={{ base: 12, md: 4 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="Max Shipments Per Day"
                        description="Maximum shipments allowed per day"
                        placeholder="Enter max per day"
                        min={1}
                        max={1000}
                        {...form.getInputProps('maxShipmentsPerDay')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>Max Shipments Per Day</Text>
                        <Text size="lg">{settings?.maxShipmentsPerDay || 0}</Text>
                        <Text size="xs" c="dimmed">Maximum shipments allowed per day</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 4 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="Max Shipments Per User"
                        description="Maximum shipments per user account"
                        placeholder="Enter max per user"
                        min={1}
                        max={10000}
                        {...form.getInputProps('maxShipmentsPerUser')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>Max Shipments Per User</Text>
                        <Text size="lg">{settings?.maxShipmentsPerUser || 0}</Text>
                        <Text size="xs" c="dimmed">Maximum shipments per user account</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 4 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="Max Pending Shipments"
                        description="Maximum pending shipments allowed"
                        placeholder="Enter max pending"
                        min={1}
                        max={100}
                        {...form.getInputProps('maxPendingShipments')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>Max Pending Shipments</Text>
                        <Text size="lg">{settings?.maxPendingShipments || 0}</Text>
                        <Text size="xs" c="dimmed">Maximum pending shipments allowed</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                </Grid>
              </Card>
            </Grid.Col>

            {/* Security Settings */}
            <Grid.Col span={12}>
              <Card withBorder shadow="sm" p="lg">
                <Group mb="md">
                  <IconShield size={20} />
                  <Text fw={600} size="lg">Security Settings</Text>
                </Group>
                <Divider mb="md" />
                <Grid>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="Max Failed Logins"
                        description="Maximum failed login attempts before lockout"
                        placeholder="Enter max failed logins"
                        min={1}
                        max={20}
                        {...form.getInputProps('maxFailedLogins')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>Max Failed Logins</Text>
                        <Text size="lg">{settings?.maxFailedLogins || 0}</Text>
                        <Text size="xs" c="dimmed">Maximum failed login attempts before lockout</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Stack gap="md">
                      {isEditMode ? (
                        <Switch
                          label="Enable Two-Factor Authentication"
                          description="Require 2FA for user accounts"
                          {...form.getInputProps('enable2fa', { type: 'checkbox' })}
                        />
                      ) : (
                        <Stack gap="xs">
                          <Text size="sm" fw={500}>Two-Factor Authentication</Text>
                          <Badge color={settings?.enable2fa ? 'green' : 'red'} variant="light">
                            {settings?.enable2fa ? 'Enabled' : 'Disabled'}
                          </Badge>
                          <Text size="xs" c="dimmed">Require 2FA for user accounts</Text>
                        </Stack>
                      )}

                      {isEditMode ? (
                        <Switch
                          label="Require Photo Proof"
                          description="Require photo proof for deliveries"
                          {...form.getInputProps('requirePhotoProof', { type: 'checkbox' })}
                        />
                      ) : (
                        <Stack gap="xs">
                          <Text size="sm" fw={500}>Photo Proof Requirement</Text>
                          <Badge color={settings?.requirePhotoProof ? 'green' : 'red'} variant="light">
                            {settings?.requirePhotoProof ? 'Required' : 'Optional'}
                          </Badge>
                          <Text size="xs" c="dimmed">Require photo proof for deliveries</Text>
                        </Stack>
                      )}
                    </Stack>
                  </Grid.Col>
                </Grid>
              </Card>
            </Grid.Col>

            {/* Review Settings */}
            <Grid.Col span={12}>
              <Card withBorder shadow="sm" p="lg">
                <Group mb="md">
                  <IconClock size={20} />
                  <Text fw={600} size="lg">Review Settings</Text>
                </Group>
                <Divider mb="md" />
                <Grid>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    {isEditMode ? (
                      <NumberInput
                        label="Review Period (hours)"
                        description="Time period for shipment reviews"
                        placeholder="Enter review period"
                        min={1}
                        max={168}
                        {...form.getInputProps('reviewPeriodHours')}
                      />
                    ) : (
                      <Stack gap="xs">
                        <Text size="sm" fw={500}>Review Period</Text>
                        <Text size="lg">
                          {settings?.reviewPeriodHours || 0}
                          {' '}
                          hours
                        </Text>
                        <Text size="xs" c="dimmed">Time period for shipment reviews</Text>
                      </Stack>
                    )}
                  </Grid.Col>
                </Grid>
              </Card>
            </Grid.Col>
          </Grid>
        </form>

        {/* Last Updated Info */}
        {settings?.updatedAt && (
        <Paper p="md" withBorder>
          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              Last updated:
              {' '}
              {new Date(settings.updatedAt).toLocaleString()}
            </Text>
            {settings.updatedBy && (
            <Text size="sm" c="dimmed">
              Updated by:
              {' '}
              {settings.updatedBy}
            </Text>
            )}
          </Group>
        </Paper>
        )}
      </Stack>
    </Container>
  );
}
