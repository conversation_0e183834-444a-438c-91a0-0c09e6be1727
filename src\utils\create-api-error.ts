import { AxiosError } from 'axios';
import { FALLBACK_ERROR_MESSAGE, HTTP_CODE } from '../data';

type BackendErrorBody = {
  data: null;
  error: {
    status: number;
    name: string;
    message: string;
    details: {
      key?: string;
      params?: Record<string, string | string[]>;
    };
  };
};

type ApiError = {
  code: number;
  message: string;
  errors?: Error[];
};

// Utility supports both new and legacy call signatures.
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function createApiError(...params: any[]): ApiError {
  // Handle legacy signature
  if (typeof params[0] === 'string') {
    const [msg, , details] = params as [string, string | undefined, unknown];

    return {
      code: HTTP_CODE.BAD_REQUEST,
      message: msg,
      // Preserve extra details only in development to avoid leaking sensitive data
      errors: process.env.NODE_ENV === 'development' && details ? [details as Error] : [],
    };
  }

  const [{ error }] = params as [{ error: unknown }];

  let errorBody: ApiError;
  if (error instanceof AxiosError) {
    const { response } = error as AxiosError<BackendErrorBody>;

    // Prefer status present in response data, then fallback to HTTP status, then bad request
    const statusCode = (response?.data?.error?.status as number | undefined)
      ?? (response?.status as number | undefined)
      ?? HTTP_CODE.BAD_REQUEST;

    errorBody = {
      code: statusCode,
      message: response?.data?.error?.message || FALLBACK_ERROR_MESSAGE,
      errors: process.env.DEBUG ? [error as Error] : [],
    };
  } else {
    errorBody = {
      code: HTTP_CODE.INTERNAL_SERVER_ERROR,
      message: FALLBACK_ERROR_MESSAGE,
      errors: process.env.DEBUG ? [error as Error] : [],
    };
  }

  // eslint-disable-next-line no-console
  if (process.env.DEBUG) console.log(error);

  return errorBody;
}

export default createApiError;
