/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-underscore-dangle */
import { NextApiRequest } from 'next';

// eslint-disable-next-line complexity
export const returnReportsParams = (req: NextApiRequest) => {
  const params = req.query;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const cleanParams: Record<string, any> = {};

  // Handle format parameter
  if (params.format) {
    cleanParams.format = params.format;
  }

  // Handle date filters
  if (params.date_from) {
    cleanParams.date_from = params.date_from;
  }
  if (params.date_to) {
    cleanParams.date_to = params.date_to;
  }

  // Handle user type filter (for user reports)
  if (params.user_type) {
    cleanParams.user_type = params.user_type;
  }

  // Handle status filter (for shipment reports)
  if (params.status) {
    cleanParams.status = params.status;
  }

  // Add cache buster for fresh data
  if (params._t) {
    cleanParams._t = params._t;
  }

  return cleanParams;
};
