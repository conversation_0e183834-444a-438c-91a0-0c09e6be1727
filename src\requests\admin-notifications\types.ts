import { Pagination } from '../../types';

// Admin Notification Types (from specification)
export enum AdminNotificationType {
  // User Management Events
  USER_REGISTERED = 'USER_REGISTERED',
  USER_EMAIL_VERIFIED = 'USER_EMAIL_VERIFIED',
  OPERATOR_NEEDS_APPROVAL = 'OPERATOR_NEEDS_APPROVAL',
  USER_STATUS_CHANGED = 'USER_STATUS_CHANGED',

  // Security Events
  SECURITY_ALERT = 'SECURITY_ALERT',
  ADMIN_CREATED = 'ADMIN_CREATED',

  // System Events
  SHIPMENT_CREATED = 'SHIPMENT_CREATED',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  BULK_OPERATION_COMPLETED = 'BULK_OPERATION_COMPLETED',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
}

// Priority Levels (from specification)
export enum AdminNotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

// Filter interface for admin notifications
export type AdminNotificationFilter = {
  search?: string;
  type?: AdminNotificationType;
  priority?: AdminNotificationPriority;
  read?: boolean;
  fromDate?: string;
  toDate?: string;
  shipmentId?: string;
};

// Query props interface for getting admin notifications
export interface GetAdminNotificationsQueryProps {
  pagination?: Pagination;
  filters?: AdminNotificationFilter;
  sort?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: any;
}

// Query props for unread count
export interface GetUnreadCountQueryProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: any;
}

// Query props for mark as read
export interface MarkAsReadQueryProps {
  id: string;
}

// Query props for mark all as read
export interface MarkAllAsReadQueryProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: any;
}

// Query props for delete notification
export interface DeleteNotificationQueryProps {
  id: string;
}

// Request types will be defined in request-transformer.ts to avoid circular dependencies
