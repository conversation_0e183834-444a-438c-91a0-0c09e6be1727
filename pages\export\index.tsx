/* eslint-disable max-lines */
import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Select,
  Grid,
  Card,
  Text,
  Badge,
  ActionIcon,
  Tooltip,
  Modal,
  Checkbox,
} from '@mantine/core';
import {
  IconDownload,
  IconRefresh,
  IconPlus,
  IconFileExport,
  IconClock,
  IconCheck,
  IconX,
  IconCalendar,
} from '@tabler/icons-react';
import { DatePickerInput } from '@mantine/dates';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { exportUsersMutation, exportShipmentsMutation } from '../../src/requests/export';
import { DataTable } from '../../src/components/common/DataTable';
import { useLoading } from '../../src/contexts/LoadingContext';

interface ExportJob {
  id: string;
  type: 'users' | 'shipments';
  format: 'csv' | 'excel' | 'json';
  status: 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  filters: Record<string, any>;
  created_by: string;
}

export default function ExportPage() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();

  // Get initial values from URL params
  const { type: urlType, format: urlFormat, ...urlFilters } = router.query;

  // Modal and form states
  const [exportModalOpened, setExportModalOpened] = useState(false);
  const [exportType, setExportType] = useState<'users' | 'shipments'>(
    (urlType as 'users' | 'shipments') || 'users',
  );
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'json'>(
    (urlFormat as 'csv' | 'excel' | 'json') || 'excel',
  );
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [includeFilters, setIncludeFilters] = useState(true);

  // Mock export jobs data (replace with actual API call)
  const [exportJobs] = useState<ExportJob[]>([
    {
      id: '1',
      type: 'users',
      format: 'excel',
      status: 'completed',
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      completed_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      filters: { user_type: 'CUSTOMER' },
      created_by: '<EMAIL>',
    },
    {
      id: '2',
      type: 'shipments',
      format: 'csv',
      status: 'processing',
      created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      filters: { status: 'IN_TRANSIT' },
      created_by: '<EMAIL>',
    },
  ]);

  // Mutations
  const exportUsersMut = useMutation(exportUsersMutation());
  const exportShipmentsMut = useMutation(exportShipmentsMutation());

  // Open modal if URL params are present
  useEffect(() => {
    if (urlType) {
      setExportModalOpened(true);
    }
  }, [urlType]);

  // Handle export submission
  const handleExport = async () => {
    try {
      showLoading('Creating export job...');

      const exportData = {
        format: exportFormat,
        filters: includeFilters ? {
          ...(dateRange[0] && { date_from: dateRange[0].toISOString() }),
          ...(dateRange[1] && { date_to: dateRange[1].toISOString() }),
          ...urlFilters,
        } : {},
      };

      if (exportType === 'users') {
        await exportUsersMut.mutateAsync(exportData);
      } else {
        await exportShipmentsMut.mutateAsync(exportData);
      }

      notifications.show({
        title: 'Export Started',
        message: 'Your export job has been queued and will be processed shortly.',
        color: 'green',
      });

      setExportModalOpened(false);
      // Clear URL params
      router.replace('/export', undefined, { shallow: true });
    } catch (error) {
      notifications.show({
        title: 'Export Failed',
        message: 'Failed to create export job. Please try again.',
        color: 'red',
      });
    } finally {
      hideLoading();
    }
  };

  // Handle download
  const handleDownload = (jobId: string) => {
    // This would typically trigger a download
    notifications.show({
      title: 'Download Started',
      message: 'Your file download will begin shortly.',
      color: 'blue',
    });
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'processing': return 'blue';
      case 'failed': return 'red';
      default: return 'gray';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <IconCheck size="1rem" />;
      case 'processing': return <IconClock size="1rem" />;
      case 'failed': return <IconX size="1rem" />;
      default: return null;
    }
  };

  // Table columns
  const columns = [
    {
      accessor: 'type',
      label: 'Type',
      sortable: true,
      render: (job: ExportJob) => (
        <Badge color={job.type === 'users' ? 'blue' : 'green'} variant="light">
          {job.type.charAt(0).toUpperCase() + job.type.slice(1)}
        </Badge>
      ),
    },
    {
      accessor: 'format',
      label: 'Format',
      sortable: true,
      render: (job: ExportJob) => job.format.toUpperCase(),
    },
    {
      accessor: 'status',
      label: 'Status',
      sortable: true,
      render: (job: ExportJob) => (
        <Badge
          color={getStatusColor(job.status)}
          variant="light"
          leftSection={getStatusIcon(job.status)}
        >
          {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
        </Badge>
      ),
    },
    {
      accessor: 'created_at',
      label: 'Created',
      sortable: true,
      render: (job: ExportJob) => new Date(job.created_at).toLocaleString(),
    },
    {
      accessor: 'completed_at',
      label: 'Completed',
      sortable: true,
      render: (job: ExportJob) => (job.completed_at ? new Date(job.completed_at).toLocaleString() : 'N/A'),
    },
    {
      accessor: 'actions',
      label: 'Actions',
      render: (job: ExportJob) => (
        <Group gap="xs">
          {job.status === 'completed' && (
            <Tooltip label="Download">
              <ActionIcon
                variant="light"
                color="blue"
                onClick={() => handleDownload(job.id)}
              >
                <IconDownload size="1rem" />
              </ActionIcon>
            </Tooltip>
          )}
        </Group>
      ),
    },
  ];

  return (
    <div>
      <Head>
        <title>Export Center | NAQALAT Admin</title>
      </Head>

      <Container size="xl" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <Group justify="space-between">
            <div>
              <Title order={1}>Export Center</Title>
              <Text c="dimmed" size="lg">
                Manage and download data exports
              </Text>
            </div>
            <Group>
              <Button
                leftSection={<IconRefresh size="1rem" />}
                variant="light"
                onClick={() => window.location.reload()}
              >
                Refresh
              </Button>
              <Button
                leftSection={<IconPlus size="1rem" />}
                onClick={() => setExportModalOpened(true)}
              >
                New Export
              </Button>
            </Group>
          </Group>

          {/* Quick Export Cards */}
          <Grid>
            <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
              <Card
                withBorder
                padding="lg"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  setExportType('users');
                  setExportModalOpened(true);
                }}
              >
                <Group justify="space-between" mb="xs">
                  <IconFileExport size="2rem" color="var(--mantine-color-blue-6)" />
                  <Badge color="blue" variant="light">Users</Badge>
                </Group>
                <Text fw={500} size="lg" mb="xs">Export Users</Text>
                <Text size="sm" c="dimmed">
                  Download user data with filtering options
                </Text>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
              <Card
                withBorder
                padding="lg"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  setExportType('shipments');
                  setExportModalOpened(true);
                }}
              >
                <Group justify="space-between" mb="xs">
                  <IconFileExport size="2rem" color="var(--mantine-color-green-6)" />
                  <Badge color="green" variant="light">Shipments</Badge>
                </Group>
                <Text fw={500} size="lg" mb="xs">Export Shipments</Text>
                <Text size="sm" c="dimmed">
                  Download shipment data and tracking information
                </Text>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, sm: 6, md: 4 }}>
              <Card withBorder padding="lg" style={{ cursor: 'pointer' }} onClick={() => router.push('/reports')}>
                <Group justify="space-between" mb="xs">
                  <IconCalendar size="2rem" color="var(--mantine-color-orange-6)" />
                  <Badge color="orange" variant="light">Reports</Badge>
                </Group>
                <Text fw={500} size="lg" mb="xs">View Reports</Text>
                <Text size="sm" c="dimmed">
                  Access detailed analytics and reports
                </Text>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Export Jobs Table */}
          <Paper withBorder>
            <Group justify="space-between" p="md" style={{ borderBottom: '1px solid var(--mantine-color-gray-3)' }}>
              <Title order={4}>Export History</Title>
              <Group gap="xs">
                <Text size="sm" c="dimmed">
                  {exportJobs.length}
                  {' '}
                  jobs
                </Text>
              </Group>
            </Group>

            <DataTable
              columns={columns}
              data={exportJobs}
              loading={false}
              error={null}
              emptyMessage="No export jobs found"
            />
          </Paper>
        </Stack>
      </Container>

      {/* Export Modal */}
      <Modal
        opened={exportModalOpened}
        onClose={() => {
          setExportModalOpened(false);
          router.replace('/export', undefined, { shallow: true });
        }}
        title="Create New Export"
        size="md"
      >
        <Stack gap="md">
          <Select
            label="Export Type"
            data={[
              { value: 'users', label: 'Users' },
              { value: 'shipments', label: 'Shipments' },
            ]}
            value={exportType}
            onChange={(value) => setExportType(value as 'users' | 'shipments')}
          />

          <Select
            label="Format"
            data={[
              { value: 'excel', label: 'Excel (.xlsx)' },
              { value: 'csv', label: 'CSV (.csv)' },
              { value: 'json', label: 'JSON (.json)' },
            ]}
            value={exportFormat}
            onChange={(value) => setExportFormat(value as 'csv' | 'excel' | 'json')}
          />

          <Checkbox
            label="Include current filters"
            checked={includeFilters}
            onChange={(event) => setIncludeFilters(event.currentTarget.checked)}
          />

          {includeFilters && (
            <DatePickerInput
              type="range"
              label="Date Range (optional)"
              placeholder="Select date range"
              value={dateRange}
              onChange={(value) => setDateRange(value as [Date | null, Date | null])}
            />
          )}

          <Group justify="flex-end" mt="md">
            <Button
              variant="outline"
              onClick={() => {
                setExportModalOpened(false);
                router.replace('/export', undefined, { shallow: true });
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              loading={exportUsersMut.isPending || exportShipmentsMut.isPending}
            >
              Create Export
            </Button>
          </Group>
        </Stack>
      </Modal>
    </div>
  );
}
