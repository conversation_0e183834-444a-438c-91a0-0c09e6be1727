/**
 * Application Routes
 *
 * This file contains all the frontend routes used in the application.
 * Each route is defined as a string or function that returns the URL.
 * Parameters are used for dynamic routes.
 */

export const ROUTES = {
  // Root route
  root: '/',

  // Auth routes
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
    changePassword: '/auth/change-password',
  },

  // User routes (for all authenticated users)
  users: {
    profile: '/profile', // Generic profile page
    settings: '/profile/settings', // User-specific settings
    editProfile: '/profile/edit', // Edit user's own profile
  },

  // Profile route (shorthand for generic profile)
  profile: '/profile',

  // Role-specific profile routes (can point to a more specific component or layout if needed)
  customer: {
    profile: '/customer/profile',
    shipments: '/customer/shipments',
    createShipment: '/customer/shipments/create',
  },
  accessOperator: {
    profile: '/access-operator/profile',
    shipments: '/access-operator/shipments',
    pendingShipments: '/access-operator/shipments/pending',
    destShipments: '/access-operator/shipments/incoming',
    myShipments: '/access-operator/shipments/my',
  },
  carOperator: {
    profile: '/car-operator/profile',
    shipments: '/car-operator/shipments',
    myShipments: '/car-operator/shipments/my',
    inTransitShipments: '/car-operator/shipments/in-transit',
  },

  // Admin routes
  admin: {
    dashboard: '/admin/dashboard', // Admin landing page
    users: {
      list: '/admin/users', // List all users
      detail: (id: string) => `/admin/users/${id}`, // View a specific user's details (admin view)
      approval: '/admin/users/approval', // Page for approving/rejecting AO/CO applications
    },
    profile: '/admin/profile', // Admin's own profile page
  },

  // Legal routes
  legal: {
    privacyPolicy: '/privacy-policy',
    termsOfService: '/terms-of-service',
  },

  // Support routes
  support: {
    help: '/help',
    contact: '/contact', // Added missing contact route from original structure assumption
  },

  // Notification routes
  notifications: '/notifications',

  // Reports routes
  reports: {
    dashboard: '/reports',
    users: '/reports/users',
    shipments: '/reports/shipments',
    system: '/reports/system',
  },

  // Export routes
  export: {
    center: '/export',
  },

  // Audit & Security routes
  audit: {
    logs: '/audit-logs',
  },
  security: {
    monitoring: '/security',
  },
};

// Pages that require authentication
export const authPages = [
  ROUTES.root, // Usually, the root might redirect to login or a dashboard if authenticated

  // Generic User Profile Pages
  ROUTES.users.profile,
  ROUTES.users.settings,
  ROUTES.users.editProfile,
  ROUTES.profile, // Shorthand

  // Role-Specific Profile Pages
  ROUTES.customer.profile,
  ROUTES.accessOperator.profile,
  ROUTES.accessOperator.shipments,
  ROUTES.accessOperator.pendingShipments,
  ROUTES.accessOperator.destShipments,
  ROUTES.accessOperator.myShipments,
  ROUTES.carOperator.profile,
  ROUTES.carOperator.shipments,
  ROUTES.carOperator.myShipments,
  ROUTES.carOperator.inTransitShipments,

  // Admin Pages
  ROUTES.admin.dashboard,
  ROUTES.admin.users.list,
  ROUTES.admin.users.approval,
  // ROUTES.admin.users.detail is a function, so it cannot be directly in authPages.
  // Access to specific user details would be handled by the component for that route.
  ROUTES.admin.profile,

  // Auth actions are typically pages themselves
  ROUTES.auth.changePassword,
  ROUTES.auth.logout, // Though logout might be an action, it can be a page redirecting after action

  // Notification pages
  ROUTES.notifications,
];

// Other page arrays (customerPages, accessOperatorPages, carOperatorPages, adminPages - excluding profile)
// are removed as their non-profile content is being stripped from the application.
// If specific layouts or page groups are needed for roles beyond just their profile,
// new, more focused arrays can be created later.
