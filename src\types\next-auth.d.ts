// src/types/next-auth.d.ts
import 'next-auth';
import 'next-auth/jwt';

// Define your custom user properties
interface CustomUser {
  id: string;
  user_type?: string | null; // Or specific enum if you have one
  status?: string | null; // Or specific enum
  phone?: string | null;
  accessToken?: string | null; // This is the backend token
}

declare module 'next-auth' {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    accessToken?: string | null; // The backend API token
    user: {
      // Default NextAuth user properties
      id?: string | null;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    } & CustomUser; // Add your custom user properties
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   * Also the shape of the user object returned in the `authorize` callback of the Credentials provider.
   */
  interface User extends CustomUser {
    // Default NextAuth user properties can also be here if needed,
    // but CustomUser already includes id and accessToken
    // email and name are usually part of the default User
  }
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `auth`, when using JWT sessions */
  interface JWT {
    accessToken?: string | null; // The backend API token
    user?: {
      id: string;
      email: string;
      name: string;
      user_type?: string | null;
      status?: string | null;
      phone?: string | null;
    };
    // You can add other properties to the JWT token if needed
    // e.g., iat, exp, sub are standard JWT claims
  }
}
