import { QueryClient } from '@tanstack/react-query';

/**
 * Centralized cache invalidation utilities for React Query
 * This provides a consistent way to invalidate related queries after mutations
 */

// Query key constants - centralized for consistency
export const QUERY_KEYS = {
  // Auth related
  auth: {
    login: 'login',
    register: 'register',
    forgotPassword: 'forgotPassword',
    verifyResetToken: 'verifyResetToken',
    resetPassword: 'resetPassword',
    changePassword: 'changePassword',
    sendVerificationOtp: 'sendVerificationOtp',
    verifyEmailOtp: 'verifyEmailOtp',
    resendVerificationOtp: 'resendVerificationOtp',
  },

  // Profile related
  profile: 'profile',

  // Access points related
  accessPoints: {
    list: 'access-points',
    single: 'access-point',
    create: 'create-access-point',
    update: 'update-access-point',
  },

  // Shipments related
  shipments: {
    single: 'shipment',
    list: 'shipments',
    myShipments: 'my-shipments',
    myAssignedShipments: 'my-assigned-shipments',
    myTransportedShipments: 'my-transported-shipments',
    pendingShipments: 'pending-shipments',
    create: 'create-shipment',
    cancel: 'cancel-shipment',
    scan: 'scan-shipment',
    deliver: 'deliver-shipment',
  },

  // QR Labels related
  qrLabels: {
    list: 'qrLabels',
    single: 'qrLabel',
    generateForShipment: 'generateForShipment',
    generateBulk: 'generateBulk',
    generatePDF: 'generatePDF',
  },

  // Dashboard related
  dashboard: 'dashboard',

  // Notifications related
  notifications: {
    list: 'notifications',
    unreadCount: 'notifications-unread-count',
    preferences: 'notification-preferences',
    filterOptions: 'notification-filter-options',
    broadcast: 'notification-broadcast',
    templates: 'notification-templates',
    analytics: 'notification-analytics',
  },
} as const;

/**
 * Cache invalidation strategies for different mutation types
 */
export class CacheInvalidationManager {
  // eslint-disable-next-line no-useless-constructor, no-empty-function
  constructor(private queryClient: QueryClient) {}

  /**
   * Invalidate queries after successful authentication operations
   */
  async invalidateAfterAuth() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.profile] }),
      // Add other auth-related invalidations as needed
    ]);
  }

  /**
   * Invalidate queries after successful profile updates
   */
  async invalidateAfterProfileUpdate() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.profile] }),
      // Profile updates might affect access points if location changed
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.accessPoints.list] }),
    ]);
  }

  /**
   * Invalidate queries after successful access point operations
   */
  async invalidateAfterAccessPointMutation() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.accessPoints.list] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.profile] }), // Profile might show related access points
    ]);
  }

  /**
   * Invalidate queries after successful shipment creation
   */
  async invalidateAfterShipmentCreate() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.list] }),
      // Don't invalidate pending shipments as new shipments aren't pending yet
    ]);
  }

  /**
   * Invalidate queries after successful shipment cancellation
   */
  async invalidateAfterShipmentCancel() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.list] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.pendingShipments] }),
    ]);
  }

  /**
   * Invalidate queries after successful shipment scan (pickup/dropoff/arrival)
   */
  async invalidateAfterShipmentScan() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myAssignedShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myTransportedShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.pendingShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.list] }),
    ]);
  }

  /**
   * Invalidate queries after successful shipment delivery
   */
  async invalidateAfterShipmentDelivery() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myAssignedShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myTransportedShipments] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.list] }),
      // Delivered shipments might still appear in myShipments for customers
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myShipments] }),
    ]);
  }

  /**
   * Invalidate queries after successful QR label generation
   */
  async invalidateAfterQRLabelGeneration() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.qrLabels.list] }),
      // QR generation might be related to shipments
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.shipments.myShipments] }),
    ]);
  }

  /**
   * Invalidate queries after successful notification operations
   */
  async invalidateAfterNotificationUpdate() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.notifications.list] }),
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.notifications.unreadCount] }),
    ]);
  }

  /**
   * Invalidate queries after successful notification preferences update
   */
  async invalidateAfterNotificationPreferencesUpdate() {
    await Promise.all([
      this.queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.notifications.preferences] }),
    ]);
  }

  /**
   * Generic method to invalidate specific query keys
   */
  async invalidateQueries(queryKeys: string[]) {
    await Promise.all(
      queryKeys.map((key) => this.queryClient.invalidateQueries({ queryKey: [key] })),
    );
  }

  /**
   * Force refetch specific queries (more aggressive than invalidate)
   */
  async refetchQueries(queryKeys: string[]) {
    await Promise.all(
      queryKeys.map((key) => this.queryClient.refetchQueries({ queryKey: [key] })),
    );
  }

  /**
   * Remove specific queries from cache entirely
   */
  async removeQueries(queryKeys: string[]) {
    queryKeys.forEach((key) => {
      this.queryClient.removeQueries({ queryKey: [key] });
    });
  }

  /**
   * Clear all cache (use sparingly, typically only on logout)
   */
  async clearAllCache() {
    this.queryClient.clear();
  }
}

/**
 * Hook to get cache invalidation manager instance
 */
export const useCacheInvalidation = (queryClient: QueryClient) => new CacheInvalidationManager(queryClient);

/**
 * Utility function to create cache invalidation manager
 */
export const createCacheInvalidationManager = (queryClient: QueryClient) => new CacheInvalidationManager(queryClient);
