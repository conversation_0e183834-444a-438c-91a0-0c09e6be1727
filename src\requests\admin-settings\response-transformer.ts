import z from 'zod';
import { ApiResponseSchema } from '../common';

// Define the system settings data structure - make fields optional to handle missing data
export const systemSettingsDataSchema = z.object({
  id: z.union([z.string(), z.number()]).optional(),
  min_distance_km: z.number().optional(),
  max_shipments_per_day: z.number().optional(),
  max_shipments_per_user: z.number().optional(),
  max_pending_shipments: z.number().optional(),
  require_photo_proof: z.boolean().optional(),
  max_failed_logins: z.number().optional(),
  review_period_hours: z.number().optional(),
  enable_2fa: z.boolean().optional(),
  gps_tolerance_meters: z.number().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  updated_by: z.string().optional(),
});

// Transform backend response to frontend format with default values
export const transformSystemSettingsResponse = (data: z.infer<typeof systemSettingsDataSchema>) => ({
  id: data.id?.toString(),
  minDistanceKm: data.min_distance_km ?? 2.0,
  maxShipmentsPerDay: data.max_shipments_per_day ?? 10,
  maxShipmentsPerUser: data.max_shipments_per_user ?? 100,
  maxPendingShipments: data.max_pending_shipments ?? 5,
  requirePhotoProof: data.require_photo_proof ?? true,
  maxFailedLogins: data.max_failed_logins ?? 5,
  reviewPeriodHours: data.review_period_hours ?? 24,
  enable2fa: data.enable_2fa ?? true,
  gpsToleranceMeters: data.gps_tolerance_meters ?? 50,
  createdAt: data.created_at,
  updatedAt: data.updated_at,
  updatedBy: data.updated_by,
});

export const systemSettingsSchema = ApiResponseSchema(systemSettingsDataSchema);

export type SystemSettingsData = ReturnType<typeof transformSystemSettingsResponse>;
