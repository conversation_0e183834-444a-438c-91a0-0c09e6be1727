/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable react/require-default-props */
import { useState, useEffect, ReactNode } from 'react';
import { Box, Loader, Text } from '@mantine/core';

interface ClientOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
  loadingText?: string;
}

/**
 * ClientOnly component to prevent hydration mismatches
 * This component only renders its children on the client side after hydration is complete
 */
export default function ClientOnly({
  children,
  fallback,
  loadingText = 'Loading...',
}: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return fallback || (
      <Box
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
          flexDirection: 'column',
        }}
      >
        <Loader size="sm" />
        <Text size="sm" c="dimmed" mt="xs">{loadingText}</Text>
      </Box>
    );
  }

  return <>{children}</>;
}
