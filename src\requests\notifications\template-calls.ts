/* eslint-disable @typescript-eslint/no-explicit-any */
import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { QueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '../cache-invalidation';
import {
  GetTemplatesRequest,
  GetTemplatesResponse,
  GetTemplateRequest,
  GetTemplateResponse,
  CreateTemplateRequest,
  CreateTemplateResponse,
  UpdateTemplateRequest,
  UpdateTemplateResponse,
  DeleteTemplateRequest,
  DeleteTemplateResponse,
  DuplicateTemplateRequest,
  DuplicateTemplateResponse,
  GetTemplateUsageRequest,
  GetTemplateUsageResponse,
  PreviewTemplateRequest,
  PreviewTemplateResponse,
} from '../../types/notification-api.types';

// Use centralized query keys
const queryKeys = QUERY_KEYS.notifications;

// Use centralized template endpoints
const TEMPLATE_ENDPOINTS = API_ENDPOINT.notifications.templates;

/**
 * @description Get list of notification templates
 * @param params - Filter and pagination parameters
 * @returns paginated list of templates
 */
const getTemplatesRequest = (params: GetTemplatesRequest = {}) => {
  const urlParams = new URLSearchParams();

  if (params.page !== undefined) urlParams.append('page', String(params.page));
  if (params.limit !== undefined) urlParams.append('limit', String(params.limit));
  if (params.search) urlParams.append('search', params.search);
  if (params.type) urlParams.append('type', params.type);
  if (params.isActive !== undefined) urlParams.append('isActive', String(params.isActive));
  if (params.createdBy) urlParams.append('createdBy', params.createdBy);

  const queryString = urlParams.toString();
  const url = queryString ? `${TEMPLATE_ENDPOINTS.list}?${queryString}` : TEMPLATE_ENDPOINTS.list;

  return CLIENT_API.get(url)
    .then((res) => res?.data as GetTemplatesResponse)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Get single template by ID
 * @param params - Template ID
 * @returns template details
 */
const getTemplateRequest = (params: GetTemplateRequest) => CLIENT_API.get(TEMPLATE_ENDPOINTS.byId(params.id))
  .then((res) => res?.data as GetTemplateResponse)
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description Create new notification template
 * @param data - Template creation data
 * @returns created template
 */
const createTemplateRequest = (data: CreateTemplateRequest) => CLIENT_API.post(TEMPLATE_ENDPOINTS.create, data)
  .then((res) => res?.data as CreateTemplateResponse)
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description Update existing template
 * @param params - Template ID and update data
 * @returns updated template
 */
const updateTemplateRequest = (params: UpdateTemplateRequest) => {
  const { id, ...updateData } = params;
  return CLIENT_API.put(TEMPLATE_ENDPOINTS.update(id), updateData)
    .then((res) => res?.data as UpdateTemplateResponse)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Delete template
 * @param params - Template ID
 * @returns deletion confirmation
 */
const deleteTemplateRequest = (params: DeleteTemplateRequest) => CLIENT_API.delete(TEMPLATE_ENDPOINTS.delete(params.id))
  .then((res) => res?.data as DeleteTemplateResponse)
  .catch((e) => {
    handleApiError(e);
    throw e.response?.data;
  });

/**
 * @description Duplicate existing template
 * @param params - Template ID and optional new name
 * @returns duplicated template
 */
const duplicateTemplateRequest = (params: DuplicateTemplateRequest) => {
  const { id, ...duplicateData } = params;
  return CLIENT_API.post(TEMPLATE_ENDPOINTS.duplicate(id), duplicateData)
    .then((res) => res?.data as DuplicateTemplateResponse)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Get template usage statistics
 * @param params - Template ID and date range
 * @returns usage statistics
 */
const getTemplateUsageRequest = (params: GetTemplateUsageRequest) => {
  const { id, ...queryParams } = params;
  const urlParams = new URLSearchParams();

  if (queryParams.fromDate) urlParams.append('fromDate', queryParams.fromDate);
  if (queryParams.toDate) urlParams.append('toDate', queryParams.toDate);

  const queryString = urlParams.toString();
  const url = queryString ? `${TEMPLATE_ENDPOINTS.usage(id)}?${queryString}` : TEMPLATE_ENDPOINTS.usage(id);

  return CLIENT_API.get(url)
    .then((res) => res?.data as GetTemplateUsageResponse)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

/**
 * @description Preview template with sample data
 * @param params - Template ID and variable values
 * @returns rendered template preview
 */
const previewTemplateRequest = (params: PreviewTemplateRequest) => {
  const { id, ...previewData } = params;
  return CLIENT_API.post(TEMPLATE_ENDPOINTS.preview(id), previewData)
    .then((res) => res?.data as PreviewTemplateResponse)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

// React Query configurations for template operations
export const getTemplatesQuery = (params: GetTemplatesRequest = {}) => ({
  queryKey: [queryKeys.templates, 'list', params],
  queryFn: () => getTemplatesRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 300000, // 5 minutes - templates don't change frequently
});

export const getTemplateQuery = (params: GetTemplateRequest) => ({
  queryKey: [queryKeys.templates, 'detail', params.id],
  queryFn: () => getTemplateRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 300000, // 5 minutes
});

export const getTemplateUsageQuery = (params: GetTemplateUsageRequest) => ({
  queryKey: [queryKeys.templates, 'usage', params.id, params.fromDate, params.toDate],
  queryFn: () => getTemplateUsageRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 60000, // 1 minute - usage stats can be more dynamic
});

// Mutation functions for template operations
export const createTemplateMutation = () => ({
  mutationKey: [queryKeys.templates, 'create'],
  mutationFn: (data: CreateTemplateRequest) => createTemplateRequest(data),
});

export const updateTemplateMutation = () => ({
  mutationKey: [queryKeys.templates, 'update'],
  mutationFn: (params: UpdateTemplateRequest) => updateTemplateRequest(params),
});

export const deleteTemplateMutation = () => ({
  mutationKey: [queryKeys.templates, 'delete'],
  mutationFn: (params: DeleteTemplateRequest) => deleteTemplateRequest(params),
});

export const duplicateTemplateMutation = () => ({
  mutationKey: [queryKeys.templates, 'duplicate'],
  mutationFn: (params: DuplicateTemplateRequest) => duplicateTemplateRequest(params),
});

export const previewTemplateMutation = () => ({
  mutationKey: [queryKeys.templates, 'preview'],
  mutationFn: (params: PreviewTemplateRequest) => previewTemplateRequest(params),
});

/**
 * Enhanced mutation functions with built-in cache invalidation
 */

// Create template mutation with auto-invalidation
export const createTemplateMutationWithInvalidation = (queryClient: QueryClient) => ({
  ...createTemplateMutation(),
  onSuccess: async () => {
    // Invalidate templates list after creation
    await queryClient.invalidateQueries({ queryKey: [queryKeys.templates, 'list'] });
  },
});

// Update template mutation with auto-invalidation
export const updateTemplateMutationWithInvalidation = (queryClient: QueryClient) => ({
  ...updateTemplateMutation(),
  onSuccess: async (data: any, variables: { id: any; }) => {
    // Invalidate specific template and list
    await queryClient.invalidateQueries({ queryKey: [queryKeys.templates, 'detail', variables.id] });
    await queryClient.invalidateQueries({ queryKey: [queryKeys.templates, 'list'] });
  },
});

// Delete template mutation with auto-invalidation
export const deleteTemplateMutationWithInvalidation = (queryClient: QueryClient) => ({
  ...deleteTemplateMutation(),
  onSuccess: async (data: any, variables: { id: any; }) => {
    // Remove specific template from cache and invalidate list
    queryClient.removeQueries({ queryKey: [queryKeys.templates, 'detail', variables.id] });
    await queryClient.invalidateQueries({ queryKey: [queryKeys.templates, 'list'] });
  },
});

// Duplicate template mutation with auto-invalidation
export const duplicateTemplateMutationWithInvalidation = (queryClient: QueryClient) => ({
  ...duplicateTemplateMutation(),
  onSuccess: async () => {
    // Invalidate templates list after duplication
    await queryClient.invalidateQueries({ queryKey: [queryKeys.templates, 'list'] });
  },
});

// Export request functions for direct use
export {
  getTemplatesRequest,
  getTemplateRequest,
  createTemplateRequest,
  updateTemplateRequest,
  deleteTemplateRequest,
  duplicateTemplateRequest,
  getTemplateUsageRequest,
  previewTemplateRequest,
};
