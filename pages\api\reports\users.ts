import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { userReportsQuerySchema, userReportsResponseSchema } from '../../../src/requests/reports/types';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === apiMethods.GET) {
    try {
      // Validate query parameters
      const validatedQuery = userReportsQuerySchema.parse(req.query);

      // Make backend request
      const { data } = await BACKEND_API(req).get(API_ENDPOINT.reports.users(), {
        headers: { authorization: token },
        params: validatedQuery,
      });

      return createApiResponse(
        res,
        userReportsResponseSchema,
        {
          success: data.success,
          message: data.message,
          data: data.data,
        },
      );
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: 'Method not allowed' });
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
}

export default handler;
