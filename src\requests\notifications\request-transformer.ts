/* eslint-disable @typescript-eslint/no-explicit-any */
import z from 'zod';

// Helper to convert string/array to number with validation
const stringToNumber = (min?: number, max?: number) => z.union([z.string(), z.number(), z.array(z.string())])
  .transform((val) => {
    if (Array.isArray(val)) {
      return parseInt(val[0], 10); // Take first element if array
    }
    return typeof val === 'string' ? parseInt(val, 10) : val;
  })
  .pipe(z.number().min(min ?? 0).max(max ?? Number.MAX_SAFE_INTEGER));

// Helper to convert string/array to boolean
const stringToBoolean = () => z.union([z.string(), z.boolean(), z.array(z.string())])
  .transform((val) => {
    if (Array.isArray(val)) {
      return val[0] === 'true'; // Take first element if array
    }
    return typeof val === 'string' ? val === 'true' : val;
  })
  .pipe(z.boolean());

// Helper to handle string or array of strings (take first element)
const stringOrArray = () => z.union([z.string(), z.array(z.string())])
  .transform((val) => (Array.isArray(val) ? val[0] : val));

// Get notifications request schema - handles URL query parameters (strings) and converts to proper types
export const getNotificationsRequestSchema = z.object({
  page: stringToNumber(0).optional(),
  limit: stringToNumber(1, 100).optional(),
  search: stringOrArray().optional(),
  read: stringToBoolean().optional(),
  type: stringOrArray().optional(),
  priority: stringOrArray().pipe(z.enum(['LOW', 'NORMAL', 'HIGH', 'URGENT'])).optional(),
  shipmentId: stringOrArray().optional(),
  fromDate: stringOrArray().optional(),
  toDate: stringOrArray().optional(),
}).passthrough(); // Allow unknown keys to pass through

// Mark notification as read request schema
export const markAsReadRequestSchema = z.object({
  notificationId: z.string().min(1, 'Notification ID is required'),
});

// Update notification preferences request schema
export const updatePreferencesRequestSchema = z.object({
  email_notifications: z.boolean().optional(),
  sms_notifications: z.boolean().optional(),
  push_notifications: z.boolean().optional(),
  shipment_created: z.boolean().optional(),
  shipment_status_change: z.boolean().optional(),
  qr_assignment: z.boolean().optional(),
  package_ready: z.boolean().optional(),
  delivery_completed: z.boolean().optional(),
  system_alerts: z.boolean().optional(),
});

// Request validation functions
export const validateGetNotificationsRequest = (data: unknown) => getNotificationsRequestSchema.parse(data);

export const validateMarkAsReadRequest = (data: unknown) => markAsReadRequestSchema.parse(data);

export const validateUpdatePreferencesRequest = (data: unknown) => updatePreferencesRequestSchema.parse(data);

// Transform functions for API requests
export const transformGetNotificationsParams = (params: Record<string, any>) => {
  const validated = validateGetNotificationsRequest(params);

  // Convert all values to strings for URL params
  const transformed: Record<string, string> = {};

  Object.entries(validated).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      transformed[key] = String(value);
    }
  });

  return transformed;
};

export const transformMarkAsReadRequest = (notificationId: string) => validateMarkAsReadRequest({ notificationId });

export const transformUpdatePreferencesRequest = (data: Record<string, any>) => validateUpdatePreferencesRequest(data);
