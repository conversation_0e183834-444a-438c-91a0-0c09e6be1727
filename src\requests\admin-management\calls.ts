/* eslint-disable @typescript-eslint/no-explicit-any */
import { CLIENT_API } from '../../lib/axios';
import { API_ENDPOINT } from '../../data/api-endpoints';
import { handleApiError } from '../../utils/handle-api-error';
import {
  transformAdminListParams,
  transformAdminStatusChangeRequest,
  transformCreateAdminRequest,
  transformUpdateAdminRequest,
  transformUpdateProfileRequest,
  transformChangePasswordRequest,
} from './request-transformer';
import {
  transformAdminListResponse,
  transformAdminDetailResponse,
  transformCreateAdminResponse,
  transformUpdateAdminResponse,
  transformAdminStatusChangeResponse,
  transformUpdateProfileResponse,
  transformChangePasswordResponse,
} from './response-transformer';
import {
  AdminListParamsType,
  AdminStatusChangeRequestType,
  CreateAdminRequestType,
  UpdateAdminRequestType,
  UpdateProfileRequestType,
  ChangePasswordRequestType,
  AdminListResponseType,
  AdminDetailResponseType,
  CreateAdminResponseType,
  UpdateAdminResponseType,
  AdminStatusChangeResponseType,
  UpdateProfileResponseType,
  ChangePasswordResponseType,
} from './types';

// Type for error objects with status property
interface ErrorWithStatus {
  status?: number;
}

// API Request Functions
export const getAdminListRequest = async (params: AdminListParamsType = {}): Promise<AdminListResponseType> => {
  try {
    const transformedParams = transformAdminListParams(params);
    const response = await CLIENT_API.get(API_ENDPOINT.admins.list, { params: transformedParams });
    return transformAdminListResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const getAdminDetailRequest = async (adminId: string): Promise<AdminDetailResponseType> => {
  try {
    const response = await CLIENT_API.get(`${API_ENDPOINT.admins.list}/${adminId}`);
    return transformAdminDetailResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const createAdminRequest = async (data: CreateAdminRequestType): Promise<CreateAdminResponseType> => {
  try {
    const transformedData = transformCreateAdminRequest(data);
    const response = await CLIENT_API.post(API_ENDPOINT.admins.list, transformedData);
    return transformCreateAdminResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const updateAdminRequest = async (data: UpdateAdminRequestType): Promise<UpdateAdminResponseType> => {
  try {
    const transformedData = transformUpdateAdminRequest(data);
    const { adminId, ...updateData } = transformedData;
    const response = await CLIENT_API.put(`${API_ENDPOINT.admins.list}/${adminId}`, updateData);
    return transformUpdateAdminResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const changeAdminStatusRequest = async (data: AdminStatusChangeRequestType): Promise<AdminStatusChangeResponseType> => {
  try {
    const transformedData = transformAdminStatusChangeRequest(data);
    const response = await CLIENT_API.put(API_ENDPOINT.admins.status, transformedData);
    return transformAdminStatusChangeResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const deleteAdminRequest = async (adminId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await CLIENT_API.delete(`${API_ENDPOINT.admins.list}/${adminId}`);
    return response.data;
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const updateProfileRequest = async (data: UpdateProfileRequestType): Promise<UpdateProfileResponseType> => {
  try {
    const transformedData = transformUpdateProfileRequest(data);
    const response = await CLIENT_API.put(API_ENDPOINT.profile, transformedData);
    return transformUpdateProfileResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const changePasswordRequest = async (data: ChangePasswordRequestType): Promise<ChangePasswordResponseType> => {
  try {
    const transformedData = transformChangePasswordRequest(data);
    const response = await CLIENT_API.post(API_ENDPOINT.auth.changePassword, transformedData);
    return transformChangePasswordResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

// React Query Configuration Objects
export const getAdminListQuery = (params: AdminListParamsType = {}) => ({
  queryKey: ['admin-list', params],
  queryFn: () => getAdminListRequest(params),
  refetchOnWindowFocus: false,
  staleTime: 2 * 60 * 1000, // 2 minutes
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
});

export const getAdminDetailQuery = (adminId: string) => ({
  queryKey: ['admin-detail', adminId],
  queryFn: () => getAdminDetailRequest(adminId),
  refetchOnWindowFocus: false,
  staleTime: 5 * 60 * 1000, // 5 minutes
  enabled: !!adminId,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
});

export const createAdminMutation = {
  mutationKey: ['create-admin'],
  mutationFn: createAdminRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 409) {
      return false;
    }
    return failureCount < 3;
  },
};

export const updateAdminMutation = {
  mutationKey: ['update-admin'],
  mutationFn: updateAdminRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const changeAdminStatusMutation = {
  mutationKey: ['change-admin-status'],
  mutationFn: changeAdminStatusRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const deleteAdminMutation = {
  mutationKey: ['delete-admin'],
  mutationFn: deleteAdminRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403 || error?.status === 404) {
      return false;
    }
    return failureCount < 3;
  },
};

export const updateProfileMutation = {
  mutationKey: ['update-profile'],
  mutationFn: updateProfileRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
};

export const changePasswordMutation = {
  mutationKey: ['change-password'],
  mutationFn: changePasswordRequest,
  retry: (failureCount: number, error: ErrorWithStatus) => {
    if (error?.status === 400 || error?.status === 401 || error?.status === 403) {
      return false;
    }
    return failureCount < 3;
  },
};
