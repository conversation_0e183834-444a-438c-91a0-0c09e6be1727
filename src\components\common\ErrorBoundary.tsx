/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/require-default-props */
import React, { Component, ReactNode } from 'react';
import {
  <PERSON><PERSON>, Button, Stack, Text, Paper,
} from '@mantine/core';
import { IconAlertTriangle, IconRefresh } from '@tabler/icons-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error to console for debugging
    // eslint-disable-next-line no-console
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // You can also log the error to an error reporting service here
    // Example: logErrorToService(error, errorInfo);
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <Paper p="md" withBorder>
          <Stack gap="md" align="center">
            <Alert
              icon={<IconAlertTriangle size="1rem" />}
              color="red"
              variant="light"
              title="Something went wrong"
            >
              <Stack gap="sm">
                <Text size="sm">
                  An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.
                </Text>
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <Text size="xs" c="dimmed" style={{ fontFamily: 'monospace' }}>
                    {this.state.error.message}
                  </Text>
                )}
                <Button
                  size="sm"
                  variant="light"
                  leftSection={<IconRefresh size="0.8rem" />}
                  onClick={this.handleReset}
                >
                  Try Again
                </Button>
              </Stack>
            </Alert>
          </Stack>
        </Paper>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
