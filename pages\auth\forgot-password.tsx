import { useState } from 'react';
import Link from 'next/link';
import {
  TextInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { forgotPasswordMutation } from '../../src/requests/admin-auth';

type ForgotPasswordFormValues = {
  email: string;
};

export default function AdminForgotPasswordPage() {
  const [submitted, setSubmitted] = useState(false);

  const forgotPasswordMut = useMutation(forgotPasswordMutation);

  const form = useForm<ForgotPasswordFormValues>({
    validate: {
      email: (value) => {
        if (!value) return 'Email is required';
        if (!/\S+@\S+\.\S+/.test(value)) return 'Invalid email format';
        return null;
      },
    },
    initialValues: {
      email: '',
    },
  });

  const handleSubmit = async (values: ForgotPasswordFormValues) => {
    try {
      const result = await forgotPasswordMut.mutateAsync(values);

      if (result.success) {
        notifications.show({
          title: 'Success',
          message: result.message || 'Password reset link sent to your email',
          color: 'green',
        });
        setSubmitted(true);
        form.reset();
      }
    } catch (error: unknown) {
      let errorMessage: string;

      if (error && typeof error === 'object' && 'status' in error) {
        const statusError = error as { status: number; message?: string };
        if (statusError.status === 404) {
          errorMessage = 'Admin account not found';
        } else {
          errorMessage = statusError.message || 'Something went wrong';
        }
      } else {
        errorMessage = error instanceof Error ? error.message : 'Something went wrong';
      }

      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    }
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        Forgot Password
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        Enter your email to receive a password reset link
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {!submitted ? (
          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Stack>
              <TextInput
                required
                label="Email Address"
                placeholder="Enter your admin email"
                {...form.getInputProps('email')}
              />
              <Button
                type="submit"
                fullWidth
                mt="xl"
                loading={forgotPasswordMut.isPending}
              >
                Send Reset Link
              </Button>
            </Stack>
          </form>
        ) : (
          <Text ta="center">
            Password reset link has been sent to your email
          </Text>
        )}
        <Group justify="center" mt="lg">
          <Link href="/auth/login" passHref legacyBehavior>
            <Anchor component="a" c="dimmed" size="sm">
              Back to Sign In
            </Anchor>
          </Link>
        </Group>
      </Paper>
    </Container>
  );
}
