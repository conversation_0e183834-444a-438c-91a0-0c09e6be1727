import { z } from 'zod';

// Constants for validation messages
const EMAIL_INVALID_FORMAT = 'Invalid email format';
const UUID_INVALID_FORMAT = 'Invalid UUID format';
const DATETIME_INVALID_FORMAT = 'Invalid datetime format';

// Base API response schema
const baseApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
});

// ============================================================================
// USER REPORTS SCHEMAS
// ============================================================================

export const userReportsQuerySchema = z.object({
  format: z.enum(['json', 'csv', 'excel']).default('json'),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']).optional(),
});

export const userReportsResponseSchema = baseApiResponseSchema.extend({
  data: z.object({
    summary: z.object({
      total_users: z.number(),
      by_type: z.array(z.object({
        type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']),
        count: z.number(),
      })),
      by_status: z.array(z.object({
        status: z.enum(['ACTIVE', 'PENDING', 'SUSPENDED']),
        count: z.number(),
      })),
    }),
    recent_registrations: z.array(z.object({
      id: z.string().uuid(UUID_INVALID_FORMAT),
      name: z.string(),
      email: z.string().email(EMAIL_INVALID_FORMAT),
      user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']),
      status: z.enum(['ACTIVE', 'PENDING', 'SUSPENDED']),
      created_at: z.string().datetime(DATETIME_INVALID_FORMAT),
    })),
    generated_at: z.string().datetime(DATETIME_INVALID_FORMAT),
    filters: z.object({
      user_type: z.enum(['CUSTOMER', 'ACCESS_OPERATOR', 'CAR_OPERATOR']).optional(),
      date_from: z.string().optional(),
      date_to: z.string().optional(),
    }),
  }),
});

// ============================================================================
// SHIPMENT REPORTS SCHEMAS
// ============================================================================

export const shipmentReportsQuerySchema = z.object({
  status: z.enum(['PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'EXPIRED']).optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
});

export const shipmentReportsResponseSchema = baseApiResponseSchema.extend({
  data: z.object({
    summary: z.object({
      total_shipments: z.number(),
      delivered_shipments: z.number(),
      delivery_rate: z.number(),
      by_status: z.array(z.object({
        status: z.enum(['PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'EXPIRED']),
        count: z.number(),
      })),
    }),
    recent_shipments: z.array(z.object({
      id: z.string().uuid(UUID_INVALID_FORMAT),
      tracking_code: z.string(),
      status: z.enum(['PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'EXPIRED']),
      created_at: z.string().datetime(DATETIME_INVALID_FORMAT),
      customer: z.object({
        name: z.string(),
      }).nullable(),
      originAO: z.object({
        business_name: z.string(),
      }).nullable(),
      destAO: z.object({
        business_name: z.string(),
      }).nullable(),
    })),
    generated_at: z.string().datetime(DATETIME_INVALID_FORMAT),
    filters: z.object({
      status: z.enum(['PENDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED', 'EXPIRED']).optional(),
      date_from: z.string().optional(),
      date_to: z.string().optional(),
    }),
  }),
});

// ============================================================================
// SYSTEM REPORTS SCHEMAS
// ============================================================================

export const systemReportsResponseSchema = baseApiResponseSchema.extend({
  data: z.object({
    system_statistics: z.object({
      users: z.number(),
      shipments: z.number(),
      admins: z.number(),
      notifications: z.number(),
    }),
    performance_metrics: z.object({
      uptime_hours: z.number(),
      memory_usage_mb: z.number(),
      avg_response_time_ms: z.number(),
    }),
    recent_errors: z.array(z.object({
      id: z.string().uuid(UUID_INVALID_FORMAT),
      action: z.string(),
      created_at: z.string().datetime(DATETIME_INVALID_FORMAT),
      details: z.record(z.any()).optional(),
    })),
    generated_at: z.string().datetime(DATETIME_INVALID_FORMAT),
  }),
});

// ============================================================================
// EXPORT SCHEMAS
// ============================================================================

export const exportUsersRequestSchema = z.object({
  format: z.enum(['csv', 'excel', 'json']).default('csv'),
  filters: z.record(z.any()).optional(),
});

export const exportShipmentsRequestSchema = z.object({
  format: z.enum(['csv', 'excel', 'json']).default('csv'),
  filters: z.record(z.any()).optional(),
});

export const exportUsersResponseSchema = baseApiResponseSchema.extend({
  data: z.object({
    id: z.string(), // Not UUID - uses timestamp
    type: z.literal('users'),
    format: z.enum(['csv', 'excel', 'json']),
    filters: z.record(z.any()),
    status: z.literal('processing'),
    created_by: z.string().uuid(UUID_INVALID_FORMAT),
    created_at: z.string().datetime(DATETIME_INVALID_FORMAT),
  }),
});

export const exportShipmentsResponseSchema = baseApiResponseSchema.extend({
  data: z.object({
    id: z.string(), // Not UUID - uses timestamp
    type: z.literal('shipments'),
    format: z.enum(['csv', 'excel', 'json']),
    filters: z.record(z.any()),
    status: z.literal('processing'),
    created_by: z.string().uuid(UUID_INVALID_FORMAT),
    created_at: z.string().datetime(DATETIME_INVALID_FORMAT),
  }),
});

export const exportDownloadResponseSchema = baseApiResponseSchema.extend({
  data: z.object({
    export_id: z.string(),
  }),
}).or(
  // For when implementation is complete
  z.object({
    success: z.boolean(),
    message: z.string(),
    // File will be streamed directly, no JSON response
  }),
);

// ============================================================================
// TYPE EXPORTS
// ============================================================================

export type UserReportsQueryType = z.infer<typeof userReportsQuerySchema>;
export type UserReportsResponseType = z.infer<typeof userReportsResponseSchema>;
export type ShipmentReportsQueryType = z.infer<typeof shipmentReportsQuerySchema>;
export type ShipmentReportsResponseType = z.infer<typeof shipmentReportsResponseSchema>;
export type SystemReportsResponseType = z.infer<typeof systemReportsResponseSchema>;

export type ExportUsersRequestType = z.infer<typeof exportUsersRequestSchema>;
export type ExportShipmentsRequestType = z.infer<typeof exportShipmentsRequestSchema>;
export type ExportUsersResponseType = z.infer<typeof exportUsersResponseSchema>;
export type ExportShipmentsResponseType = z.infer<typeof exportShipmentsResponseSchema>;
export type ExportDownloadResponseType = z.infer<typeof exportDownloadResponseSchema>;
