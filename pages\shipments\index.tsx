import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Group,
  TextInput,
  Select,
  Button,
  Stack,
  Alert,
} from '@mantine/core';
import {
  IconSearch,
  IconRefresh,
  IconAlertCircle,
  IconFileExport,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { useLoading } from '../../src/contexts/LoadingContext';
import { ShipmentStatus, Shipment } from '../../src/types/admin.types';
import { getShipmentsQuery } from '../../src/requests/shipment';
import { DataTable } from '../../src/components/common/DataTable';
import { getShipmentsColumns } from '../../src/components/shipments/ShipmentsTableColumns';
import { useSearch } from '../../src/hooks/useSearch';
import { sortKeysMapping } from '../../src/requests/shipment/params';

const STATUS_OPTIONS = [
  { value: '', label: 'All Statuses' },
  { value: ShipmentStatus.PENDING, label: 'Pending' },
  { value: ShipmentStatus.ASSIGNED, label: 'Assigned' },
  { value: ShipmentStatus.IN_TRANSIT, label: 'In Transit' },
  { value: ShipmentStatus.DELIVERED, label: 'Delivered' },
  { value: ShipmentStatus.CANCELLED, label: 'Cancelled' },
  { value: ShipmentStatus.EXPIRED, label: 'Expired' },
];

export default function Shipments() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();

  // Get filters from URL query parameters
  const statusFilter = `${router.query.status || ''}`;
  const originAoIdFilter = `${router.query.originAoId || ''}`;
  const destAoIdFilter = `${router.query.destAoId || ''}`;
  const customerIdFilter = `${router.query.customerId || ''}`;

  // State for filters and pagination
  const [page, setPage] = useState(1);
  const { searchValue, searchInput, handleInputChange } = useSearch((newPage: number) => setPage(newPage));
  const [pageSize] = useState(20);
  const [status, setStatus] = useState<string>(statusFilter);
  const [originAoId, setOriginAoId] = useState<string>(originAoIdFilter);
  const [destAoId, setDestAoId] = useState<string>(destAoIdFilter);
  const [customerId, setCustomerId] = useState<string>(customerIdFilter);
  const [sortStatus, setSortStatus] = useState<{ accessor: string; direction: 'asc' | 'desc' }>({
    accessor: 'created_at',
    direction: 'desc',
  });

  // Update filters from URL on component mount
  useEffect(() => {
    setStatus(statusFilter !== 'undefined' ? statusFilter : '');
    setOriginAoId(originAoIdFilter !== 'undefined' ? originAoIdFilter : '');
    setDestAoId(destAoIdFilter !== 'undefined' ? destAoIdFilter : '');
    setCustomerId(customerIdFilter !== 'undefined' ? customerIdFilter : '');
  }, [router.query, statusFilter, originAoIdFilter, destAoIdFilter, customerIdFilter]);

  // Prepare filters for API call
  const filters = {
    search: searchValue,
    status: status || undefined,
    originAoId: originAoId || undefined,
    destAoId: destAoId || undefined,
    customerId: customerId || undefined,
  };

  // Convert sort status to API format
  const sortKey = `${
    sortKeysMapping.has(sortStatus.accessor)
      ? sortKeysMapping.get(sortStatus.accessor)
      : sortStatus.accessor
  }:${sortStatus.direction}`;

  // Data fetching
  const {
    data: shipmentsData, isLoading, error, refetch,
  } = useQuery(
    getShipmentsQuery({
      filters,
      sort: sortKey,
      pagination: { page, pageSize },
    }),
  );

  // Handle filter changes
  const handleFilterChange = (filterType: string, value: string) => {
    setPage(1); // Reset to first page when filters change

    const newQuery = { ...router.query };
    if (value) {
      newQuery[filterType] = value;
    } else {
      delete newQuery[filterType];
    }

    router.push({
      pathname: router.pathname,
      query: newQuery,
    });

    // Update local state
    switch (filterType) {
      case 'status':
        setStatus(value);
        break;
      case 'originAoId':
        setOriginAoId(value);
        break;
      case 'destAoId':
        setDestAoId(value);
        break;
      case 'customerId':
        setCustomerId(value);
        break;
      default:
        break;
    }
  };

  // Handle sort changes
  const handleSortChange = (sort: { accessor: string; direction: 'asc' | 'desc' }) => {
    setSortStatus(sort);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle shipment view
  const handleViewShipment = (shipment: Shipment) => {
    router.push(`/shipments/${shipment.id}`);
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
  };

  // Handle export
  const handleExport = () => {
    try {
      const shipments = shipmentsData?.data?.shipments || [];
      if (shipments.length === 0) {
        notifications.show({
          title: 'Export Aborted',
          message: 'There is no data to export.',
          color: 'yellow',
        });
        return;
      }

      const headers = [
        'ID',
        'Status',
        'Customer',
        'Origin',
        'Destination',
        'Created At',
        'Updated At',
      ];
      const csvContent = [
        headers.join(','),
        ...shipments.map(
          (shipment: Shipment) =>
            // eslint-disable-next-line implicit-arrow-linebreak
            `"${shipment.id}","${shipment.status}","${shipment.customer?.name || 'N/A'}","${
              shipment.originAO?.businessName || 'N/A'
            }","${shipment.destAO?.businessName || 'N/A'}","${shipment.createdAt}","${
              shipment.updatedAt
            }"`,
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      if (link.href) {
        URL.revokeObjectURL(link.href);
      }
      const url = URL.createObjectURL(blob);
      link.href = url;
      link.setAttribute('download', 'shipments.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      notifications.show({
        title: 'Export Successful',
        message: 'The shipments data has been exported to shipments.csv.',
        color: 'green',
      });
    } catch (exportError) {
      notifications.show({
        title: 'Export Failed',
        message: 'An unexpected error occurred during the export. Please try again.',
        color: 'red',
      });
    }
  };

  // Calculate pagination
  const shipments = shipmentsData?.data?.shipments || [];
  const totalShipments = shipmentsData?.data?.pagination?.total || 0;
  const totalPages = Math.ceil(totalShipments / pageSize);

  // Set loading state
  useEffect(() => {
    if (isLoading) {
      showLoading('Loading shipments...');
    } else {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading]);

  return (
    <div>
      <Head>
        <title>Shipments | Admin</title>
      </Head>
      <Container size="xl" py="md">
        <Stack gap="md">
          <Group justify="space-between" align="center">
            <Title order={2}>Shipments Management</Title>
            <Group>
              <Button
                leftSection={<IconFileExport size="1rem" />}
                onClick={handleExport}
                variant="outline"
              >
                Export CSV
              </Button>
              <Button
                leftSection={<IconRefresh size="1rem" />}
                onClick={handleRefresh}
                loading={isLoading}
                variant="light"
              >
                Refresh
              </Button>
            </Group>
          </Group>

          {/* Filters */}
          <Paper p="md" withBorder>
            <Group gap="md" align="end">
              <TextInput
                placeholder="Search shipments..."
                leftSection={<IconSearch size="1rem" />}
                value={searchInput}
                onChange={(event) => handleInputChange(event.currentTarget.value)}
                style={{ flex: 1 }}
              />

              <Select
                placeholder="Status"
                data={STATUS_OPTIONS}
                value={status}
                onChange={(value) => handleFilterChange('status', value || '')}
                clearable
                w={150}
              />
            </Group>
          </Paper>

          {/* Error Alert */}
          {error && (
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title="Error loading shipments"
              color="red"
              variant="light"
            >
              {typeof error === 'string' ? error : 'Failed to load shipments. Please try again.'}
            </Alert>
          )}

          {/* Shipments Table */}
          <DataTable
            columns={getShipmentsColumns(handleViewShipment)}
            data={shipments}
            loading={isLoading}
            error={typeof error === 'string' ? error : null}
            emptyMessage="No shipments found matching your criteria"
            pagination={{
              page,
              totalPages,
              onPageChange: handlePageChange,
            }}
            onSortChange={handleSortChange}
            sortState={sortStatus}
          />
        </Stack>
      </Container>
    </div>
  );
}
