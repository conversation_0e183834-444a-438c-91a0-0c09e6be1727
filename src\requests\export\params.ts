/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable no-underscore-dangle */
import { NextApiRequest } from 'next';

// eslint-disable-next-line complexity
export const returnExportParams = (req: NextApiRequest) => {
  const params = req.query;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const cleanParams: Record<string, any> = {};

  // Handle format parameter
  if (params.format) {
    cleanParams.format = params.format;
  }

  // Handle filters object (passed in request body for POST requests)
  if (req.body?.filters) {
    cleanParams.filters = req.body.filters;
  }

  // Add cache buster for fresh data
  if (params._t) {
    cleanParams._t = params._t;
  }

  return cleanParams;
};
