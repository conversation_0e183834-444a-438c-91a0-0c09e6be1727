/* eslint-disable */
// @ts-nocheck
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import NotificationBell from '../NotificationBell';
import { MantineProvider } from '@mantine/core';

// Mock React Query's useQuery to control unread count
jest.mock('@tanstack/react-query', () => ({
  useQuery: () => ({
    data: 5,
    refetch: jest.fn(),
    isLoading: false,
  }),
}));

describe('NotificationBell component', () => {
  it('renders unread count badge and handles click', () => {
    const handleClick = jest.fn();

    render(
      <MantineProvider>
        <NotificationBell onClick={handleClick} />
      </MantineProvider>,
    );

    // Expect unread count label to be in the document
    expect(screen.getByText('5')).toBeInTheDocument();

    // Click the bell icon and ensure handler fires
    const button = screen.getByRole('button', {
      name: /notifications \(5 unread\)/i,
    });
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });
}); 