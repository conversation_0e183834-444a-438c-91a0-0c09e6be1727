/* eslint-disable @typescript-eslint/no-explicit-any */
import { API_ENDPOINT } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { ExportUsersRequest, ExportShipmentsRequest } from './types';

enum mutationKeys {
  exportUsers = 'exportUsers',
  exportShipments = 'exportShipments',
}

// Export Users Request - Now returns blob for direct download
const exportUsersRequest = async (body: ExportUsersRequest): Promise<Blob> => {
  try {
    const response = await CLIENT_API.post(API_ENDPOINT.export.users(), body, {
      responseType: 'blob', // This tells axios to expect binary data
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (err: any) {
    handleApiError(err);
    throw err;
  }
};

export const exportUsersMutation = () => ({
  mutationKey: [mutationKeys.exportUsers],
  mutationFn: exportUsersRequest,
});

// Export Shipments Request - Now returns blob for direct download
const exportShipmentsRequest = async (body: ExportShipmentsRequest): Promise<Blob> => {
  try {
    const response = await CLIENT_API.post(API_ENDPOINT.export.shipments(), body, {
      responseType: 'blob', // This tells axios to expect binary data
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return response.data;
  } catch (err: any) {
    handleApiError(err);
    throw err;
  }
};

export const exportShipmentsMutation = () => ({
  mutationKey: [mutationKeys.exportShipments],
  mutationFn: exportShipmentsRequest,
});

// Remove the old downloadExportQuery - no longer needed for direct downloads
