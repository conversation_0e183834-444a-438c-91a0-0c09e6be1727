import {
  adminLoginRequestSchema,
  adminRegisterRequestSchema,
  otpVerificationRequestSchema,
  forgotPasswordRequestSchema,
  resetPasswordRequestSchema,
  changePasswordRequestSchema,
  AdminLoginRequestType,
  AdminRegisterRequestType,
  OtpVerificationRequestType,
  ForgotPasswordRequestType,
  ResetPasswordRequestType,
  ChangePasswordRequestType,
} from './types';

// Request transformers - convert frontend data to backend format
export const transformAdminLoginRequest = (data: AdminLoginRequestType) => {
  const validated = adminLoginRequestSchema.parse(data);
  return {
    email: validated.email.toLowerCase().trim(),
    password: validated.password,
  };
};

export const transformAdminRegisterRequest = (data: AdminRegisterRequestType) => {
  const validated = adminRegisterRequestSchema.parse(data);
  return {
    name: validated.name.trim(),
    email: validated.email.toLowerCase().trim(),
    password: validated.password,
    role: validated.role,
  };
};

export const transformOtpVerificationRequest = (data: OtpVerificationRequestType) => {
  const validated = otpVerificationRequestSchema.parse(data);
  return {
    email: validated.email.toLowerCase().trim(),
    otp: validated.otp,
  };
};

export const transformForgotPasswordRequest = (data: ForgotPasswordRequestType) => {
  const validated = forgotPasswordRequestSchema.parse(data);
  return {
    email: validated.email.toLowerCase().trim(),
  };
};

export const transformResetPasswordRequest = (data: ResetPasswordRequestType) => {
  const validated = resetPasswordRequestSchema.parse(data);
  return {
    token: validated.token,
    email: validated.email.toLowerCase().trim(),
    newPassword: validated.newPassword,
  };
};

export const transformChangePasswordRequest = (data: ChangePasswordRequestType) => {
  const validated = changePasswordRequestSchema.parse(data);
  return {
    currentPassword: validated.currentPassword,
    newPassword: validated.newPassword,
  };
};
