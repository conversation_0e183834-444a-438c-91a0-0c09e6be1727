/* eslint-disable */
// @ts-nocheck
// Jest unit tests for notification transformers

import {
  transformGetNotificationsParams,
} from '../request-transformer';
import {
  transformUnreadCountResponse,
  transformNotificationListResponse,
} from '../response-transformer';

// Jest global functions (describe, it, expect) are available without explicit imports

describe('Notification request/response transformers', () => {
  describe('transformGetNotificationsParams', () => {
    it('should convert various parameter types to string key-value pairs', () => {
      const params = {
        page: '1',
        limit: 20,
        read: 'true',
        type: 'SYSTEM_ALERT',
        priority: 'HIGH',
      } as const;

      const result = transformGetNotificationsParams(params as unknown as Record<string, unknown>);

      expect(result).toEqual({
        page: '1',
        limit: '20',
        read: 'true',
        type: 'SYSTEM_ALERT',
        priority: 'HIGH',
      });
    });
  });

  describe('transformUnreadCountResponse', () => {
    it('should extract unreadCount from data wrapper', () => {
      const response = {
        success: true,
        data: {
          unreadCount: 5,
        },
      };

      expect(transformUnreadCountResponse(response)).toBe(5);
    });

    it('should extract unreadCount from root level', () => {
      const response = {
        unreadCount: 3,
      };

      expect(transformUnreadCountResponse(response)).toBe(3);
    });
  });

  describe('transformNotificationListResponse', () => {
    it('should correctly transform a valid notification list response', () => {
      const now = new Date().toISOString();
      const apiResponse = {
        success: true,
        message: 'Fetched successfully',
        data: {
          notifications: [
            {
              id: '1',
              user_id: 'user1',
              shipment_id: null,
              notification_type: 'SHIPMENT_CREATED',
              title: 'Shipment Created',
              message: 'Your shipment has been created',
              priority: 'NORMAL',
              read: false,
              read_at: null,
              metadata: null,
              expires_at: null,
              created_at: now,
              updated_at: now,
            },
          ],
          pagination: {
            page: 0,
            limit: 10,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
          unreadCount: 1,
        },
      };

      const expected = {
        notifications: apiResponse.data.notifications,
        pagination: apiResponse.data.pagination,
        unreadCount: apiResponse.data.unreadCount,
      };

      expect(transformNotificationListResponse(apiResponse)).toEqual(expected);
    });
  });
}); 