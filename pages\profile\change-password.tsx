/* eslint-disable no-use-before-define */
import { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Button,
  PasswordInput,
  Stack,
  Text,
  Alert,
  Group,
  Progress,
} from '@mantine/core';
import {
  IconLock,
  IconCheck,
  IconX,
  IconArrowLeft,
  IconShield,
} from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import { useForm } from '@mantine/form';
import { useRouter } from 'next/router';
import { notifications } from '@mantine/notifications';
import {
  changePasswordMutation,
  ChangePasswordRequestType,
} from '../../src/requests/admin-management';

// Password strength checker
const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
  let score = 0;

  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  if (score < 3) return { score: score * 16.67, label: 'Weak', color: 'red' };
  if (score < 5) return { score: score * 16.67, label: 'Fair', color: 'orange' };
  if (score < 6) return { score: score * 16.67, label: 'Good', color: 'yellow' };
  return { score: 100, label: 'Strong', color: 'green' };
};

export default function ChangePasswordPage() {
  const router = useRouter();
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: '' });

  const changePasswordMut = useMutation({
    mutationKey: changePasswordMutation.mutationKey,
    mutationFn: changePasswordMutation.mutationFn,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'Password changed successfully',
        color: 'green',
      });

      // Reset form and redirect
      form.reset();
      router.push('/profile');
    },
    onError: (error: unknown) => {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    },
  });

  const form = useForm<ChangePasswordRequestType>({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    validate: {
      currentPassword: (value) => (!value ? 'Current password is required' : null),
      newPassword: (value) => {
        if (!value) return 'New password is required';
        if (value.length < 6) return 'Password must be at least 6 characters';
        if (value.length < 8) return 'For better security, use at least 8 characters';
        return null;
      },
      confirmPassword: (value, values) => {
        if (!value) return 'Please confirm your password';
        if (value !== values.newPassword) return 'Passwords do not match';
        return null;
      },
    },
  });

  const handleSubmit = (values: ChangePasswordRequestType) => {
    changePasswordMut.mutate(values);
  };

  const handleNewPasswordChange = (value: string) => {
    form.setFieldValue('newPassword', value);
    setPasswordStrength(getPasswordStrength(value));
  };

  const handleGoBack = () => {
    router.push('/profile');
  };

  return (
    <Container size="sm" py="xl">
      <Group mb="lg">
        <Button
          variant="subtle"
          leftSection={<IconArrowLeft size="1rem" />}
          onClick={handleGoBack}
        >
          Back to Profile
        </Button>
      </Group>

      <Title order={1} mb="lg">
        Change Password
      </Title>

      <Paper withBorder p="lg">
        <Alert
          icon={<IconShield size="1rem" />}
          title="Password Security"
          color="blue"
          variant="light"
          mb="lg"
        >
          <Text size="sm">
            Choose a strong password that you haven&apos;t used elsewhere.
            A good password should be at least 8 characters long and include
            a mix of letters, numbers, and special characters.
          </Text>
        </Alert>

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <PasswordInput
              withAsterisk
              label="Current Password"
              placeholder="Enter your current password"
              leftSection={<IconLock size="1rem" />}
              {...form.getInputProps('currentPassword')}
            />

            <div>
              <PasswordInput
                withAsterisk
                label="New Password"
                placeholder="Enter your new password"
                leftSection={<IconLock size="1rem" />}
                value={form.values.newPassword}
                onChange={(e) => handleNewPasswordChange(e.target.value)}
                error={form.errors.newPassword}
              />

              {form.values.newPassword && (
                <div style={{ marginTop: '8px' }}>
                  <Group justify="space-between" mb={4}>
                    <Text size="xs" c="dimmed">Password Strength</Text>
                    <Text size="xs" c={passwordStrength.color} fw={500}>
                      {passwordStrength.label}
                    </Text>
                  </Group>
                  <Progress
                    value={passwordStrength.score}
                    color={passwordStrength.color}
                    size="xs"
                  />
                </div>
              )}
            </div>

            <PasswordInput
              withAsterisk
              label="Confirm New Password"
              placeholder="Confirm your new password"
              leftSection={<IconLock size="1rem" />}
              {...form.getInputProps('confirmPassword')}
            />

            {/* Password Requirements */}
            <Paper withBorder p="sm" bg="gray.0">
              <Text size="sm" fw={500} mb="xs">Password Requirements:</Text>
              <Stack gap={4}>
                <Group gap="xs">
                  {form.values.newPassword.length >= 6 ? (
                    <IconCheck size="0.8rem" color="green" />
                  ) : (
                    <IconX size="0.8rem" color="red" />
                  )}
                  <Text size="xs" c={form.values.newPassword.length >= 6 ? 'green' : 'red'}>
                    At least 6 characters
                  </Text>
                </Group>

                <Group gap="xs">
                  {form.values.newPassword.length >= 8 ? (
                    <IconCheck size="0.8rem" color="green" />
                  ) : (
                    <IconX size="0.8rem" color="gray" />
                  )}
                  <Text size="xs" c={form.values.newPassword.length >= 8 ? 'green' : 'gray'}>
                    At least 8 characters (recommended)
                  </Text>
                </Group>

                <Group gap="xs">
                  {/[A-Z]/.test(form.values.newPassword) ? (
                    <IconCheck size="0.8rem" color="green" />
                  ) : (
                    <IconX size="0.8rem" color="gray" />
                  )}
                  <Text size="xs" c={/[A-Z]/.test(form.values.newPassword) ? 'green' : 'gray'}>
                    Contains uppercase letter
                  </Text>
                </Group>

                <Group gap="xs">
                  {/[a-z]/.test(form.values.newPassword) ? (
                    <IconCheck size="0.8rem" color="green" />
                  ) : (
                    <IconX size="0.8rem" color="gray" />
                  )}
                  <Text size="xs" c={/[a-z]/.test(form.values.newPassword) ? 'green' : 'gray'}>
                    Contains lowercase letter
                  </Text>
                </Group>

                <Group gap="xs">
                  {/[0-9]/.test(form.values.newPassword) ? (
                    <IconCheck size="0.8rem" color="green" />
                  ) : (
                    <IconX size="0.8rem" color="gray" />
                  )}
                  <Text size="xs" c={/[0-9]/.test(form.values.newPassword) ? 'green' : 'gray'}>
                    Contains number
                  </Text>
                </Group>

                <Group gap="xs">
                  {/[^A-Za-z0-9]/.test(form.values.newPassword) ? (
                    <IconCheck size="0.8rem" color="green" />
                  ) : (
                    <IconX size="0.8rem" color="gray" />
                  )}
                  <Text size="xs" c={/[^A-Za-z0-9]/.test(form.values.newPassword) ? 'green' : 'gray'}>
                    Contains special character
                  </Text>
                </Group>
              </Stack>
            </Paper>

            <Group justify="flex-end" mt="lg">
              <Button
                variant="outline"
                onClick={handleGoBack}
                disabled={changePasswordMut.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={changePasswordMut.isPending}
                leftSection={<IconCheck size="1rem" />}
              >
                Change Password
              </Button>
            </Group>
          </Stack>
        </form>
      </Paper>
    </Container>
  );
}
