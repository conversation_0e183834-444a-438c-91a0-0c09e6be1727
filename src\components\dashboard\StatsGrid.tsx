import { SimpleGrid } from '@mantine/core';
import {
  IconUsers,
  IconUser<PERSON>heck,
  IconUserX,
  IconTruck,
  IconPackage,
  IconPackageOff,
  IconClock,
  IconCheck,
} from '@tabler/icons-react';
import { StatsCard } from './StatsCard';
import { UserStats, ShipmentStats } from '../../types/admin.types';

interface StatsGridProps {
  userStats: UserStats;
  shipmentStats: ShipmentStats;
}

export function StatsGrid({ userStats, shipmentStats }: StatsGridProps) {
  return (
    <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md">
      {/* User Statistics */}
      <StatsCard
        title="Total Users"
        value={userStats.total}
        icon={<IconUsers size="1.4rem" />}
        color="blue"
        description="All registered users"
      />

      <StatsCard
        title="Active Users"
        value={userStats.active_users}
        icon={<IconUserCheck size="1.4rem" />}
        color="green"
        description="Currently active users"
      />

      <StatsCard
        title="Pending Approvals"
        value={userStats.pending_approvals}
        icon={<IconClock size="1.4rem" />}
        color="orange"
        description="Operators awaiting approval"
      />

      <StatsCard
        title="Inactive Users"
        value={userStats.inactive_users}
        icon={<IconUserX size="1.4rem" />}
        color="red"
        description="Suspended or inactive"
      />

      {/* Shipment Statistics */}
      <StatsCard
        title="Total Shipments"
        value={shipmentStats.total}
        icon={<IconPackage size="1.4rem" />}
        color="indigo"
        description="All time shipments"
      />

      <StatsCard
        title="Delivered"
        value={shipmentStats.delivered}
        icon={<IconCheck size="1.4rem" />}
        color="green"
        description="Successfully delivered"
      />

      <StatsCard
        title="In Transit"
        value={shipmentStats.in_transit}
        icon={<IconTruck size="1.4rem" />}
        color="blue"
        description="Currently being delivered"
      />

      <StatsCard
        title="Delivery Rate"
        value={`${shipmentStats.delivery_rate}%`}
        icon={<IconPackageOff size="1.4rem" />}
        color="teal"
        description="Success rate"
      />
    </SimpleGrid>
  );
}
