/* eslint-disable default-param-last */
/* eslint-disable sonarjs/no-duplicate-string */
/**
 * File download utility functions for handling direct file downloads
 */

export interface DownloadFileOptions {
  filename?: string;
  format: 'csv' | 'excel';
}

/**
 * Downloads a blob as a file
 */
export const downloadFile = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};

/**
 * Downloads users export with proper filename
 */
export const downloadUsersExport = async (
  filters: {
    search?: string;
    user_type?: 'ACCESS_OPERATOR' | 'CAR_OPERATOR';
    status?: 'ACTIVE' | 'PENDING' | 'SUSPENDED';
  },
  format: 'csv' | 'excel' = 'csv',
): Promise<void> => {
  const response = await fetch('/api/export/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      format,
      filters,
    }),
  });

  if (!response.ok) {
    throw new Error('Export failed');
  }

  const blob = await response.blob();
  const filename = `users_export_${new Date().toISOString().split('T')[0]}.${format}`;
  downloadFile(blob, filename);
};

/**
 * Downloads shipments export with proper filename
 */
export const downloadShipmentsExport = async (
  filters: {
    search?: string;
    status?: string;
    date_from?: string;
    date_to?: string;
  },
  format: 'csv' | 'excel' = 'csv',
): Promise<void> => {
  const response = await fetch('/api/export/shipments', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      format,
      filters,
    }),
  });

  if (!response.ok) {
    throw new Error('Export failed');
  }

  const blob = await response.blob();
  const filename = `shipments_export_${new Date().toISOString().split('T')[0]}.${format}`;
  downloadFile(blob, filename);
};

/**
 * Generic export function for any export type
 */
export const downloadExport = async (
  endpoint: string,
  filters: Record<string, unknown>,
  format: 'csv' | 'excel' = 'csv',
  baseFilename: string,
): Promise<void> => {
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      format,
      filters,
    }),
  });

  if (!response.ok) {
    throw new Error('Export failed');
  }

  const blob = await response.blob();
  const filename = `${baseFilename}_${new Date().toISOString().split('T')[0]}.${format}`;
  downloadFile(blob, filename);
};
