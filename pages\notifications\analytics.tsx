/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
/* eslint-disable max-lines */
import React, { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Text,
  Grid,
  Card,
  Select,
  Badge,
  Progress,
  Alert,
  Center,
  Loader,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconDownload,
  IconRefresh,
  IconTrendingUp,
  IconTrendingDown,
  IconMinus,
} from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { DatePickerInput } from '@mantine/dates';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import AdminLayout from '../../src/components/layouts/AdminLayout';
import { PermissionGuard } from '../../src/components/auth/PermissionGuard';
import { Permission } from '../../src/utils/permissions';
import {
  useAnalyticsData,
  exportAnalyticsMutation,
  type GetAnalyticsOverviewRequest,
} from '../../src/requests/notifications/analytics-calls';

export default function NotificationAnalyticsPage() {
  const router = useRouter();
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    new Date(),
  ]);
  const [notificationTypeFilter, setNotificationTypeFilter] = useState<string>('');

  // Prepare analytics request parameters
  const analyticsParams: GetAnalyticsOverviewRequest = {
    fromDate: dateRange[0]?.toISOString(),
    toDate: dateRange[1]?.toISOString(),
    type: notificationTypeFilter || undefined,
  };

  // Fetch analytics data
  const { data: analyticsResponse, isLoading, refetch } = useAnalyticsData(analyticsParams);
  const analyticsData = analyticsResponse?.data;

  // Export mutation
  const exportMutation = useMutation(exportAnalyticsMutation());

  const handleBack = () => {
    router.push('/notifications');
  };

  const handleRefresh = () => {
    refetch();
    notifications.show({
      title: 'Success',
      message: 'Analytics refreshed',
      color: 'green',
    });
  };

  const handleExport = async () => {
    try {
      await exportMutation.mutateAsync({
        fromDate: dateRange[0]?.toISOString(),
        toDate: dateRange[1]?.toISOString(),
        type: notificationTypeFilter || undefined,
        format: 'excel',
      });
      notifications.show({
        title: 'Success',
        message: 'Analytics exported successfully',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to export analytics',
        color: 'red',
      });
    }
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <IconTrendingUp size="1rem" color="green" />;
    if (trend < 0) return <IconTrendingDown size="1rem" color="red" />;
    return <IconMinus size="1rem" color="gray" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'green';
    if (trend < 0) return 'red';
    return 'gray';
  };

  return (
    <AdminLayout>
      <PermissionGuard permission={Permission.VIEW_NOTIFICATION_ANALYTICS}>
        <Container size="xl" py="md">
          <Stack gap="lg">
            {/* Header */}
            <Group justify="space-between">
              <div>
                <Title order={1} size="h2">
                  Notification Analytics
                </Title>
                <Text c="dimmed" size="sm" mt={4}>
                  View notification performance and metrics
                </Text>
              </div>
              <Group gap="sm">
                <Button
                  variant="light"
                  leftSection={<IconArrowLeft size="1rem" />}
                  onClick={handleBack}
                >
                  Back
                </Button>
                <Button
                  variant="light"
                  leftSection={<IconRefresh size="1rem" />}
                  onClick={handleRefresh}
                  loading={isLoading}
                >
                  Refresh
                </Button>
                <Button
                  leftSection={<IconDownload size="1rem" />}
                  onClick={handleExport}
                  loading={exportMutation.isPending}
                >
                  Export
                </Button>
              </Group>
            </Group>

            {/* Filters */}
            <Paper withBorder p="md">
              <Group gap="md">
                <DatePickerInput
                  type="range"
                  label="Date Range"
                  placeholder="Select date range"
                  value={dateRange}
                  onChange={(dates) => {
                    const [start, end] = dates;
                    setDateRange([start ? new Date(start) : null, end ? new Date(end) : null]);
                  }}
                  style={{ minWidth: 250 }}
                />
                <Select
                  label="Notification Type"
                  placeholder="All Types"
                  data={[
                    { value: '', label: 'All Types' },
                    { value: 'SHIPMENT_DELIVERED', label: 'Shipment Delivered' },
                    { value: 'SYSTEM_ANNOUNCEMENT', label: 'System Announcement' },
                    { value: 'BROADCAST_MESSAGE', label: 'Broadcast Message' },
                  ]}
                  value={notificationTypeFilter}
                  onChange={(value) => setNotificationTypeFilter(value || '')}
                  clearable
                />
              </Group>
            </Paper>

            {isLoading ? (
              <Center py="xl">
                <Loader size="lg" />
              </Center>
            ) : (
              <>
                {/* Overview Cards */}
                <Grid>
                  <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                    <Card withBorder>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Total Sent</Text>
                          {getTrendIcon(analyticsData?.trends.sentTrend || 0)}
                        </Group>
                        <Text size="xl" fw={700}>
                          {analyticsData?.overview.totalSent.toLocaleString() || '0'}
                        </Text>
                        <Text size="xs" c={getTrendColor(analyticsData?.trends.sentTrend || 0)}>
                          {(analyticsData?.trends.sentTrend || 0) > 0 ? '+' : ''}
                          {analyticsData?.trends.sentTrend || 0}
                          % from last period
                        </Text>
                      </Stack>
                    </Card>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                    <Card withBorder>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Total Delivered</Text>
                          {getTrendIcon(analyticsData?.trends.deliveredTrend || 0)}
                        </Group>
                        <Text size="xl" fw={700}>
                          {analyticsData?.overview.totalDelivered.toLocaleString() || '0'}
                        </Text>
                        <Text size="xs" c={getTrendColor(analyticsData?.trends.deliveredTrend || 0)}>
                          {(analyticsData?.trends.deliveredTrend || 0) > 0 ? '+' : ''}
                          {analyticsData?.trends.deliveredTrend || 0}
                          % from last period
                        </Text>
                        <Progress
                          value={analyticsData?.overview.deliveryRate || 0}
                          color="green"
                          size="xs"
                        />
                        <Text size="xs" c="dimmed">
                          {analyticsData?.overview.deliveryRate.toFixed(1) || '0'}
                          % delivery rate
                        </Text>
                      </Stack>
                    </Card>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                    <Card withBorder>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Total Opened</Text>
                          {getTrendIcon(analyticsData?.trends.openedTrend || 0)}
                        </Group>
                        <Text size="xl" fw={700}>
                          {analyticsData?.overview.totalOpened.toLocaleString() || '0'}
                        </Text>
                        <Text size="xs" c={getTrendColor(analyticsData?.trends.openedTrend || 0)}>
                          {(analyticsData?.trends.openedTrend || 0) > 0 ? '+' : ''}
                          {analyticsData?.trends.openedTrend || 0}
                          % from last period
                        </Text>
                        <Progress
                          value={analyticsData?.overview.openRate || 0}
                          color="blue"
                          size="xs"
                        />
                        <Text size="xs" c="dimmed">
                          {analyticsData?.overview.openRate.toFixed(1) || '0'}
                          % open rate
                        </Text>
                      </Stack>
                    </Card>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
                    <Card withBorder>
                      <Stack gap="xs">
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Total Failed</Text>
                          {getTrendIcon(analyticsData?.trends.failedTrend || 0)}
                        </Group>
                        <Text size="xl" fw={700}>
                          {analyticsData?.overview.totalFailed.toLocaleString() || '0'}
                        </Text>
                        <Text size="xs" c={getTrendColor(analyticsData?.trends.failedTrend || 0)}>
                          {(analyticsData?.trends.failedTrend || 0) > 0 ? '+' : ''}
                          {analyticsData?.trends.failedTrend || 0}
                          % from last period
                        </Text>
                        <Progress
                          value={analyticsData?.overview.failureRate || 0}
                          color="red"
                          size="xs"
                        />
                        <Text size="xs" c="dimmed">
                          {analyticsData?.overview.failureRate.toFixed(1) || '0'}
                          % failure rate
                        </Text>
                      </Stack>
                    </Card>
                  </Grid.Col>
                </Grid>

                {/* Performance by Type */}
                <Paper withBorder p="md">
                  <Title order={3} size="h4" mb="md">
                    Performance by Type
                  </Title>
                  <Stack gap="md">
                    {(analyticsData?.byType || []).map((item) => (
                      <Card key={item.type} withBorder>
                        <Group justify="space-between" mb="sm">
                          <Text fw={500}>
                            {item.type.replace('_', ' ')}
                          </Text>
                          <Badge color="blue" size="sm">
                            {item.sent.toLocaleString()}
                            {' '}
                            sent
                          </Badge>
                        </Group>
                        <Grid>
                          <Grid.Col span={4}>
                            <Text size="sm" c="dimmed">Delivered</Text>
                            <Text fw={500}>{item.delivered.toLocaleString()}</Text>
                            <Progress
                              value={item.sent > 0 ? (item.delivered / item.sent) * 100 : 0}
                              color="green"
                              size="xs"
                              mt={4}
                            />
                          </Grid.Col>
                          <Grid.Col span={4}>
                            <Text size="sm" c="dimmed">Opened</Text>
                            <Text fw={500}>{item.opened.toLocaleString()}</Text>
                            <Progress
                              value={item.delivered > 0 ? (item.opened / item.delivered) * 100 : 0}
                              color="blue"
                              size="xs"
                              mt={4}
                            />
                          </Grid.Col>
                          <Grid.Col span={4}>
                            <Text size="sm" c="dimmed">Failed</Text>
                            <Text fw={500}>{item.failed.toLocaleString()}</Text>
                            <Progress
                              value={item.sent > 0 ? (item.failed / item.sent) * 100 : 0}
                              color="red"
                              size="xs"
                              mt={4}
                            />
                          </Grid.Col>
                        </Grid>
                      </Card>
                    ))}
                  </Stack>
                </Paper>

                {/* Info Alert */}
                <Alert color="blue">
                  <Text size="sm">
                    Analytics data is updated in real-time and shows performance metrics for your notification campaigns.
                  </Text>
                </Alert>
              </>
            )}
          </Stack>
        </Container>
      </PermissionGuard>
    </AdminLayout>
  );
}

// Protect the page with authentication
NotificationAnalyticsPage.auth = true;
