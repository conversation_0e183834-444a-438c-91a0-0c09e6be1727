/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextApiRequest, NextApiResponse } from 'next';
import { BACKEND_API } from '../../../src/lib/axios';
import { API_ENDPOINT } from '../../../src/data/api-endpoints';
import { getJwt } from '../../../src/utils/auth';
import { HTTP_CODE } from '../../../src/data';
import { dashboardApiRespons } from '../../../src/requests';
import { createApiError } from '../../../src/utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { token } = await getJwt(req);

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          type: 'AUTHENTICATION_ERROR',
          message: 'No token provided',
        },
      });
    }

    const { data } = await BACKEND_API(req).get(API_ENDPOINT.dashboard, {
      headers: {
        Authorization: token,
      },
    });

    return res.status(HTTP_CODE.SUCCESS).json(dashboardApiRespons(data));
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
}
