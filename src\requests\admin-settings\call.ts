import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import { getSettingsQueryProps, UpdateSettings } from './type';

enum queryKeys {
  settings = 'settings',
  update = 'update',
  reset = 'reset',
}
const getSettingsRequest = (props: getSettingsQueryProps) => {
  const {
    filters, sort, pagination,
  } = props;
  return CLIENT_API.get(API_ENDPOINT.settings.list(), {
    params: {
      search: filters?.search,
      createdAtGte: filters?.createdAtGte,
      sort,
      limit: pagination?.limit || pagination?.pageSize,
      page: pagination?.page,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};
export const getSettingsQuery = (props: getSettingsQueryProps) => ({
  queryKey: [queryKeys.settings, props?.filters, props?.pagination, props?.sort],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  queryFn: (params: any) => getSettingsRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

const updateSettingsRequest = (body: UpdateSettings) => CLIENT_API.put(API_ENDPOINT.settings.list(), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const updateSettingsMutation = () => ({
  mutationKey: [queryKeys.update],
  mutationFn: updateSettingsRequest,
});

const updateSettingsResetRequest = () => CLIENT_API.post(API_ENDPOINT.settings.reset())
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const updateSettingsResetMutation = () => ({
  mutationKey: [queryKeys.reset],
  mutationFn: updateSettingsResetRequest,
});
