import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { LoadingOverlay, Container } from '@mantine/core';

export default function HomePage() {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (status === 'authenticated' && session) {
      // User is authenticated, redirect to dashboard
      router.replace('/dashboard');
    } else {
      // User is not authenticated, redirect to login
      router.replace('/auth/login');
    }
  }, [session, status, router]);

  // Show loading while redirecting
  return (
    <Container size="xl" py="xl" style={{ position: 'relative', minHeight: '100vh' }}>
      <LoadingOverlay visible />
    </Container>
  );
}
