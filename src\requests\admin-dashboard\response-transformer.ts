import * as z from 'zod';
import { ApiResponseSchema } from '../common';

export const AdminDashboardSchema = ApiResponseSchema(
  z.object({
    admin_info: z.object({
      id: z.string().uuid(),
      name: z.string(),
      email: z.string().email(),
      role: z.enum(['ADMIN', 'AUDITOR', 'SUPPORT']),
      status: z.enum(['ACTIVE', 'SUSPENDED']),
      email_verified: z.boolean(),
    }),
    dashboard_data: z.object({
      user_stats: z.object({
        total: z.number(),
        customers: z.number(),
        access_operators: z.number(),
        car_operators: z.number(),
        pending_approvals: z.number(),
      }),
      shipment_stats: z.object({
        total: z.number(),
        active: z.number(),
        delivered: z.number(),
        delivery_rate: z.number(),
      }),
      recent_users: z.array(
        z.object({
          id: z.string().uuid(),
          name: z.string(),
          email: z.string().email(),
          user_type: z.string(),
          status: z.string(),
          created_at: z.string().refine((s) => !Number.isNaN(Date.parse(s))),
        }),
      ),
      recent_shipments: z.array(z.any()),
      quick_actions: z.array(
        z.object({
          label: z.string(),
          action: z.string(),
          icon: z.string(),
        }),
      ),
    }),
  }),
);

// 2) Transform everything into camelCase:
export const dashboardApiRespons = (item: z.infer<typeof AdminDashboardSchema>) => ({
  adminInfo: {
    id: item.data.admin_info.id,
    name: item.data.admin_info.name,
    email: item.data.admin_info.email,
    role: item.data.admin_info.role,
    status: item.data.admin_info.status,
    emailVerified: item.data.admin_info.email_verified,
  },
  dashboardData: {
    // from user_stats
    totalUsers: item.data.dashboard_data.user_stats.total,
    totalCustomers: item.data.dashboard_data.user_stats.customers,
    totalAccessOperators: item.data.dashboard_data.user_stats.access_operators,
    totalCarOperators: item.data.dashboard_data.user_stats.car_operators,
    pendingApprovals: item.data.dashboard_data.user_stats.pending_approvals,

    // from shipment_stats
    totalShipments: item.data.dashboard_data.shipment_stats.total,
    activeShipments: item.data.dashboard_data.shipment_stats.active,
    deliveredShipments: item.data.dashboard_data.shipment_stats.delivered,
    deliveryRate: item.data.dashboard_data.shipment_stats.delivery_rate,

    // arrays
    recentUsers: item.data.dashboard_data.recent_users,
    recentShipments: item.data.dashboard_data.recent_shipments,

    // quick actions
    quickActions: item.data.dashboard_data.quick_actions,
  },
});
