import { z } from 'zod';
import { AdminDashboardSchema } from './response-transformer';

export type AdminDashboardBackendResponse = z.infer<typeof AdminDashboardSchema>;

export type AdminDashboardResponse = {
  adminInfo: {
    id: string;
    name: string;
    email: string;
    role: 'ADMIN' | 'AUDITOR' | 'SUPPORT';
    status: 'ACTIVE' | 'SUSPENDED';
    emailVerified: boolean;
  };
  dashboardData: {
    totalUsers: number;
    totalCustomers: number;
    totalAccessOperators: number;
    totalCarOperators: number;
    pendingApprovals: number;
    totalShipments: number;
    activeShipments: number;
    deliveredShipments: number;
    deliveryRate: number;
    recentUsers: {
      id: string;
      name: string;
      email: string;
      user_type: string;
      status: string;
      created_at: string;
    }[];
    recentShipments: unknown[];
    quickActions: {
      label: string;
      action: string;
      icon: string;
    }[];
  };
};
