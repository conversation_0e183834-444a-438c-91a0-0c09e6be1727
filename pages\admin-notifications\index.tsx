/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-lines */
import React, { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Text,
  Badge,
  ActionIcon,
  Tooltip,
  TextInput,
  Select,
  Table,
  Pagination,
  Center,
  Loader,
  Alert,
  Box,
  Menu,
  Modal,
} from '@mantine/core';
import {
  IconRefresh,
  IconCheck,
  IconTrash,
  IconSearch,
  IconFilter,
  IconBell,
  IconDots,
  IconEye,
} from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { useDebouncedValue, useDisclosure } from '@mantine/hooks';
import {
  getAdminNotificationsQuery,
  getUnreadCountQuery,
  markAsReadMutation,
  markAllAsReadMutation,
  deleteNotificationMutation,
  AdminNotificationType,
  AdminNotificationPriority,
} from '../../src/requests/admin-notifications';

export default function AdminNotificationsPage() {
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [type, setType] = useState<string | null>(null);
  const [priority, setPriority] = useState<string | null>(null);
  const [readStatus, setReadStatus] = useState<string | null>(null);
  const [debouncedSearch] = useDebouncedValue(search, 300);
  const [selectedNotification, setSelectedNotification] = useState<any>(null);
  const [detailsOpened, { open: openDetails, close: closeDetails }] = useDisclosure(false);

  const limit = 20;

  // Fetch notifications
  const {
    data: notificationsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    ...getAdminNotificationsQuery({
      pagination: { page, limit },
      filters: {
        search: debouncedSearch || undefined,
        type: type as AdminNotificationType || undefined,
        priority: priority as AdminNotificationPriority || undefined,
        read: readStatus === 'read' ? true : readStatus === 'unread' ? false : undefined,
      },
      sort: 'createdAt:desc',
    }),
  });

  // Fetch unread count
  const { data: unreadData } = useQuery(getUnreadCountQuery());

  // Mutations
  const markAsReadMut = useMutation({
    ...markAsReadMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
      notifications.show({
        title: 'Success',
        message: 'Notification marked as read',
        color: 'green',
      });
    },
  });

  const markAllAsReadMut = useMutation({
    ...markAllAsReadMutation(),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['adminNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
      notifications.show({
        title: 'Success',
        message: `Marked ${data.data.updatedCount} notifications as read`,
        color: 'green',
      });
    },
  });

  const deleteNotificationMut = useMutation({
    ...deleteNotificationMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadCount'] });
      notifications.show({
        title: 'Success',
        message: 'Notification deleted successfully',
        color: 'green',
      });
    },
  });

  const notificationsList = notificationsData?.data?.notifications || [];
  const pagination = notificationsData?.data?.pagination;
  const unreadCount = unreadData?.data?.unreadCount || 0;

  const handleMarkAsRead = (id: string) => {
    markAsReadMut.mutate({ id });
  };

  const handleMarkAllAsRead = () => {
    markAllAsReadMut.mutate();
  };

  const handleDelete = (id: string) => {
    deleteNotificationMut.mutate({ id });
  };

  const handleViewDetails = (notification: any) => {
    setSelectedNotification(notification);
    openDetails();
  };

  const clearFilters = () => {
    setSearch('');
    setType(null);
    setPriority(null);
    setReadStatus(null);
    setPage(1);
  };

  const getPriorityColor = (p: string) => {
    switch (p) {
      case 'URGENT': return 'red';
      case 'HIGH': return 'orange';
      case 'NORMAL': return 'blue';
      case 'LOW': return 'gray';
      default: return 'blue';
    }
  };

  const getTypeColor = (t: string) => {
    switch (t) {
      case 'SECURITY_ALERT': return 'red';
      case 'SYSTEM_ERROR': return 'red';
      case 'OPERATOR_NEEDS_APPROVAL': return 'orange';
      case 'USER_REGISTERED': return 'green';
      case 'ADMIN_CREATED': return 'blue';
      default: return 'gray';
    }
  };

  return (
    <Container size="xl">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between" align="center">
          <Group gap="sm">
            <IconBell size="2rem" />
            <div>
              <Title order={2}>Admin Notifications</Title>
              <Text c="dimmed" size="sm">
                Manage and view system notifications
              </Text>
            </div>
          </Group>

          <Group gap="sm">
            <Badge color="blue" size="lg">
              {unreadCount}
              {' '}
              unread
            </Badge>
            <Tooltip label="Refresh notifications">
              <ActionIcon
                variant="light"
                onClick={() => refetch()}
                loading={isLoading}
              >
                <IconRefresh size="1.1rem" />
              </ActionIcon>
            </Tooltip>
            {unreadCount > 0 && (
            <Button
              leftSection={<IconCheck size="1rem" />}
              onClick={handleMarkAllAsRead}
              loading={markAllAsReadMut.isPending}
            >
              Mark All as Read
            </Button>
            )}
          </Group>
        </Group>

        {/* Filters */}
        <Paper withBorder p="md">
          <Stack gap="md">
            <Group gap="md" align="end">
              <TextInput
                placeholder="Search notifications..."
                leftSection={<IconSearch size="1rem" />}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                style={{ flex: 1 }}
              />
              <Select
                placeholder="Type"
                data={[
                  { value: 'USER_REGISTERED', label: 'User Registered' },
                  { value: 'USER_EMAIL_VERIFIED', label: 'Email Verified' },
                  { value: 'OPERATOR_NEEDS_APPROVAL', label: 'Operator Approval' },
                  { value: 'USER_STATUS_CHANGED', label: 'User Status Changed' },
                  { value: 'SECURITY_ALERT', label: 'Security Alert' },
                  { value: 'ADMIN_CREATED', label: 'Admin Created' },
                  { value: 'SHIPMENT_CREATED', label: 'Shipment Created' },
                  { value: 'SYSTEM_ERROR', label: 'System Error' },
                  { value: 'BULK_OPERATION_COMPLETED', label: 'Bulk Operation' },
                  { value: 'SYSTEM_MAINTENANCE', label: 'System Maintenance' },
                ]}
                value={type}
                onChange={setType}
                clearable
                w={200}
              />
              <Select
                placeholder="Priority"
                data={[
                  { value: 'LOW', label: 'Low' },
                  { value: 'NORMAL', label: 'Normal' },
                  { value: 'HIGH', label: 'High' },
                  { value: 'URGENT', label: 'Urgent' },
                ]}
                value={priority}
                onChange={setPriority}
                clearable
                w={120}
              />
              <Select
                placeholder="Status"
                data={[
                  { value: 'unread', label: 'Unread' },
                  { value: 'read', label: 'Read' },
                ]}
                value={readStatus}
                onChange={setReadStatus}
                clearable
                w={120}
              />
              <Button
                variant="light"
                leftSection={<IconFilter size="1rem" />}
                onClick={clearFilters}
              >
                Clear
              </Button>
            </Group>
          </Stack>
        </Paper>

        {/* Content */}
        {error && (
        <Alert color="red" title="Error">
          Failed to load notifications. Please try again.
        </Alert>
        )}

        {isLoading ? (
          <Center py="xl">
            <Loader size="lg" />
          </Center>
        ) : (
          <Paper withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Title</Table.Th>
                  <Table.Th>Type</Table.Th>
                  <Table.Th>Priority</Table.Th>
                  <Table.Th>Created</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {notificationsList.length === 0 ? (
                  <Table.Tr>
                    <Table.Td colSpan={6}>
                      <Center py="xl">
                        <Stack align="center" gap="sm">
                          <IconBell size="3rem" color="gray" />
                          <Text c="dimmed">No notifications found</Text>
                        </Stack>
                      </Center>
                    </Table.Td>
                  </Table.Tr>
                ) : (
                  notificationsList.map((notification: any) => (
                    <Table.Tr
                      key={notification.id}
                      style={{
                        backgroundColor: notification.read ? 'transparent' : 'var(--mantine-color-blue-0)',
                      }}
                    >
                      <Table.Td>
                        <Box
                          style={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: notification.read ? 'var(--mantine-color-gray-4)' : 'var(--mantine-color-blue-6)',
                          }}
                        />
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={notification.read ? 400 : 600} size="sm">
                            {notification.title}
                          </Text>
                          <Text size="xs" c="dimmed" lineClamp={2}>
                            {notification.message}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getTypeColor(notification.notificationType)} size="sm">
                          {notification.notificationType ? notification.notificationType.replace(/_/g, ' ') : ''}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getPriorityColor(notification.priority)} size="sm">
                          {notification.priority}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(notification.createdAt).toLocaleDateString()}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {new Date(notification.createdAt).toLocaleTimeString()}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Menu shadow="md" width={200}>
                          <Menu.Target>
                            <ActionIcon variant="subtle" size="sm">
                              <IconDots size="1rem" />
                            </ActionIcon>
                          </Menu.Target>

                          <Menu.Dropdown>
                            <Menu.Item
                              leftSection={<IconEye size="1rem" />}
                              onClick={() => handleViewDetails(notification)}
                            >
                              View Details
                            </Menu.Item>
                            {!notification.read && (
                            <Menu.Item
                              leftSection={<IconCheck size="1rem" />}
                              onClick={() => handleMarkAsRead(notification.id)}
                              disabled={markAsReadMut.isPending}
                            >
                              Mark as Read
                            </Menu.Item>
                            )}
                            <Menu.Divider />
                            <Menu.Item
                              leftSection={<IconTrash size="1rem" />}
                              color="red"
                              onClick={() => handleDelete(notification.id)}
                              disabled={deleteNotificationMut.isPending}
                            >
                              Delete
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Table.Td>
                    </Table.Tr>
                  ))
                )}
              </Table.Tbody>
            </Table>

            {/* Pagination */}
            {pagination && pagination.totalPages > 1 && (
            <Group justify="center" p="md">
              <Pagination
                value={page}
                onChange={setPage}
                total={pagination.totalPages}
                size="sm"
              />
            </Group>
            )}
          </Paper>
        )}
      </Stack>

      {/* Notification Details Modal */}
      <Modal
        opened={detailsOpened}
        onClose={closeDetails}
        title="Notification Details"
        size="lg"
      >
        {selectedNotification && (
        <Stack gap="md">
          <Group justify="space-between">
            <Badge color={getPriorityColor(selectedNotification.priority)}>
              {selectedNotification.priority}
            </Badge>
            <Badge color={getTypeColor(selectedNotification.notificationType)}>
              {selectedNotification.notificationType ? selectedNotification.notificationType.replace(/_/g, ' ') : ''}
            </Badge>
          </Group>

          <div>
            <Text fw={600} size="lg">
              {selectedNotification.title}
            </Text>
            <Text mt="sm">
              {selectedNotification.message}
            </Text>
          </div>

          <Group gap="md">
            <div>
              <Text size="sm" c="dimmed">Created</Text>
              <Text size="sm">
                {new Date(selectedNotification.createdAt).toLocaleString()}
              </Text>
            </div>
            <div>
              <Text size="sm" c="dimmed">Status</Text>
              <Text size="sm">
                {selectedNotification.read ? 'Read' : 'Unread'}
              </Text>
            </div>
            {selectedNotification.readAt && (
            <div>
              <Text size="sm" c="dimmed">Read At</Text>
              <Text size="sm">
                {new Date(selectedNotification.readAt).toLocaleString()}
              </Text>
            </div>
            )}
          </Group>

          {selectedNotification.metadata && (
          <div>
            <Text size="sm" c="dimmed" mb="xs">Metadata</Text>
            <Paper withBorder p="sm">
              <Text size="xs" style={{ fontFamily: 'monospace' }}>
                {JSON.stringify(selectedNotification.metadata, null, 2)}
              </Text>
            </Paper>
          </div>
          )}

          <Group justify="flex-end" mt="md">
            {!selectedNotification.read && (
            <Button
              leftSection={<IconCheck size="1rem" />}
              onClick={() => {
                handleMarkAsRead(selectedNotification.id);
                closeDetails();
              }}
              loading={markAsReadMut.isPending}
            >
              Mark as Read
            </Button>
            )}
            <Button variant="light" onClick={closeDetails}>
              Close
            </Button>
          </Group>
        </Stack>
        )}
      </Modal>
    </Container>
  );
}
