/* eslint-disable react/require-default-props */
import {
  Paper, Text, Group, ThemeIcon, Stack,
} from '@mantine/core';
import { ReactNode } from 'react';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  color?: string;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export function StatsCard({
  title,
  value,
  icon,
  color = 'blue',
  description,
  trend,
}: StatsCardProps) {
  return (
    <Paper withBorder p="md" radius="md" shadow="sm">
      <Group justify="space-between">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
            {title}
          </Text>
          <Text size="xl" fw={700}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </Text>
          {description && (
          <Text size="xs" c="dimmed">
            {description}
          </Text>
          )}
          {trend && (
          <Text
            size="xs"
            c={trend.isPositive ? 'green' : 'red'}
            fw={500}
          >
            {trend.isPositive ? '+' : ''}
            {trend.value}
            %
          </Text>
          )}
        </Stack>
        <ThemeIcon
          color={color}
          variant="light"
          size="xl"
          radius="md"
        >
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}
