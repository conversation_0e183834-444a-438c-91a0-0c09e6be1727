import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, getJwt } from '../../../../src/utils';
import { BACKEND_API } from '../../../../src/lib/axios';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../../src/data';
import { transformSystemSettingsResponse } from '../../../../src/requests/admin-settings';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === apiMethods.POST) {
    try {
      // Reset doesn't require body validation since it uses default values
      const { data } = await BACKEND_API(req).post(
        API_ENDPOINT.settings.reset(),
        {}, // Empty body since reset uses defaults
        {
          headers: { authorization: token },
        },
      );

      // Handle case where data might be null or empty
      const settingsData = data.data || {};

      // Transform the data
      const transformedData = transformSystemSettingsResponse(settingsData);

      // Return response
      return res.status(200).json({
        success: data.success,
        message: data.message,
        data: transformedData,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: 'Method not allowed' });
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
}

export default handler;
