/* eslint-disable linebreak-style, no-underscore-dangle, @typescript-eslint/no-explicit-any, eol-last */
import { format } from 'date-fns';

/**
 * Format the given date (string | number | Date) to `dd/MM/yyyy HH:mm` (24h).
 */
export function formatDateDDMMYYYY(dateInput: string | number | Date): string {
  try {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput);
    if (Number.isNaN(date.getTime())) throw new Error('Invalid date');
    return format(date, 'dd/MM/yyyy HH:mm');
  } catch {
    return 'Invalid date';
  }
}

// ------------------------------------------------------------
// Monkey-patch Date.prototype.toLocaleDateString so that calls
// without explicit formatting options default to our
// `dd/MM/yyyy HH:mm` format instead of the browser / OS locale.
// ------------------------------------------------------------

/* eslint-disable no-extend-native,func-names */
if (!(globalThis as any).__naqalatDatePatchApplied) {
  const originalToLocaleDateString = Date.prototype.toLocaleDateString;
  // @ts-ignore
  (globalThis as any).__naqalatDatePatchApplied = true;

  Date.prototype.toLocaleDateString = function (locale?: string | undefined, options?: Intl.DateTimeFormatOptions) {
    // If the caller provided custom options, fall back to the original behaviour
    if (options && Object.keys(options).length > 0) {
      return originalToLocaleDateString.call(this, locale, options);
    }
    // Otherwise enforce dd/MM/yyyy HH:mm
    return formatDateDDMMYYYY(this);
  };
}
/* eslint-enable no-extend-native,func-names */

/* EOF */