/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable import/no-extraneous-dependencies */
import {
  describe, it, expect, jest, beforeEach, afterEach,
} from '@jest/globals';
import { CLIENT_API } from '../../../lib/axios';
// import { handleApiError } from '../../../utils/handle-backend-error';
import {
  getAdminNotificationsQuery,
  getUnreadCountQuery,
  markAsReadMutation,
  markAllAsReadMutation,
  deleteNotificationMutation,
} from '../calls';
import { AdminNotificationType, AdminNotificationPriority } from '../types';

// Mock dependencies
jest.mock('../../../lib/axios');
// jest.mock('../../../utils/handle-backend-error');

const mockClientAPI = CLIENT_API as jest.Mocked<typeof CLIENT_API>;
// const mockHandleApiError = handleApiError as jest.MockedFunction<typeof handleApiError>;

describe('Admin Notifications API Calls', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getAdminNotificationsQuery', () => {
    const mockQueryProps = {
      filters: {
        search: 'test',
        type: AdminNotificationType.USER_REGISTERED,
        priority: AdminNotificationPriority.HIGH,
        read: false,
      },
      pagination: {
        page: 1,
        limit: 20,
      },
      sort: 'createdAt:desc',
    };

    const mockApiResponse = {
      data: {
        success: true,
        message: 'Notifications retrieved successfully',
        data: {
          notifications: [
            {
              id: 'notification-1',
              admin_id: 'admin-1',
              shipment_id: null,
              notification_type: 'USER_REGISTERED',
              title: 'New User Registration',
              message: 'A new customer has registered',
              priority: 'HIGH',
              read: false,
              read_at: null,
              metadata: { user_id: 'user-1' },
              expires_at: null,
              created_at: '2024-01-15T10:30:00Z',
              updated_at: '2024-01-15T10:30:00Z',
              shipment: null,
            },
          ],
          pagination: {
            page: 1,
            limit: 20,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
          unreadCount: 1,
        },
      },
    };

    it('should create query configuration with correct parameters', () => {
      const query = getAdminNotificationsQuery(mockQueryProps);

      expect(query.queryKey).toEqual([
        'adminNotifications',
        mockQueryProps.filters,
        mockQueryProps.pagination,
        mockQueryProps.sort,
      ]);
      expect(query.refetchOnWindowFocus).toBe(false);
      expect(typeof query.queryFn).toBe('function');
    });

    it('should call CLIENT_API.get with correct endpoint and params', async () => {
      mockClientAPI.get.mockResolvedValue(mockApiResponse);
      const query = getAdminNotificationsQuery(mockQueryProps);

      await query.queryFn({});

      expect(mockClientAPI.get).toHaveBeenCalledWith('/notifications/my', {
        params: {
          search: 'test',
          type: AdminNotificationType.USER_REGISTERED,
          priority: AdminNotificationPriority.HIGH,
          read: false,
          fromDate: undefined,
          toDate: undefined,
          shipmentId: undefined,
          sort: 'createdAt:desc',
          limit: 20,
          page: 1,
        },
      });
    });
  });

  describe('getUnreadCountQuery', () => {
    const mockApiResponse = {
      data: {
        success: true,
        message: 'Unread count retrieved successfully',
        data: {
          unreadCount: 5,
        },
      },
    };

    it('should create query configuration', () => {
      const query = getUnreadCountQuery();

      expect(query.queryKey).toEqual(['unreadCount']);
      expect(query.refetchOnWindowFocus).toBe(false);
      expect(typeof query.queryFn).toBe('function');
    });

    it('should call CLIENT_API.get with correct endpoint', async () => {
      mockClientAPI.get.mockResolvedValue(mockApiResponse);
      const query = getUnreadCountQuery();

      await query.queryFn();

      expect(mockClientAPI.get).toHaveBeenCalledWith('/notifications/unread-count');
    });
  });

  describe('markAsReadMutation', () => {
    const mockApiResponse = {
      data: {
        success: true,
        message: 'Notification marked as read',
        data: {
          notification: {
            id: 'notification-1',
            read: true,
            read_at: '2024-01-15T11:00:00Z',
          },
        },
      },
    };

    it('should create mutation configuration', () => {
      const mutation = markAsReadMutation();

      expect(mutation.mutationKey).toEqual(['markAsRead']);
      expect(typeof mutation.mutationFn).toBe('function');
    });

    it('should call CLIENT_API.put with correct endpoint', async () => {
      mockClientAPI.put.mockResolvedValue(mockApiResponse);
      const mutation = markAsReadMutation();

      await mutation.mutationFn({ id: 'notification-1' });

      expect(mockClientAPI.put).toHaveBeenCalledWith('/notifications/notification-1/read');
    });
  });

  describe('markAllAsReadMutation', () => {
    const mockApiResponse = {
      data: {
        success: true,
        message: 'All notifications marked as read',
        data: {
          updatedCount: 5,
        },
      },
    };

    it('should create mutation configuration', () => {
      const mutation = markAllAsReadMutation();

      expect(mutation.mutationKey).toEqual(['markAllAsRead']);
      expect(typeof mutation.mutationFn).toBe('function');
    });

    it('should call CLIENT_API.put with correct endpoint', async () => {
      mockClientAPI.put.mockResolvedValue(mockApiResponse);
      const mutation = markAllAsReadMutation();

      await mutation.mutationFn();

      expect(mockClientAPI.put).toHaveBeenCalledWith('/notifications/mark-all-read');
    });
  });

  describe('deleteNotificationMutation', () => {
    const mockApiResponse = {
      data: {
        success: true,
        message: 'Notification deleted successfully',
      },
    };

    it('should create mutation configuration', () => {
      const mutation = deleteNotificationMutation();

      expect(mutation.mutationKey).toEqual(['deleteNotification']);
      expect(typeof mutation.mutationFn).toBe('function');
    });

    it('should call CLIENT_API.delete with correct endpoint', async () => {
      mockClientAPI.delete.mockResolvedValue(mockApiResponse);
      const mutation = deleteNotificationMutation();

      await mutation.mutationFn({ id: 'notification-1' });

      expect(mockClientAPI.delete).toHaveBeenCalledWith('/notifications/notification-1');
    });
  });
});
