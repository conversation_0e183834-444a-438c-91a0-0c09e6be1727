import React from 'react';
import {
  Badge, Group, ActionIcon, Text, Stack,
} from '@mantine/core';
import { IconEye } from '@tabler/icons-react';
import { DataTableColumn } from '../common/DataTable';
import {
  User, UserStatus, ApprovalStatus,
} from '../../types/admin.types';

// Helper functions for badge colors
export const getStatusColor = (status: UserStatus): string => {
  switch (status) {
    case UserStatus.ACTIVE:
      return 'green';
    case UserStatus.PENDING:
      return 'yellow';
    case UserStatus.SUSPENDED:
      return 'red';
    default:
      return 'gray';
  }
};

export const getApprovalStatusColor = (status: ApprovalStatus): string => {
  switch (status) {
    case ApprovalStatus.APPROVED:
      return 'green';
    case ApprovalStatus.PENDING:
      return 'yellow';
    case ApprovalStatus.REJECTED:
      return 'red';
    default:
      return 'gray';
  }
};

export const formatDate = (dateString: string): string => new Date(dateString).toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'short',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit',
});

// Define the columns for the users table
export const getUsersColumns = (
  onViewUser: (user: User) => void,
): DataTableColumn<User>[] => [
  {
    label: 'Name',
    accessor: 'name',
    sortable: true,
    render: (user: User) => (
      <div>
        <Text fw={500}>{user.name}</Text>
        {user.phone && <Text size="xs" c="dimmed">{user.phone}</Text>}
      </div>
    ),
  },
  {
    label: 'Email',
    accessor: 'email',
    sortable: true,
  },
  {
    label: 'Type',
    accessor: 'user_type',
    sortable: true,
    render: (user: User) => (
      <Badge variant="light" color="blue">
        {user.user_type.replace('_', ' ')}
      </Badge>
    ),
  },
  {
    label: 'Status',
    accessor: 'status',
    sortable: true,
    render: (user: User) => (
      <Badge color={getStatusColor(user.status as UserStatus)}>
        {user.status}
      </Badge>
    ),
  },
  {
    label: 'Approval',
    accessor: 'approval_status',
    sortable: false,
    render: (user: User) => (
      <Stack gap="xs">
        <Badge color={getApprovalStatusColor((user.approval_status as ApprovalStatus) || ApprovalStatus.APPROVED)}>
          {user.approval_status || ApprovalStatus.APPROVED}
        </Badge>
      </Stack>
    ),
  },
  {
    label: 'Created',
    accessor: 'created_at',
    sortable: true,
    render: (user: User) => formatDate(user.created_at),
  },
  {
    label: 'Actions',
    accessor: 'actions',
    render: (user: User) => (
      <Group gap="xs">
        <ActionIcon
          variant="light"
          color="blue"
          onClick={() => onViewUser(user)}
          title="View Full Details"
        >
          <IconEye size="1rem" />
        </ActionIcon>
      </Group>
    ),
  },
];
