import { useState } from 'react';
import Link from 'next/link';
import {
  TextInput,
  PasswordInput,
  Button,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Anchor,
  Stack,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { signIn } from 'next-auth/react';
import { z } from 'zod';

type LoginFormValues = {
  email: string;
  password: string;
};

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

export default function AdminLoginPage() {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<LoginFormValues>({
    validate: (values) => {
      const result = loginSchema.safeParse(values);
      const errors: Partial<Record<keyof LoginFormValues, string | null>> = {};

      if (!result.success) {
        result.error.issues.forEach((issue) => {
          const field = issue.path[0] as keyof LoginFormValues;
          errors[field] = issue.message;
        });
      }

      return errors as Record<keyof LoginFormValues, string | null>;
    },
    initialValues: {
      email: '',
      password: '',
    },
  });

  const handleSubmit = async (values: LoginFormValues) => {
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        email: values.email,
        password: values.password,
        redirect: true,
        callbackUrl: '/dashboard',
      });

      // If we reach here, there was an error (since redirect: true would redirect on success)
      if (result?.error) {
        notifications.show({
          title: 'Error',
          message: 'Invalid credentials',
          color: 'red',
        });
        setIsLoading(false);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Something went wrong';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
      setIsLoading(false);
    }
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center">
        Admin Login
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        Access the NAQALAT Admin Dashboard
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <TextInput
              required
              label="Email Address"
              placeholder="Enter your admin email"
              {...form.getInputProps('email')}
            />
            <PasswordInput
              required
              label="Password"
              placeholder="Enter your password"
              {...form.getInputProps('password')}
            />
            <Group justify="space-between" mt="lg">
              <Link href="/auth/forgot-password" passHref legacyBehavior>
                <Anchor component="a" size="sm">
                  Forgot Password?
                </Anchor>
              </Link>
            </Group>
            <Button
              type="submit"
              fullWidth
              mt="xl"
              loading={isLoading}
            >
              Sign In
            </Button>
          </Stack>
        </form>
      </Paper>
    </Container>
  );
}
