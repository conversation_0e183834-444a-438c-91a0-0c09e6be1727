import { z } from 'zod';

export const updateSystemSettingsRequest = z.object({
  minDistanceKm: z.number().optional(),
  maxShipmentsPerDay: z.number().optional(),
  maxShipmentsPerUser: z.number().optional(),
  maxPendingShipments: z.number().optional(),
  requirePhotoProof: z.boolean().optional(),
  maxFailedLogins: z.number().optional(),
  reviewPeriodHours: z.number().optional(),
  enable2fa: z.boolean().optional(),
  gpsToleranceMeters: z.number().optional(),
});

export const updateSystemSettingsBackendRequestSchema = updateSystemSettingsRequest.transform((data) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const result: Record<string, any> = {};

  if (data.minDistanceKm !== undefined) result.min_distance_km = data.minDistanceKm;
  if (data.maxShipmentsPerDay !== undefined) result.max_shipments_per_day = data.maxShipmentsPerDay;
  if (data.maxShipmentsPerUser !== undefined) result.max_shipments_per_user = data.maxShipmentsPerUser;
  if (data.maxPendingShipments !== undefined) result.max_pending_shipments = data.maxPendingShipments;
  if (data.requirePhotoProof !== undefined) result.require_photo_proof = data.requirePhotoProof;
  if (data.maxFailedLogins !== undefined) result.max_failed_logins = data.maxFailedLogins;
  if (data.reviewPeriodHours !== undefined) result.review_period_hours = data.reviewPeriodHours;
  if (data.enable2fa !== undefined) result.enable_2fa = data.enable2fa;
  if (data.gpsToleranceMeters !== undefined) result.gps_tolerance_meters = data.gpsToleranceMeters;

  return result;
});

export const resetSystemSettingsBackendRequestSchema = z.object({
  // Reset doesn't require any specific fields, keeping empty for consistency
}).optional();
