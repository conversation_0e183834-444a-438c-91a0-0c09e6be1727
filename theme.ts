import { createTheme, DEFAULT_THEME } from '@mantine/core';

// Base theme configuration
const baseTheme = {
  components: {
    Modal: {
      defaultProps: {
        zIndex: 10050,
      },
    },
    Tooltip: {
      defaultProps: {
        zIndex: 10600,
      },
    },
    Popover: {
      defaultProps: {
        zIndex: 10500,
      },
    },
  },
};

// Create theme with English font
export const createLocaleTheme = () => {
  const fontFamily = 'var(--font-roboto-condensed), "Roboto Condensed", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif';

  return createTheme({
    ...baseTheme,
    fontFamily,
  });
};

// Default theme (English)
export const theme = createLocaleTheme();
