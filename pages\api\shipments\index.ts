import { NextApiRequest, NextApiResponse } from 'next';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { ShipmentsListResponse, ShipmentDetailApiResponse } from '../../../src/requests/shipment/response-transformer';
import { returnShipmentsListParams } from '../../../src/requests/shipment/params';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id: shipmentId } = req.query;
  if (shipmentId && shipmentId !== 'undefined' && req.method === apiMethods.GET) {
    try {
      const { data } = await BACKEND_API(req).get(
        API_ENDPOINT.shipments.list(`${shipmentId}`),
        {
          headers: {
            authorization: token,
          },
        },
      );

      return res.status(HTTP_CODE.SUCCESS).json(ShipmentDetailApiResponse(data.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  } else if (req.method === apiMethods.GET) {
    try {
      const { data } = await BACKEND_API(req).get(API_ENDPOINT.shipments.list(), {
        headers: { authorization: token },
        params: {
          ...returnShipmentsListParams(req),
        },
      });

      const transformedResponse = {
        success: data.success,
        message: data.message,
        data: {
          shipments: data.data,
          pagination: {
            ...data.pagination,
            hasNext: Boolean(data.pagination?.hasNext),
            hasPrev: Boolean(data.pagination?.hasPrev),
          },
        },
      };

      return createApiResponse(
        res,
        ShipmentsListResponse,
        transformedResponse,
      );
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(HTTP_CODE.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
