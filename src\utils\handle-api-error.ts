/* eslint-disable sonarjs/cognitive-complexity */
import { HTTP_CODE } from '../data';
import { authPages } from '../data/routes';
// Removed unused error type imports
import { notifications } from '@mantine/notifications';
import { AxiosError } from 'axios';
import { signIn, signOut } from 'next-auth/react';

// Account blocked error handling removed as the routes are not implemented

const handleUnauthorizedCondition = () => {
  if (
    typeof window !== 'undefined'
    && authPages.includes(window.location.pathname)
  ) {
    signIn('keycloak');
  }
  // Iframe handling removed as it's not used in this application
};

const errorCache: { [key: string]: number } = {};

export const handleApiError = async <T extends object>(
  error: AxiosError<{
    message: { fallback: string; key: string; params: T };
    code: number;
  }>,
  isQueryRequest?: boolean,
  withoutNotification?: boolean,
) => {
  // this is not a pure function and it is ok since it will be used only in the context of this app
  if (error.response?.status === HTTP_CODE.UNAUTHORIZED) {
    // if authorized then logout
    signOut({ redirect: false });
    handleUnauthorizedCondition();
  } else if (error.response && !withoutNotification) {
    const {
      response: {
        data: { message },
        status,
      },
    } = error;

    // Handle 500 errors with proper translation
    let errorMessage: string;
    let cacheKey: string;

    if (status === 500) {
      // For 500 errors, use a standard message
      errorMessage = 'Internal server error occurred';
      cacheKey = 'internalServerError';
    } else if (message?.key) {
      // For other errors with message key, use fallback
      errorMessage = message.fallback || 'An error occurred';
      cacheKey = message.key;
    } else {
      // Fallback for errors without proper message structure
      errorMessage = message?.fallback || 'An error occurred';
      cacheKey = 'generalError';
    }

    const canShowNotification = Date.now() - (errorCache[cacheKey ?? ''] ?? 0) > 30 * 1000;

    if (
      typeof window !== 'undefined'
      && canShowNotification
      && isQueryRequest
    ) {
      errorCache[cacheKey || ''] = Date.now();
      notifications.show({
        message: errorMessage,
        color: 'red',
      });
    } else if (!isQueryRequest) {
      notifications.show({
        message: errorMessage,
        color: 'red',
      });
    }
  }
};
