import z from 'zod';
import { Pagination } from '../../types';
import { updateSystemSettingsRequest } from './request-transformer';

export type UpdateSettings = z.infer<typeof updateSystemSettingsRequest>;

export type Filter = {
  search?: string;
  createdAtGte?: string | null;
  createdAtLte?: string | null;
  userType?: string;
  status?: string;
  approvalStatus?: string;
};

export interface getSettingsQueryProps {
  pagination?: Pagination;
  filters?: Filter;
  sort?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: any;
}

export interface ResetSettingsResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    min_distance_km: number;
    max_shipments_per_day: number;
    max_shipments_per_user: number;
    max_pending_shipments: number;
    require_photo_proof: boolean;
    max_failed_logins: number;
    review_period_hours: number;
    enable_2fa: boolean;
    gps_tolerance_meters: number;
    updated_by: string | null;
    created_at: string;
    updated_at: string;
  };
}
