/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable max-lines */
/* eslint-disable no-nested-ternary */
import { useState } from 'react';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Table,
  Badge,
  ActionIcon,
  Pagination,
  LoadingOverlay,
  Alert,
  Text,
  Stack,
  Modal,
  TextInput,
  Select,
  PasswordInput,
} from '@mantine/core';
import {
  IconPlus,
  IconEye,
  IconEdit,
  IconTrash,
  IconUserCheck,
  IconUserX,
  IconRefresh,
  IconAlertCircle,
} from '@tabler/icons-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  getAdminListQuery,
  createAdminMutation,
  updateAdminMutation,
  deleteAdminMutation,
  AdminListParamsType,
  CreateAdminRequestType,
  UpdateAdminRequestType,
} from '../../src/requests/admin-management';
import { AdminRole, UserStatus, AdminUser } from '../../src/types/admin.types';
import { useAdminAuthContext } from '../../src/contexts/AdminAuthContext';
import { AdminRouteGuard } from '../../src/components/auth/AdminRouteGuard';

const ROLE_OPTIONS = [
  { value: AdminRole.ADMIN, label: 'Admin' },
  { value: AdminRole.SUPER_ADMIN, label: 'Super Admin' },
];

export default function AdminsPage() {
  const queryClient = useQueryClient();
  const { admin: currentAdmin } = useAdminAuthContext();
  const [selectedAdmin, setSelectedAdmin] = useState<AdminUser | null>(null);

  // Modal states
  const [createModalOpened, { open: openCreateModal, close: closeCreateModal }] = useDisclosure(false);
  const [editModalOpened, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [detailModalOpened, { open: openDetailModal, close: closeDetailModal }] = useDisclosure(false);

  // Filter state
  const [filters, setFilters] = useState<AdminListParamsType>({
    page: 1,
    limit: 20,
  });

  // Forms
  const createForm = useForm<CreateAdminRequestType>({
    initialValues: {
      name: '',
      email: '',
      password: '',
      role: AdminRole.ADMIN,
    },
    validate: {
      name: (value) => (!value ? 'Name is required' : value.length < 2 ? 'Name must be at least 2 characters' : null),
      email: (value) => (!value ? 'Email is required' : !/\S+@\S+\.\S+/.test(value) ? 'Invalid email format' : null),
      password: (value) => (!value ? 'Password is required' : value.length < 6 ? 'Password must be at least 6 characters' : null),
      role: (value) => (!value ? 'Role is required' : null),
    },
  });

  // Queries and mutations
  const {
    data: adminsData,
    isLoading,
    error,
    refetch,
  } = useQuery(getAdminListQuery(filters));

  const createMutation = useMutation({
    mutationKey: createAdminMutation.mutationKey,
    mutationFn: createAdminMutation.mutationFn,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'Admin created successfully',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-list'] });
      closeCreateModal();
      createForm.reset();
    },
    onError: (mutationError: unknown) => {
      const errorMessage = mutationError && typeof mutationError === 'object' && 'message' in mutationError
        ? (mutationError as { message: string }).message
        : 'Failed to create admin';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    },
  });

  const updateMutation = useMutation({
    mutationKey: updateAdminMutation.mutationKey,
    mutationFn: updateAdminMutation.mutationFn,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'Admin updated successfully',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-list'] });
      closeEditModal();
      setSelectedAdmin(null);
    },
    onError: (mutationError: unknown) => {
      const errorMessage = mutationError && typeof mutationError === 'object' && 'message' in mutationError
        ? (mutationError as { message: string }).message
        : 'Failed to update admin';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    },
  });

  // const statusMutation = useMutation({
  //   mutationKey: changeUserStatusMutation.mutationKey,
  //   mutationFn: changeUserStatusMutation.mutationFn,
  //   onSuccess: (data) => {
  //     notifications.show({
  //       title: 'Success',
  //       message: data.message || 'Admin status updated successfully',
  //       color: 'green',
  //     });
  //     queryClient.invalidateQueries({ queryKey: ['admin-list'] });
  //   },
  //   onError: (mutationError: unknown) => {
  //     const errorMessage = mutationError && typeof mutationError === 'object' && 'message' in mutationError
  //       ? (mutationError as { message: string }).message
  //       : 'Failed to update admin status';
  //     notifications.show({
  //       title: 'Error',
  //       message: errorMessage,
  //       color: 'red',
  //     });
  //   },
  // });

  const deleteMutation = useMutation({
    mutationKey: deleteAdminMutation.mutationKey,
    mutationFn: deleteAdminMutation.mutationFn,
    onSuccess: (data) => {
      notifications.show({
        title: 'Success',
        message: data.message || 'Admin deleted successfully',
        color: 'green',
      });
      queryClient.invalidateQueries({ queryKey: ['admin-list'] });
    },
    onError: (mutationError: unknown) => {
      const errorMessage = mutationError && typeof mutationError === 'object' && 'message' in mutationError
        ? (mutationError as { message: string }).message
        : 'Failed to delete admin';
      notifications.show({
        title: 'Error',
        message: errorMessage,
        color: 'red',
      });
    },
  });

  const editForm = useForm<Omit<UpdateAdminRequestType, 'adminId'>>({
    initialValues: {
      name: '',
      email: '',
      role: AdminRole.ADMIN,
    },
    validate: {
      name: (value) => (!value ? 'Name is required' : value.length < 2 ? 'Name must be at least 2 characters' : null),
      email: (value) => (!value ? 'Email is required' : !/\S+@\S+\.\S+/.test(value) ? 'Invalid email format' : null),
      role: (value) => (!value ? 'Role is required' : null),
    },
  });

  // Event handlers
  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleCreateAdmin = (values: CreateAdminRequestType) => {
    createMutation.mutate(values);
  };

  const handleEditAdmin = (values: Omit<UpdateAdminRequestType, 'adminId'>) => {
    if (selectedAdmin) {
      updateMutation.mutate({
        adminId: selectedAdmin.id,
        ...values,
      });
    }
  };

  const handleViewAdmin = (admin: { id: string; name: string; email: string; role: string; status: string; created_at: string; updated_at: string; last_login?: string }) => {
    setSelectedAdmin(admin as AdminUser);
    openDetailModal();
  };

  const handleEditClick = (admin: { id: string; name: string; email: string; role: string; status: string; created_at: string; updated_at: string; last_login?: string }) => {
    setSelectedAdmin(admin as AdminUser);
    editForm.setValues({
      name: admin.name,
      email: admin.email,
      role: admin.role as AdminRole,
    });
    openEditModal();
  };

  const handleStatusChange = (adminId: string, status: UserStatus) => {
    // const newStatus = status === UserStatus.INACTIVE ? UserStatus.SUSPENDED : UserStatus.ACTIVE;
    // statusMutation.mutate({ userId: adminId, status: newStatus });
  };

  const handleDeleteAdmin = (adminId: string) => {
    // eslint-disable-next-line no-restricted-globals, no-alert
    if (confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
      deleteMutation.mutate(adminId);
    }
  };

  // Helper functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'green';
      case 'INACTIVE':
      case 'SUSPENDED':
        return 'red';
      case 'PENDING':
        return 'orange';
      default:
        return 'gray';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN':
        return 'purple';
      case 'ADMIN':
        return 'blue';
      default:
        return 'gray';
    }
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  // eslint-disable-next-line arrow-body-style
  const canManageAdmin = (admin: { id: string; name: string; email: string; role: string; status: string; created_at: string; updated_at: string; last_login?: string }) => {
    // Super admin can manage all admins except themselves
    // Regular admin cannot manage other admins
    return currentAdmin?.role === AdminRole.SUPER_ADMIN && admin.id !== currentAdmin.id;
  };
  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading admins"
          color="red"
          variant="light"
        >
          <Text mb="md">
            Failed to load admin data. Please try again.
          </Text>
          <Button variant="outline" onClick={() => refetch()}>
            Retry
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <AdminRouteGuard requiredRole={AdminRole.SUPER_ADMIN}>
      <Container size="xl" py="xl">
        <Group justify="space-between" mb="lg">
          <Title order={1}>Admin Management</Title>
          <Group>
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="outline"
              onClick={() => refetch()}
              loading={isLoading}
            >
              Refresh
            </Button>
            <Button
              leftSection={<IconPlus size="1rem" />}
              onClick={openCreateModal}
            >
              Create Admin
            </Button>
          </Group>
        </Group>

        {/* Admins Table */}
        <Paper withBorder>
          <LoadingOverlay visible={isLoading} />

          {adminsData?.data.admins.length === 0 ? (
            <Text ta="center" py="xl" c="dimmed">
              No admins found
            </Text>
          ) : (
            <>
              <Table.ScrollContainer minWidth={800}>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Name</Table.Th>
                      <Table.Th>Email</Table.Th>
                      <Table.Th>Role</Table.Th>
                      <Table.Th>Status</Table.Th>
                      <Table.Th>Created</Table.Th>
                      <Table.Th>Last Login</Table.Th>
                      <Table.Th>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {adminsData?.data.admins.map((admin) => (
                      <Table.Tr key={admin.id}>
                        <Table.Td>
                          <Text fw={500}>{admin.name}</Text>
                          {admin.id === currentAdmin?.id && (
                            <Badge size="xs" color="blue" variant="light">
                              You
                            </Badge>
                          )}
                        </Table.Td>
                        <Table.Td>{admin.email}</Table.Td>
                        <Table.Td>
                          <Badge color={getRoleColor(admin.role)}>
                            {admin.role?.replace('_', ' ') || 'Admin'}
                          </Badge>
                        </Table.Td>
                        <Table.Td>
                          <Badge color={getStatusColor(admin.status)}>
                            {admin.status}
                          </Badge>
                        </Table.Td>
                        <Table.Td>{formatDate(admin.created_at)}</Table.Td>
                        <Table.Td>
                          {admin.last_login ? formatDate(admin.last_login) : 'Never'}
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <ActionIcon
                              variant="light"
                              color="blue"
                              onClick={() => handleViewAdmin(admin)}
                            >
                              <IconEye size="1rem" />
                            </ActionIcon>

                            {canManageAdmin(admin) && (
                              <>
                                <ActionIcon
                                  variant="light"
                                  color="orange"
                                  onClick={() => handleEditClick(admin)}
                                >
                                  <IconEdit size="1rem" />
                                </ActionIcon>

                                {admin.status === UserStatus.ACTIVE ? (
                                  <ActionIcon
                                    variant="light"
                                    color="red"
                                    onClick={() => handleStatusChange(admin.id, UserStatus.INACTIVE)}
                                    // loading={statusMutation.isPending}
                                  >
                                    <IconUserX size="1rem" />
                                  </ActionIcon>
                                ) : (
                                  <ActionIcon
                                    variant="light"
                                    color="green"
                                    onClick={() => handleStatusChange(admin.id, UserStatus.ACTIVE)}
                                    // loading={statusMutation.isPending}
                                  >
                                    <IconUserCheck size="1rem" />
                                  </ActionIcon>
                                )}

                                <ActionIcon
                                  variant="light"
                                  color="red"
                                  onClick={() => handleDeleteAdmin(admin.id)}
                                  loading={deleteMutation.isPending}
                                >
                                  <IconTrash size="1rem" />
                                </ActionIcon>
                              </>
                            )}
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    ))}
                  </Table.Tbody>
                </Table>
              </Table.ScrollContainer>

              {/* Pagination */}
              {adminsData?.data.pagination && adminsData.data.pagination.totalPages > 1 && (
                <Group justify="center" p="md">
                  <Pagination
                    value={adminsData.data.pagination.page}
                    onChange={handlePageChange}
                    total={adminsData.data.pagination.totalPages}
                  />
                </Group>
              )}
            </>
          )}
        </Paper>

        {/* Create Admin Modal */}
        <Modal
          opened={createModalOpened}
          onClose={closeCreateModal}
          title="Create New Admin"
          size="md"
        >
          <form onSubmit={createForm.onSubmit(handleCreateAdmin)}>
            <Stack gap="md">
              <TextInput
                withAsterisk
                label="Full Name"
                placeholder="Enter admin's full name"
                {...createForm.getInputProps('name')}
              />
              <TextInput
                withAsterisk
                label="Email Address"
                placeholder="Enter admin's email"
                {...createForm.getInputProps('email')}
              />
              <PasswordInput
                withAsterisk
                label="Password"
                placeholder="Enter password"
                {...createForm.getInputProps('password')}
              />
              <Select
                withAsterisk
                label="Role"
                placeholder="Select admin role"
                data={ROLE_OPTIONS}
                {...createForm.getInputProps('role')}
              />
              <Group justify="flex-end" mt="md">
                <Button variant="outline" onClick={closeCreateModal}>
                  Cancel
                </Button>
                <Button type="submit" loading={createMutation.isPending}>
                  Create Admin
                </Button>
              </Group>
            </Stack>
          </form>
        </Modal>

        {/* Edit Admin Modal */}
        <Modal
          opened={editModalOpened}
          onClose={closeEditModal}
          title="Edit Admin"
          size="md"
        >
          <form onSubmit={editForm.onSubmit(handleEditAdmin)}>
            <Stack gap="md">
              <TextInput
                withAsterisk
                label="Full Name"
                placeholder="Enter admin's full name"
                {...editForm.getInputProps('name')}
              />
              <TextInput
                withAsterisk
                label="Email Address"
                placeholder="Enter admin's email"
                {...editForm.getInputProps('email')}
              />
              <Select
                withAsterisk
                label="Role"
                placeholder="Select admin role"
                data={ROLE_OPTIONS}
                {...editForm.getInputProps('role')}
              />
              <Group justify="flex-end" mt="md">
                <Button variant="outline" onClick={closeEditModal}>
                  Cancel
                </Button>
                <Button type="submit" loading={updateMutation.isPending}>
                  Update Admin
                </Button>
              </Group>
            </Stack>
          </form>
        </Modal>

        {/* Admin Detail Modal */}
        <Modal
          opened={detailModalOpened}
          onClose={closeDetailModal}
          title="Admin Details"
          size="md"
        >
          {selectedAdmin && (
            <Stack gap="md">
              <Group>
                <Text fw={500}>Name:</Text>
                <Text>{selectedAdmin.name}</Text>
              </Group>
              <Group>
                <Text fw={500}>Email:</Text>
                <Text>{selectedAdmin.email}</Text>
              </Group>
              <Group>
                <Text fw={500}>Role:</Text>
                <Badge color={getRoleColor(selectedAdmin.role)}>
                  {selectedAdmin.role?.replace('_', ' ') || 'Admin'}
                </Badge>
              </Group>
              <Group>
                <Text fw={500}>Status:</Text>
                <Badge color={getStatusColor(selectedAdmin.status)}>
                  {selectedAdmin.status}
                </Badge>
              </Group>
              <Group>
                <Text fw={500}>Created:</Text>
                <Text>{formatDate(selectedAdmin.created_at)}</Text>
              </Group>
              <Group>
                <Text fw={500}>Last Updated:</Text>
                <Text>{formatDate(selectedAdmin.updated_at)}</Text>
              </Group>
              {selectedAdmin.last_login && (
                <Group>
                  <Text fw={500}>Last Login:</Text>
                  <Text>{formatDate(selectedAdmin.last_login)}</Text>
                </Group>
              )}
            </Stack>
          )}
        </Modal>
      </Container>
    </AdminRouteGuard>
  );
}
