/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Group,
  Button,
  Stack,
  Select,
  Grid,
  Card,
  Text,
  Badge,
  Alert,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconDownload,
  IconRefresh,
  IconUsers,
  IconUserCheck,
  IconUserX,
  IconClock,
  IconAlertCircle,
} from '@tabler/icons-react';
import { DatePickerInput } from '@mantine/dates';
import { useQuery } from '@tanstack/react-query';
import { getUserReportsQuery } from '../../src/requests/reports';
import { DataTable } from '../../src/components/common/DataTable';
import { useLoading } from '../../src/contexts/LoadingContext';
import { UserType } from '../../src/types/admin.types';

export default function UserReportsPage() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();

  // Filter states
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    new Date(), // today
  ]);
  const [userType, setUserType] = useState<UserType | ''>('');
  const [format, setFormat] = useState<'json' | 'csv' | 'excel'>('json');

  // Build query parameters
  const queryParams = {
    format,
    user_type: userType || undefined,
    date_from: dateRange[0]?.toISOString(),
    date_to: dateRange[1]?.toISOString(),
  };

  // Fetch user reports data
  const {
    data: reportsData,
    isLoading,
    error,
    refetch,
  } = useQuery(getUserReportsQuery(queryParams));

  const users = reportsData?.data?.recent_registrations || [];

  // Calculate statistics from API data
  const stats = {
    total: reportsData?.data?.summary?.total_users || 0,
    active: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === 'ACTIVE')?.count || 0,
    inactive: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === 'INACTIVE')?.count || 0,
    pending: reportsData?.data?.summary?.by_status?.find((s: any) => s.status === 'PENDING')?.count || 0,
  };

  // Set loading state
  useEffect(() => {
    if (isLoading) {
      showLoading('Loading user reports...');
    } else if (!isLoading || error) {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading, error]);

  // Handle export
  const handleExport = async (exportFormat: 'csv' | 'excel') => {
    try {
      const response = await fetch('/api/export/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          format: exportFormat,
          filters: {
            ...(userType && { user_type: userType }),
            ...(dateRange[0] && { date_from: dateRange[0].toISOString() }),
            ...(dateRange[1] && { date_to: dateRange[1].toISOString() }),
          },
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const filename = `users_export_${new Date().toISOString().split('T')[0]}.${exportFormat}`;

        // Download file
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Show success notification (you can customize this based on your notification system)
      } else {
        throw new Error('Export failed');
      }
    } catch {
      // Show error notification (you can customize this based on your notification system)
    }
  };

  // Table columns
  const columns = [
    {
      accessor: 'name',
      label: 'Name',
      sortable: true,
    },
    {
      accessor: 'email',
      label: 'Email',
      sortable: true,
    },
    {
      accessor: 'user_type',
      label: 'Type',
      sortable: true,
      render: (user: any) => (
        <Badge color={user.user_type === 'CUSTOMER' ? 'blue' : 'green'} variant="light">
          {user.user_type.replace('_', ' ')}
        </Badge>
      ),
    },
    {
      accessor: 'status',
      label: 'Status',
      sortable: true,
      render: (user: any) => (
        <Badge color={user.status === 'ACTIVE' ? 'green' : 'red'} variant="light">
          {user.status}
        </Badge>
      ),
    },
    {
      accessor: 'approval_status',
      label: 'Approval',
      sortable: true,
      render: (user: any) => (
        <Badge
          color={
            // eslint-disable-next-line no-nested-ternary
            user.approval_status === 'APPROVED' ? 'green'
              : user.approval_status === 'REJECTED' ? 'red' : 'orange'
          }
          variant="light"
        >
          {user.approval_status}
        </Badge>
      ),
    },
    {
      accessor: 'created_at',
      label: 'Created',
      sortable: true,
      render: (user: any) => new Date(user.created_at).toLocaleDateString(),
    },
  ];

  return (
    <div>
      <Head>
        <title>User Reports | NAQALAT Admin</title>
      </Head>

      <Container size="xl" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <Group justify="space-between">
            <Group>
              <ActionIcon
                variant="subtle"
                size="lg"
                onClick={() => router.push('/reports')}
              >
                <IconArrowLeft size="1.2rem" />
              </ActionIcon>
              <div>
                <Title order={1}>User Reports</Title>
                <Text c="dimmed" size="lg">
                  Comprehensive user analytics and statistics
                </Text>
              </div>
            </Group>
            <Group>
              <Tooltip label="Refresh data">
                <ActionIcon variant="light" onClick={() => refetch()}>
                  <IconRefresh size="1rem" />
                </ActionIcon>
              </Tooltip>
              <Button
                leftSection={<IconDownload size="1rem" />}
                onClick={() => handleExport('excel')}
              >
                Export Excel
              </Button>
              <Button
                variant="outline"
                leftSection={<IconDownload size="1rem" />}
                onClick={() => handleExport('csv')}
              >
                Export CSV
              </Button>
            </Group>
          </Group>

          {/* Statistics Cards */}
          <Grid>
            <Grid.Col span={{ base: 6, sm: 3 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Total Users
                    </Text>
                    <Text fw={700} size="xl">
                      {stats.total}
                    </Text>
                  </div>
                  <IconUsers size="2rem" color="var(--mantine-color-blue-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 3 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Active
                    </Text>
                    <Text fw={700} size="xl" c="green">
                      {stats.active}
                    </Text>
                  </div>
                  <IconUserCheck size="2rem" color="var(--mantine-color-green-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 3 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Inactive
                    </Text>
                    <Text fw={700} size="xl" c="red">
                      {stats.inactive}
                    </Text>
                  </div>
                  <IconUserX size="2rem" color="var(--mantine-color-red-6)" />
                </Group>
              </Card>
            </Grid.Col>
            <Grid.Col span={{ base: 6, sm: 3 }}>
              <Card withBorder padding="lg">
                <Group justify="space-between">
                  <div>
                    <Text c="dimmed" size="sm" fw={500}>
                      Pending
                    </Text>
                    <Text fw={700} size="xl" c="orange">
                      {stats.pending}
                    </Text>
                  </div>
                  <IconClock size="2rem" color="var(--mantine-color-orange-6)" />
                </Group>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Filters */}
          <Paper withBorder p="md">
            <Group gap="md" align="end">
              <DatePickerInput
                type="range"
                label="Date Range"
                placeholder="Select date range"
                value={dateRange}
                onChange={(value) => setDateRange(value as [Date | null, Date | null])}
                style={{ flex: 1 }}
              />
              <Select
                label="User Type"
                placeholder="All types"
                data={[
                  { value: '', label: 'All Types' },
                  { value: UserType.CUSTOMER, label: 'Customer' },
                  { value: UserType.ACCESS_OPERATOR, label: 'Access Operator' },
                  { value: UserType.CAR_OPERATOR, label: 'Car Operator' },
                ]}
                value={userType}
                onChange={(value) => setUserType((value as UserType) || '')}
                clearable
                w={200}
              />
              <Select
                label="Format"
                data={[
                  { value: 'json', label: 'JSON' },
                  { value: 'csv', label: 'CSV' },
                  { value: 'excel', label: 'Excel' },
                ]}
                value={format}
                onChange={(value) => setFormat(value as 'json' | 'csv' | 'excel')}
                w={120}
              />
            </Group>
          </Paper>

          {/* Error Alert */}
          {error && (
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title="Error loading user reports"
              color="red"
              variant="light"
            >
              {typeof error === 'string' ? error : 'Failed to load user reports. Please try again.'}
            </Alert>
          )}

          {/* Data Table */}
          <Paper withBorder>
            <DataTable
              columns={columns}
              data={users}
              loading={isLoading}
              error={typeof error === 'string' ? error : null}
              emptyMessage="No user data found for the selected criteria"
            />
          </Paper>
        </Stack>
      </Container>
    </div>
  );
}
