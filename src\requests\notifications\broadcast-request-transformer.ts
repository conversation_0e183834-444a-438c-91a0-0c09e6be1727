import z from 'zod';
import {
  SendBroadcastRequest,
  ScheduleBroadcastRequest,
  PreviewBroadcastRequest,
  GetRecipientOptionsRequest,
} from '../../types/notification-api.types';
import { NotificationPriority } from '../../types/notification.types';

// Validation schemas for broadcast requests
const recipientSelectionSchema = z.object({
  type: z.enum(['all', 'role', 'individual']),
  roles: z.array(z.string()).optional(),
  userIds: z.array(z.string()).optional(),
});

const sendBroadcastRequestSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  content: z.string().min(1, 'Content is required').max(5000, 'Content must be less than 5000 characters'),
  priority: z.nativeEnum(NotificationPriority),
  recipients: recipientSelectionSchema,
  scheduleTime: z.string().optional(),
  templateId: z.string().optional(),
  variables: z.record(z.string(), z.string()).optional(),
});

const scheduleBroadcastRequestSchema = sendBroadcastRequestSchema.extend({
  scheduleTime: z.string().min(1, 'Schedule time is required for scheduled broadcasts'),
});

const previewBroadcastRequestSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  recipients: recipientSelectionSchema,
  templateId: z.string().optional(),
  variables: z.record(z.string(), z.string()).optional(),
});

const getRecipientOptionsRequestSchema = z.object({
  search: z.string().optional(),
  type: z.enum(['user', 'role']).optional(),
});

/**
 * Transform and validate send broadcast request data
 */
export const transformSendBroadcastRequest = (data: SendBroadcastRequest) => {
  const validated = sendBroadcastRequestSchema.parse(data);

  // Transform for API format
  return {
    title: validated.title.trim(),
    content: validated.content.trim(),
    priority: validated.priority,
    recipients: {
      type: validated.recipients.type,
      ...(validated.recipients.roles && { roles: validated.recipients.roles }),
      ...(validated.recipients.userIds && { userIds: validated.recipients.userIds }),
    },
    ...(validated.scheduleTime && { scheduleTime: validated.scheduleTime }),
    ...(validated.templateId && { templateId: validated.templateId }),
    ...(validated.variables && { variables: validated.variables }),
  };
};

/**
 * Transform and validate schedule broadcast request data
 */
export const transformScheduleBroadcastRequest = (data: ScheduleBroadcastRequest) => {
  const validated = scheduleBroadcastRequestSchema.parse(data);

  // Validate schedule time is in the future
  const scheduleDate = new Date(validated.scheduleTime);
  const now = new Date();

  if (scheduleDate <= now) {
    throw new Error('Schedule time must be in the future');
  }

  return {
    title: validated.title.trim(),
    content: validated.content.trim(),
    priority: validated.priority,
    recipients: {
      type: validated.recipients.type,
      ...(validated.recipients.roles && { roles: validated.recipients.roles }),
      ...(validated.recipients.userIds && { userIds: validated.recipients.userIds }),
    },
    scheduleTime: validated.scheduleTime,
    ...(validated.templateId && { templateId: validated.templateId }),
    ...(validated.variables && { variables: validated.variables }),
  };
};

/**
 * Transform and validate preview broadcast request data
 */
export const transformPreviewBroadcastRequest = (data: PreviewBroadcastRequest) => {
  const validated = previewBroadcastRequestSchema.parse(data);

  return {
    title: validated.title.trim(),
    content: validated.content.trim(),
    recipients: {
      type: validated.recipients.type,
      ...(validated.recipients.roles && { roles: validated.recipients.roles }),
      ...(validated.recipients.userIds && { userIds: validated.recipients.userIds }),
    },
    ...(validated.templateId && { templateId: validated.templateId }),
    ...(validated.variables && { variables: validated.variables }),
  };
};

/**
 * Transform get recipient options request parameters
 */
export const transformGetRecipientOptionsParams = (params: GetRecipientOptionsRequest) => {
  const validated = getRecipientOptionsRequestSchema.parse(params);

  return {
    ...(validated.search && { search: validated.search.trim() }),
    ...(validated.type && { type: validated.type }),
  };
};

/**
 * Validate recipient selection data
 */
export const validateRecipientSelection = (recipients: SendBroadcastRequest['recipients']) => {
  const validated = recipientSelectionSchema.parse(recipients);

  // Additional validation logic
  if (validated.type === 'role' && (!validated.roles || validated.roles.length === 0)) {
    throw new Error('At least one role must be selected when recipient type is "role"');
  }

  if (validated.type === 'individual' && (!validated.userIds || validated.userIds.length === 0)) {
    throw new Error('At least one user must be selected when recipient type is "individual"');
  }

  return validated;
};

/**
 * Validate template variables
 */
export const validateTemplateVariables = (variables: Record<string, string>, requiredVariables: string[]) => {
  const missingVariables = requiredVariables.filter((variable) => !variables[variable]);

  if (missingVariables.length > 0) {
    throw new Error(`Missing required template variables: ${missingVariables.join(', ')}`);
  }

  return variables;
};

// Export schemas for external validation
export {
  recipientSelectionSchema,
  sendBroadcastRequestSchema,
  scheduleBroadcastRequestSchema,
  previewBroadcastRequestSchema,
  getRecipientOptionsRequestSchema,
};
