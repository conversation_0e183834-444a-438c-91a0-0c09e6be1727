/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
/* eslint-disable no-nested-ternary */
/* eslint-disable complexity */
/* eslint-disable camelcase */
import React from 'react';
import {
  Box,
  Stack,
  Group,
  Button,
  Select,
  TextInput,
  Pagination,
  Text,
  Center,
  Loader,
  Paper,
  Badge,
  ActionIcon,
  Divider,
} from '@mantine/core';
import {
  IconSearch,
  IconCheck,
  IconBellRinging,
  IconRefresh,
} from '@tabler/icons-react';
import NotificationItem from './NotificationItem';
import { NotificationPriority, NotificationType } from '../../types';

interface NotificationListProps {
  notifications: any[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
  unreadCount: number;
  isLoading: boolean;
  isFetching: boolean;

  filters: {
    searchTerm: string;
    readFilter: string | null;
    typeFilter: string | null;
    priorityFilter: string | null;
  };

  onFilterChange: (filters: any) => void;
  onPageChange: (page: number) => void;
  onRefresh: () => void;
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;

  showFilters?: boolean;
  showPagination?: boolean;
}

export default function NotificationList({
  notifications: notificationsList,
  pagination,
  unreadCount,
  isLoading,
  isFetching,
  filters,
  onFilterChange,
  onPageChange,
  onRefresh,
  onMarkAsRead,
  onMarkAllAsRead,
  showFilters = true,
  showPagination = true,
}: NotificationListProps) {
  const { total, page: currentPage, limit: pageSize } = pagination;
  const totalPages = Math.ceil(total / pageSize);

  const handleClearFilters = () => {
    onFilterChange({
      searchTerm: '',
      readFilter: null,
      typeFilter: null,
      priorityFilter: null,
    });
    onPageChange(0);
  };

  const hasActiveFilters = filters.searchTerm || filters.readFilter || filters.typeFilter || filters.priorityFilter;

  return (
    <Stack gap="md">
      {/* Header */}
      <Group justify="space-between" align="center">
        <Group gap="sm">
          <Text size="lg" fw={600}>
            Notifications
          </Text>
          {unreadCount > 0 && (
            <Badge color="blue" variant="light">
              {unreadCount}
              {' '}
              unread
            </Badge>
          )}
        </Group>

        <Group gap="sm">
          <ActionIcon
            variant="subtle"
            onClick={onRefresh}
            loading={isFetching}
            title="Refresh notifications"
          >
            <IconRefresh size="1.1rem" />
          </ActionIcon>

          {unreadCount > 0 && (
            <Button
              variant="light"
              size="sm"
              leftSection={<IconCheck size="1rem" />}
              onClick={onMarkAllAsRead}
            >
              Mark All as Read
            </Button>
          )}
        </Group>
      </Group>

      {/* Filters */}
      {showFilters && (
        <Paper p="md" withBorder>
          <Stack gap="md">
            <Group gap="md" align="end">
              <TextInput
                placeholder="Search notifications"
                leftSection={<IconSearch size="1rem" />}
                value={filters.searchTerm}
                onChange={(e) => onFilterChange({ ...filters, searchTerm: e.target.value })}
                style={{ flex: 1 }}
              />

              <Select
                placeholder="Status"
                data={[
                  { value: 'false', label: 'Unread' },
                  { value: 'true', label: 'Read' },
                ]}
                value={filters.readFilter}
                onChange={(value) => onFilterChange({ ...filters, readFilter: value })}
                clearable
                w={120}
              />

              <Select
                placeholder="Priority"
                data={Object.values(NotificationPriority).map((priority) => ({
                  value: priority,
                  label: priority.charAt(0) + priority.slice(1).toLowerCase(),
                }))}
                value={filters.priorityFilter}
                onChange={(value) => onFilterChange({ ...filters, priorityFilter: value })}
                clearable
                w={120}
              />

              <Select
                placeholder="Type"
                data={Object.values(NotificationType).map((type) => ({
                  value: type,
                  label: type.charAt(0) + type.slice(1).toLowerCase(),
                }))}
                value={filters.typeFilter}
                onChange={(value) => onFilterChange({ ...filters, typeFilter: value })}
                clearable
                w={200}
              />
            </Group>

            {hasActiveFilters && (
              <Group justify="space-between">
                <Text size="sm" c="dimmed">
                  {total}
                  {' '}
                  {total === 1 ? 'notification' : 'notifications'}
                  {' '}
                  found
                </Text>
                <Button
                  variant="subtle"
                  size="xs"
                  onClick={handleClearFilters}
                >
                  Clear Filters
                </Button>
              </Group>
            )}
          </Stack>
        </Paper>
      )}

      {/* Content */}
      <Box>
        {isLoading ? (
          <Center py="xl">
            <Stack align="center" gap="sm">
              <Loader />
              <Text size="sm" c="dimmed">
                Loading notifications...
              </Text>
            </Stack>
          </Center>
        ) : notificationsList.length === 0 ? (
          <Center py="xl">
            <Stack align="center" gap="sm">
              <IconBellRinging size="3rem" color="var(--mantine-color-gray-5)" />
              <Text size="lg" c="dimmed" ta="center">
                {hasActiveFilters ? 'No notifications match your filters' : 'No notifications yet'}
              </Text>
              {hasActiveFilters && (
                <Button variant="light" onClick={handleClearFilters}>
                  Clear Filters
                </Button>
              )}
            </Stack>
          </Center>
        ) : (
          <Paper withBorder>
            <Stack gap={0}>
              {notificationsList.map((notification, index) => (
                <Box key={notification.id} style={{}}>
                  <NotificationItem
                    notification={notification}
                    onMarkAsRead={onMarkAsRead}
                  />
                  {index < notificationsList.length - 1 && (
                    <Divider />
                  )}
                </Box>
              ))}
            </Stack>
          </Paper>
        )}
      </Box>

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <Group justify="center">
          <Pagination
            total={totalPages}
            value={currentPage + 1}
            onChange={(page) => onPageChange(page - 1)}
            size="sm"
          />
        </Group>
      )}
    </Stack>
  );
}
