import { NextApiRequest, NextApiResponse } from 'next';
import { createApiError, createApiResponse, getJwt } from '../../../src/utils';
import { API_ENDPOINT, apiMethods, HTTP_CODE } from '../../../src/data';
import { BACKEND_API } from '../../../src/lib/axios';
import { lockUserRequestSchema, LockUserResponse } from '../../../src/requests/audit-security';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);

  if (req.method === apiMethods.POST) {
    try {
      // Validate request body
      const validatedBody = lockUserRequestSchema.parse(req.body);

      const { data } = await BACKEND_API(req).post(API_ENDPOINT.security.lockUser, validatedBody, {
        headers: { Authorization: token },
      });

      return createApiResponse(
        res,
        LockUserResponse,
        {
          success: data.success,
          message: data.message,
          data: data.data,
        },
      );
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: 'Method not allowed' });
  return res.status(HTTP_CODE.METHOD_NOT_ALLOWED).json(error);
}

export default handler;
