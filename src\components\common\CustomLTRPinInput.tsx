/* eslint-disable react/require-default-props */
import React, {
  useState, useRef, useEffect, KeyboardEvent, ChangeEvent,
} from 'react';
import { Group, Input } from '@mantine/core';

interface CustomLTRPinInputProps {
  length?: number;
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  error?: boolean;
  disabled?: boolean;
}

export default function CustomLTRPinInput({
  length = 6,
  value = '',
  onChange,
  placeholder = '○',
  error = false,
  disabled = false,
}: CustomLTRPinInputProps) {
  const [values, setValues] = useState<string[]>(
    Array.from({ length }, (_, i) => value[i] || ''),
  );
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Update internal values when external value changes
  useEffect(() => {
    const newValues = Array.from({ length }, (_, i) => value[i] || '');
    setValues(newValues);
  }, [value, length]);

  // Handle input change
  const handleChange = (index: number, inputValue: string) => {
    // Only allow single character
    const newValue = inputValue.slice(-1);

    const newValues = [...values];
    newValues[index] = newValue;
    setValues(newValues);

    // Call onChange with combined value
    if (onChange) {
      onChange(newValues.join(''));
    }

    // Move to next input if value is entered
    if (newValue && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle key down events
  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace') {
      if (!values[index] && index > 0) {
        // Move to previous input if current is empty
        inputRefs.current[index - 1]?.focus();
      }
    } else if (e.key === 'ArrowLeft' && index > 0) {
      e.preventDefault();
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      e.preventDefault();
      inputRefs.current[index + 1]?.focus();
    } else if (e.key === 'Delete') {
      const newValues = [...values];
      newValues[index] = '';
      setValues(newValues);
      if (onChange) {
        onChange(newValues.join(''));
      }
    }
  };

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, length);
    const newValues = Array.from({ length }, (_, i) => pastedData[i] || '');
    setValues(newValues);

    if (onChange) {
      onChange(newValues.join(''));
    }

    // Focus the next empty input or the last input
    const nextEmptyIndex = newValues.findIndex((val) => !val);
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : length - 1;
    inputRefs.current[focusIndex]?.focus();
  };

  return (
    <div
      dir="ltr"
      style={{
        direction: 'ltr',
        unicodeBidi: 'isolate',
      }}
      onPaste={handlePaste}
    >
      <Group
        gap="xs"
        justify="center"
        style={{
          direction: 'ltr',
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        {Array.from({ length }, (_, index) => (
          <div
            key={index}
            style={{
              direction: 'ltr',
              unicodeBidi: 'isolate',
            }}
          >
            <Input
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              value={values[index]}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              placeholder={placeholder}
              error={error}
              disabled={disabled}
              size="lg"
              dir="ltr"
              style={{
                width: '2.5rem',
                height: '2.5rem',
                textAlign: 'center',
                direction: 'ltr',
                unicodeBidi: 'isolate',
                fontFamily: 'monospace, sans-serif',
                fontSize: '1rem',
                fontWeight: 600,
              }}
              styles={{
                wrapper: {
                  direction: 'ltr',
                  unicodeBidi: 'isolate',
                },
                input: {
                  textAlign: 'center',
                  direction: 'ltr',
                  unicodeBidi: 'isolate',
                  fontFamily: 'monospace, sans-serif',
                  fontSize: '1rem',
                  fontWeight: 600,
                  padding: 0,
                  '&:dir(rtl)': {
                    direction: 'ltr',
                  },
                },
              }}
              maxLength={1}
              autoComplete="off"
              inputMode="numeric"
              pattern="[0-9]*"
            />
          </div>
        ))}
      </Group>
    </div>
  );
}
