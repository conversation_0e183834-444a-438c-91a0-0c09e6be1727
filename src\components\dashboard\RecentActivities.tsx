import {
  Paper, Title, Stack, Group, Text, Avatar, Badge, ScrollArea,
} from '@mantine/core';
import {
  IconUserPlus,
  IconTruck,
  IconPackage,
  IconUserCheck,
  IconAlertTriangle,
  IconSettings,
} from '@tabler/icons-react';
import { RecentActivity } from '../../types/admin.types';

interface RecentActivitiesProps {
  activities: RecentActivity[];
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'USER_REGISTERED':
      return <IconUserPlus size="1rem" />;
    case 'USER_APPROVED':
      return <IconUserCheck size="1rem" />;
    case 'SHIPMENT_CREATED':
      return <IconPackage size="1rem" />;
    case 'SHIPMENT_DELIVERED':
      return <IconTruck size="1rem" />;
    case 'SYSTEM_ALERT':
      return <IconAlertTriangle size="1rem" />;
    case 'SETTINGS_CHANGED':
      return <IconSettings size="1rem" />;
    default:
      return <IconSettings size="1rem" />;
  }
};

const getActivityColor = (type: string) => {
  switch (type) {
    case 'USER_REGISTERED':
      return 'blue';
    case 'USER_APPROVED':
      return 'green';
    case 'SHIPMENT_CREATED':
      return 'indigo';
    case 'SHIPMENT_DELIVERED':
      return 'teal';
    case 'SYSTEM_ALERT':
      return 'red';
    case 'SETTINGS_CHANGED':
      return 'orange';
    default:
      return 'gray';
  }
};

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'Just now';
  }
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }
  if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  }
  const days = Math.floor(diffInMinutes / 1440);
  return `${days}d ago`;
};

export function RecentActivities({ activities }: RecentActivitiesProps) {
  return (
    <Paper withBorder p="md" radius="md" shadow="sm" h={400}>
      <Title order={4} mb="md">
        Recent Activities
      </Title>

      <ScrollArea h={320}>
        <Stack gap="sm">
          {activities.length === 0 ? (
            <Text c="dimmed" ta="center" py="xl">
              No recent activities
            </Text>
          ) : (
            activities.map((activity) => (
              <Group key={activity.id} gap="sm" wrap="nowrap">
                <Avatar
                  size="sm"
                  radius="xl"
                  color={getActivityColor(activity.type)}
                  variant="light"
                >
                  {getActivityIcon(activity.type)}
                </Avatar>

                <Stack gap={2} style={{ flex: 1 }}>
                  <Group justify="space-between" wrap="nowrap">
                    <Text size="sm" fw={500} style={{ flex: 1 }}>
                      {activity.description}
                    </Text>
                    <Text size="xs" c="dimmed" style={{ whiteSpace: 'nowrap' }}>
                      {formatTimestamp(activity.timestamp)}
                    </Text>
                  </Group>

                  <Group gap="xs">
                    <Badge
                      size="xs"
                      variant="light"
                      color={getActivityColor(activity.type)}
                    >
                      {activity.type.replace('_', ' ').toLowerCase()}
                    </Badge>
                    {activity.user_name && (
                    <Text size="xs" c="dimmed">
                      by
                      {' '}
                      {activity.user_name}
                    </Text>
                    )}
                  </Group>
                </Stack>
              </Group>
            ))
          )}
        </Stack>
      </ScrollArea>
    </Paper>
  );
}
