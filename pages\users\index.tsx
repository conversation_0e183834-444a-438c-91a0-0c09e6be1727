import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Group,
  TextInput,
  Select,
  Button,
  Stack,
  Alert,
} from '@mantine/core';
import {
  IconSearch,
  IconRefresh,
  IconAlertCircle,
  IconFileExport,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { useLoading } from '../../src/contexts/LoadingContext';
import {
  UserType, UserStatus, ApprovalStatus, User,
} from '../../src/types/admin.types';
import { getUsersQuery } from '../../src/requests/admin-users';
import { DataTable } from '../../src/components/common/DataTable';
import { getUsersColumns } from '../../src/components/users/UsersTableColumns';
import { useSearch } from '../../src/hooks/useSearch';
import { sortKeysMapping } from '../../src/requests/admin-users/params';

const USER_TYPE_OPTIONS = [
  { value: '', label: 'All User Types' },
  { value: UserType.CUSTOMER, label: 'Customer' },
  { value: UserType.ACCESS_OPERATOR, label: 'Access Operator' },
  { value: UserType.CAR_OPERATOR, label: 'Car Operator' },
];

const STATUS_OPTIONS = [
  { value: '', label: 'All Statuses' },
  { value: UserStatus.ACTIVE, label: 'Active' },
  { value: UserStatus.PENDING, label: 'Pending' },
  { value: UserStatus.SUSPENDED, label: 'Suspended' },
];

const APPROVAL_STATUS_OPTIONS = [
  { value: '', label: 'All Approval Statuses' },
  { value: ApprovalStatus.APPROVED, label: 'Approved' },
  { value: ApprovalStatus.PENDING, label: 'Pending' },
  { value: ApprovalStatus.REJECTED, label: 'Rejected' },
];

export default function Users() {
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();

  // Get filters from URL query parameters
  const userTypeFilter = `${router.query.userType || ''}`;
  const statusFilter = `${router.query.status || ''}`;
  const approvalStatusFilter = `${router.query.approvalStatus || ''}`;

  // State for filters and pagination
  const [page, setPage] = useState(1);
  const { searchValue, searchInput, handleInputChange } = useSearch((newPage: number) => setPage(newPage));
  const [pageSize] = useState(20);
  const [userType, setUserType] = useState<string>(userTypeFilter);
  const [status, setStatus] = useState<string>(statusFilter);
  const [approvalStatus, setApprovalStatus] = useState<string>(approvalStatusFilter);
  const [sortStatus, setSortStatus] = useState<{ accessor: string; direction: 'asc' | 'desc' }>({
    accessor: 'created_at',
    direction: 'desc',
  });

  // Update filters from URL on component mount
  useEffect(() => {
    setUserType(userTypeFilter !== 'undefined' ? userTypeFilter : '');
    setStatus(statusFilter !== 'undefined' ? statusFilter : '');
    setApprovalStatus(approvalStatusFilter !== 'undefined' ? approvalStatusFilter : '');
  }, [router.query, userTypeFilter, statusFilter, approvalStatusFilter]);

  // Prepare filters for API call
  const filters = {
    search: searchValue,
    userType: userType || undefined,
    status: status || undefined,
    approvalStatus: approvalStatus || undefined,
  };

  // Convert sort status to API format
  const sortKey = `${
    sortKeysMapping.has(sortStatus.accessor)
      ? sortKeysMapping.get(sortStatus.accessor)
      : sortStatus.accessor
  }:${sortStatus.direction}`;

  // Data fetching
  const {
    data: usersData, isLoading, error, refetch,
  } = useQuery(
    getUsersQuery({
      filters,
      sort: sortKey,
      pagination: { page, pageSize },
    }),
  );

  // Handle filter changes
  const handleFilterChange = (filterType: string, value: string) => {
    setPage(1); // Reset to first page when filters change

    const newQuery = { ...router.query };
    if (value) {
      newQuery[filterType] = value;
    } else {
      delete newQuery[filterType];
    }

    router.push({
      pathname: router.pathname,
      query: newQuery,
    });

    // Update local state
    switch (filterType) {
      case 'userType':
        setUserType(value);
        break;
      case 'status':
        setStatus(value);
        break;
      case 'approvalStatus':
        setApprovalStatus(value);
        break;
      default:
        break;
    }
  };

  // Handle sort changes
  const handleSortChange = (sort: { accessor: string; direction: 'asc' | 'desc' }) => {
    setSortStatus(sort);
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle user view
  const handleViewUser = (user: User) => {
    router.push(`/users/${user.id}`);
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
  };

  // Handle export
  const handleExport = () => {
    try {
      const users = usersData?.data?.users || [];
      if (users.length === 0) {
        notifications.show({
          title: 'Export Aborted',
          message: 'There is no data to export.',
          color: 'yellow',
        });
        return;
      }

      const headers = ['ID', 'Name', 'Email', 'User Type', 'Status', 'Created At'];
      const csvContent = [
        headers.join(','),
        ...users.map((user: User) => [
          `"${user.id}"`,
          `"${user.name}"`,
          `"${user.email}"`,
          `"${user.user_type}"`,
          `"${user.status}"`,
          `"${user.created_at}"`,
        ].join(',')),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      if (link.href) {
        URL.revokeObjectURL(link.href);
      }
      const url = URL.createObjectURL(blob);
      link.href = url;
      link.setAttribute('download', 'users.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      notifications.show({
        title: 'Export Successful',
        message: 'The users data has been exported to users.csv.',
        color: 'green',
      });
    } catch (exportError) {
      notifications.show({
        title: 'Export Failed',
        message: 'An unexpected error occurred during the export. Please try again.',
        color: 'red',
      });
    }
  };

  // Calculate pagination
  const users = usersData?.data?.users || [];
  const totalUsers = usersData?.data?.pagination?.total || 0;
  const totalPages = Math.ceil(totalUsers / pageSize);

  // Set loading state
  useEffect(() => {
    if (isLoading) {
      showLoading('Loading users...');
    } else {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading]);

  return (
    <div>
      <Head>
        <title>Users | Admin</title>
      </Head>
      <Container size="xl" py="md">
        <Stack gap="md">
          <Group justify="space-between" align="center">
            <Title order={2}>Users Management</Title>
            <Group>
              <Button
                leftSection={<IconFileExport size="1rem" />}
                onClick={handleExport}
                variant="outline"
              >
                Export CSV
              </Button>
              <Button
                leftSection={<IconRefresh size="1rem" />}
                onClick={handleRefresh}
                loading={isLoading}
                variant="light"
              >
                Refresh
              </Button>
            </Group>
          </Group>

          {/* Filters */}
          <Paper p="md" withBorder>
            <Group gap="md" align="end">
              <TextInput
                placeholder="Search users..."
                leftSection={<IconSearch size="1rem" />}
                value={searchInput}
                onChange={(event) => handleInputChange(event.currentTarget.value)}
                style={{ flex: 1 }}
              />

              <Select
                placeholder="User Type"
                data={USER_TYPE_OPTIONS}
                value={userType}
                onChange={(value) => handleFilterChange('userType', value || '')}
                clearable
                w={200}
              />

              <Select
                placeholder="Status"
                data={STATUS_OPTIONS}
                value={status}
                onChange={(value) => handleFilterChange('status', value || '')}
                clearable
                w={150}
              />

              <Select
                placeholder="Approval Status"
                data={APPROVAL_STATUS_OPTIONS}
                value={approvalStatus}
                onChange={(value) => handleFilterChange('approvalStatus', value || '')}
                clearable
                w={180}
              />
            </Group>
          </Paper>

          {/* Error Alert */}
          {error && (
            <Alert
              icon={<IconAlertCircle size="1rem" />}
              title="Error loading users"
              color="red"
              variant="light"
            >
              {typeof error === 'string' ? error : 'Failed to load users. Please try again.'}
            </Alert>
          )}

          {/* Users Table */}
          <DataTable
            columns={getUsersColumns(handleViewUser)}
            data={users}
            loading={isLoading}
            error={typeof error === 'string' ? error : null}
            emptyMessage="No users found matching your criteria"
            pagination={{
              page,
              totalPages,
              onPageChange: handlePageChange,
            }}
            onSortChange={handleSortChange}
            sortState={sortStatus}
          />
        </Stack>
      </Container>
    </div>
  );
}
