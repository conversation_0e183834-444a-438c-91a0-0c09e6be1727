/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Paper,
  Grid,
  Card,
  Text,
  Button,
  Group,
  Stack,
  Badge,
  SimpleGrid,
  ThemeIcon,
} from '@mantine/core';
import {
  IconUsers,
  IconPackage,
  IconChartBar,
  IconDownload,
  IconCalendar,
  IconTrendingUp,
  IconFileText,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { getUserReportsQuery, getShipmentReportsQuery, getSystemReportsQuery } from '../../src/requests/reports';
import { UserStatus, ShipmentStatus } from '../../src/types/admin.types';

export default function ReportsPage() {
  const router = useRouter();
  const [dateRange] = useState<[Date | null, Date | null]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    new Date(), // today
  ]);

  // Fetch summary data for dashboard
  const { data: userReports } = useQuery(
    getUserReportsQuery({
      format: 'json',
      date_from: dateRange[0]?.toISOString(),
      date_to: dateRange[1]?.toISOString(),
    }),
  );

  const { data: shipmentReports } = useQuery(
    getShipmentReportsQuery({
      date_from: dateRange[0]?.toISOString(),
      date_to: dateRange[1]?.toISOString(),
    }),
  );

  const { data: systemReports } = useQuery(getSystemReportsQuery());

  const reportCards = [
    {
      title: 'User Reports',
      description: 'Comprehensive user analytics and statistics',
      icon: IconUsers,
      color: 'blue',
      route: '/reports/users',
      stats: userReports?.data?.summary ? `${userReports.data.summary.total_users} users` : 'Loading...',
    },
    {
      title: 'Shipment Reports',
      description: 'Detailed shipment tracking and analytics',
      icon: IconPackage,
      color: 'green',
      route: '/reports/shipments',
      stats: shipmentReports?.data?.summary ? `${shipmentReports.data.summary.total_shipments} shipments` : 'Loading...',
    },
    {
      title: 'System Reports',
      description: 'System performance and usage statistics',
      icon: IconChartBar,
      color: 'orange',
      route: '/reports/system',
      stats: systemReports?.data ? 'Available' : 'Loading...',
    },
  ];

  const quickActions = [
    {
      title: 'Export All Users',
      description: 'Download complete user database',
      icon: IconDownload,
      action: () => router.push('/export?type=users'),
    },
    {
      title: 'Export Shipments',
      description: 'Download shipment records',
      icon: IconDownload,
      action: () => router.push('/export?type=shipments'),
    },
    {
      title: 'Scheduled Reports',
      description: 'Manage automated reports',
      icon: IconCalendar,
      action: () => router.push('/reports/scheduled'),
    },
  ];

  return (
    <div>
      <Head>
        <title>Reports Dashboard | NAQALAT Admin</title>
      </Head>

      <Container size="xl" py="xl">
        <Stack gap="xl">
          {/* Header */}
          <Group justify="space-between">
            <div>
              <Title order={1}>Reports Dashboard</Title>
              <Text c="dimmed" size="lg">
                Generate and view comprehensive reports across your system
              </Text>
            </div>
            <Button
              leftSection={<IconFileText size="1rem" />}
              onClick={() => router.push('/export')}
            >
              Export Center
            </Button>
          </Group>

          {/* Report Cards */}
          <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
            {reportCards.map((card) => (
              <Card
                key={card.title}
                shadow="sm"
                padding="lg"
                radius="md"
                withBorder
                style={{ cursor: 'pointer' }}
                onClick={() => router.push(card.route)}
              >
                <Group justify="space-between" mb="xs">
                  <ThemeIcon color={card.color} size="lg" radius="md">
                    <card.icon size="1.2rem" />
                  </ThemeIcon>
                  <Badge color={card.color} variant="light">
                    {card.stats}
                  </Badge>
                </Group>

                <Text fw={500} size="lg" mb="xs">
                  {card.title}
                </Text>

                <Text size="sm" c="dimmed" mb="md">
                  {card.description}
                </Text>

                <Button
                  variant="light"
                  color={card.color}
                  fullWidth
                  radius="md"
                  rightSection={<IconTrendingUp size="1rem" />}
                >
                  View Report
                </Button>
              </Card>
            ))}
          </SimpleGrid>

          {/* Quick Actions */}
          <Paper withBorder p="lg" radius="md">
            <Title order={3} mb="md">
              Quick Actions
            </Title>
            <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="md">
              {quickActions.map((action) => (
                <Button
                  key={action.title}
                  variant="outline"
                  size="lg"
                  leftSection={<action.icon size="1.2rem" />}
                  onClick={action.action}
                  style={{
                    height: 'auto',
                    padding: '16px',
                    flexDirection: 'column',
                    gap: '8px',
                  }}
                >
                  <Text fw={500}>{action.title}</Text>
                  <Text size="xs" c="dimmed">
                    {action.description}
                  </Text>
                </Button>
              ))}
            </SimpleGrid>
          </Paper>

          {/* Recent Activity Summary */}
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper withBorder p="lg" radius="md">
                <Title order={4} mb="md">
                  Recent User Activity
                </Title>
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Text size="sm">New Registrations (30 days)</Text>
                    <Badge color="blue">
                      {userReports?.data?.recent_registrations?.filter((u: any) => new Date(u.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length || 0}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text size="sm">Total Users</Text>
                    <Badge color="green">
                      {userReports?.data?.summary?.total_users || 0}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text size="sm">Active Users</Text>
                    <Badge color="blue">
                      {userReports?.data?.summary?.by_status?.find((s: any) => s.status === UserStatus.ACTIVE)?.count || 0}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text size="sm">Pending Approvals</Text>
                    <Badge color="orange">
                      {userReports?.data?.summary?.by_status?.find((s: any) => s.status === UserStatus.PENDING)?.count || 0}
                    </Badge>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Paper withBorder p="lg" radius="md">
                <Title order={4} mb="md">
                  Shipment Summary
                </Title>
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Text size="sm">Total Shipments</Text>
                    <Badge color="indigo">
                      {shipmentReports?.data?.summary?.total_shipments || 0}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text size="sm">Delivered</Text>
                    <Badge color="green">
                      {shipmentReports?.data?.summary?.delivered_shipments || 0}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text size="sm">Delivery Rate</Text>
                    <Badge color="blue">
                      {shipmentReports?.data?.summary?.delivery_rate ? `${shipmentReports.data.summary.delivery_rate}%` : '0%'}
                    </Badge>
                  </Group>
                  <Group justify="space-between">
                    <Text size="sm">In Transit</Text>
                    <Badge color="yellow">
                      {shipmentReports?.data?.summary?.by_status?.find((s: any) => s.status === ShipmentStatus.IN_TRANSIT)?.count || 0}
                    </Badge>
                  </Group>
                </Stack>
              </Paper>
            </Grid.Col>
          </Grid>
        </Stack>
      </Container>
    </div>
  );
}
