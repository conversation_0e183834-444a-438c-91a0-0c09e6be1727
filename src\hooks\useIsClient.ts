import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if component is running on client side
 * Useful for preventing hydration mismatches
 */
export const useIsClient = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
};

/**
 * Custom hook that returns true only after component has mounted on client
 * Alternative name for the same functionality
 */
export const useHasMounted = () => {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  return hasMounted;
};

export default useIsClient;
