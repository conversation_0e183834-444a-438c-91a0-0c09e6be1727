export * from './error.type';
export * from './admin.types';
export * from './notification.types';
export * from './notification-api.types';

// Legacy pagination interfaces (keeping for compatibility)
export interface Pagination {
  page?: number;
  pageSize?: number;
  start?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationResponse {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}
