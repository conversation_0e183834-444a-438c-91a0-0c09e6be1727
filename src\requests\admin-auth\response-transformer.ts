import {
  adminLoginResponseSchema,
  adminRegisterResponseSchema,
  otpVerificationResponseSchema,
  forgotPasswordResponseSchema,
  resetPasswordResponseSchema,
  changePasswordResponseSchema,
  AdminLoginResponseType,
  AdminRegisterResponseType,
  OtpVerificationResponseType,
  ForgotPasswordResponseType,
  ResetPasswordResponseType,
  ChangePasswordResponseType,
} from './types';

// Response transformers - convert backend data to frontend format
export const transformAdminLoginResponse = (data: unknown): AdminLoginResponseType => {
  const validated = adminLoginResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        name: validated.data.admin.name,
        email: validated.data.admin.email,
        role: validated.data.admin.role,
        status: validated.data.admin.status,
        created_at: validated.data.admin.created_at,
        updated_at: validated.data.admin.updated_at,
        last_login: validated.data.admin.last_login,
      },
      token: validated.data.token,
    },
  };
};

export const transformAdminRegisterResponse = (data: unknown): AdminRegisterResponseType => {
  const validated = adminRegisterResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        name: validated.data.admin.name,
        email: validated.data.admin.email,
        role: validated.data.admin.role,
        status: validated.data.admin.status,
        created_at: validated.data.admin.created_at,
        updated_at: validated.data.admin.updated_at,
        last_login: validated.data.admin.last_login,
      },
    },
  };
};

export const transformOtpVerificationResponse = (data: unknown): OtpVerificationResponseType => {
  const validated = otpVerificationResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        status: validated.data.admin.status,
      },
      token: validated.data.token,
    },
  };
};

export const transformForgotPasswordResponse = (data: unknown): ForgotPasswordResponseType => {
  const validated = forgotPasswordResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
  };
};

export const transformResetPasswordResponse = (data: unknown): ResetPasswordResponseType => {
  const validated = resetPasswordResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
  };
};

export const transformChangePasswordResponse = (data: unknown): ChangePasswordResponseType => {
  const validated = changePasswordResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
  };
};
