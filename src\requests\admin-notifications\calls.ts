import { API_ENDPOINT, HTTP_CODE } from '../../data';
import { CLIENT_API } from '../../lib/axios';
import { handleApiError } from '../../utils/handle-backend-error';
import {
  GetAdminNotificationsQueryProps,
  GetUnreadCountQueryProps,
} from './types';

enum queryKeys {
  adminNotifications = 'adminNotifications',
  unreadCount = 'unreadCount',
  markAsRead = 'markAsRead',
  markAllAsRead = 'markAllAsRead',
  deleteNotification = 'deleteNotification',
}

// Get admin notifications request
const getAdminNotificationsRequest = (props: GetAdminNotificationsQueryProps) => {
  const { filters, sort, pagination } = props;

  return CLIENT_API.get(API_ENDPOINT.adminNotifications.my, {
    params: {
      search: filters?.search,
      type: filters?.type,
      priority: filters?.priority,
      read: filters?.read,
      fromDate: filters?.fromDate,
      toDate: filters?.toDate,
      shipmentId: filters?.shipmentId,
      sort,
      limit: pagination?.limit || pagination?.pageSize,
      page: pagination?.page,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e);
      throw e.response?.data;
    });
};

export const getAdminNotificationsQuery = (props: GetAdminNotificationsQueryProps) => ({
  queryKey: [queryKeys.adminNotifications, props?.filters, props?.pagination, props?.sort],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  queryFn: (params: any) => getAdminNotificationsRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Get unread count request
const getUnreadCountRequest = () => CLIENT_API.get(API_ENDPOINT.adminNotifications.unreadCount)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err.response.data;
  });

export const getUnreadCountQuery = (props: GetUnreadCountQueryProps = {}) => ({
  queryKey: [queryKeys.unreadCount],
  queryFn: () => getUnreadCountRequest(),
  refetchOnWindowFocus: false,
  retry: (failureCount: number, error: { code: HTTP_CODE }) => error?.code !== HTTP_CODE.UNAUTHORIZED,
});

// Mark notification as read request
const markAsReadRequest = ({ id }: { id: string }) => CLIENT_API.put(API_ENDPOINT.adminNotifications.markAsRead(id))
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const markAsReadMutation = () => ({
  mutationKey: [queryKeys.markAsRead],
  mutationFn: markAsReadRequest,
});

// Mark all notifications as read request
const markAllAsReadRequest = () => CLIENT_API.put(API_ENDPOINT.adminNotifications.markAllAsRead)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const markAllAsReadMutation = () => ({
  mutationKey: [queryKeys.markAllAsRead],
  mutationFn: markAllAsReadRequest,
});

// Delete notification request
const deleteNotificationRequest = ({ id }: { id: string }) => CLIENT_API.delete(API_ENDPOINT.adminNotifications.delete(id))
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const deleteNotificationMutation = () => ({
  mutationKey: [queryKeys.deleteNotification],
  mutationFn: deleteNotificationRequest,
});
