import {
  userReportsQuerySchema,
  shipmentReportsQuerySchema,
  exportUsersRequestSchema,
  exportShipmentsRequestSchema,
  UserReportsQueryType,
  ShipmentReportsQueryType,
  ExportUsersRequestType,
  ExportShipmentsRequestType,
} from './types';

// Request transformers - convert frontend data to backend format
export const transformUserReportsRequest = (data: UserReportsQueryType) => {
  const validated = userReportsQuerySchema.parse(data);
  return {
    format: validated.format,
    date_from: validated.date_from,
    date_to: validated.date_to,
    user_type: validated.user_type,
  };
};

export const transformShipmentReportsRequest = (data: ShipmentReportsQueryType) => {
  const validated = shipmentReportsQuerySchema.parse(data);
  return {
    status: validated.status,
    date_from: validated.date_from,
    date_to: validated.date_to,
  };
};

export const transformExportUsersRequest = (data: ExportUsersRequestType) => {
  const validated = exportUsersRequestSchema.parse(data);
  return {
    format: validated.format,
    filters: validated.filters || {},
  };
};

export const transformExportShipmentsRequest = (data: ExportShipmentsRequestType) => {
  const validated = exportShipmentsRequestSchema.parse(data);
  return {
    format: validated.format,
    filters: validated.filters || {},
  };
};
