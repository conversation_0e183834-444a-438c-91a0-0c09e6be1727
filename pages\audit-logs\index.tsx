import React, { useState } from 'react';
import Head from 'next/head';
import {
  Container,
  Title,
  Paper,
  Group,
  TextInput,
  Button,
  Alert,
  Table,
  Text,
  Badge,
  Pagination,
  Grid,
} from '@mantine/core';
import {
  IconSearch,
  IconRefresh,
  IconAlertCircle,
  IconFilter,
} from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { DatePickerInput } from '@mantine/dates';
import { useAdminAuthContext } from '../../src/contexts/AdminAuthContext';
import { useLoading } from '../../src/contexts/LoadingContext';
import { getAuditLogsQuery, AuditLog } from '../../src/requests/audit-security';

function AuditLogsPage() {
  const { isAuthenticated } = useAdminAuthContext();
  const { showLoading, hideLoading } = useLoading();

  // State for filters and pagination
  const [filters, setFilters] = useState({
    page: 0,
    limit: 20,
    action: '',
    user_id: '',
    admin_id: '',
    date_from: null as Date | null,
    date_to: null as Date | null,
  });

  // Fetch audit logs
  const {
    data: auditLogsData,
    isLoading,
    error,
    refetch,
  } = useQuery(getAuditLogsQuery(
    {
      pagination: { page: filters.page, limit: filters.limit },
      filters: {
        action: filters.action || undefined,
        user_id: filters.user_id || undefined,
        admin_id: filters.admin_id || undefined,
        date_from: filters.date_from ? new Date(filters.date_from).toISOString() : undefined,
        date_to: filters.date_to ? new Date(filters.date_to).toISOString() : undefined,
      },
    },
    { isAuth: isAuthenticated },
  ));

  React.useEffect(() => {
    if (isLoading) {
      showLoading('Loading audit logs...');
    } else {
      hideLoading();
    }
  }, [isLoading, showLoading, hideLoading]);

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page: page - 1 }));
  };

  const handleFilterChange = (field: string, value: string | Date | null) => {
    setFilters((prev) => ({ ...prev, [field]: value, page: 0 }));
  };

  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      action: '',
      user_id: '',
      admin_id: '',
      date_from: null,
      date_to: null,
    });
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleString();

  const formatDetails = (details: Record<string, unknown>) => JSON.stringify(details, null, 2);

  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading audit logs"
          color="red"
          variant="light"
        >
          <Text mb="md">
            Failed to load audit logs. Please try again.
          </Text>
          <Button variant="outline" onClick={() => refetch()}>
            Retry
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>Audit Logs - NAQALAT Admin</title>
      </Head>

      <Container size="xl" py="xl">
        <Group justify="space-between" mb="lg">
          <Title order={1}>Audit Logs</Title>
          <Group>
            <Button
              leftSection={<IconRefresh size="1rem" />}
              variant="outline"
              onClick={() => refetch()}
              loading={isLoading}
            >
              Refresh
            </Button>
          </Group>
        </Group>

        {/* Filters */}
        <Paper p="md" mb="lg">
          <Title order={3} mb="md">Filters</Title>
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Action"
                placeholder="Filter by action"
                value={filters.action}
                onChange={(e) => handleFilterChange('action', e.target.value)}
                leftSection={<IconSearch size="1rem" />}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="User ID"
                placeholder="Filter by user ID"
                value={filters.user_id}
                onChange={(e) => handleFilterChange('user_id', e.target.value)}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="Admin ID"
                placeholder="Filter by admin ID"
                value={filters.admin_id}
                onChange={(e) => handleFilterChange('admin_id', e.target.value)}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="From Date"
                placeholder="Select start date"
                value={filters.date_from}
                onChange={(date) => handleFilterChange('date_from', date)}
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <DatePickerInput
                label="To Date"
                placeholder="Select end date"
                value={filters.date_to}
                onChange={(date) => handleFilterChange('date_to', date)}
              />
            </Grid.Col>
          </Grid>
          <Group mt="md">
            <Button
              leftSection={<IconFilter size="1rem" />}
              onClick={clearFilters}
              variant="outline"
            >
              Clear Filters
            </Button>
          </Group>
        </Paper>

        {/* Audit Logs Table */}
        <Paper>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Action</Table.Th>
                <Table.Th>User ID</Table.Th>
                <Table.Th>Admin ID</Table.Th>
                <Table.Th>Details</Table.Th>
                <Table.Th>Created At</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {(auditLogsData?.data?.logs as AuditLog[] | undefined)?.map((log: AuditLog) => (
                <Table.Tr key={log.id}>
                  <Table.Td>
                    <Badge variant="light">{log.action}</Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm" c="dimmed">
                      {log.user_id || 'N/A'}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{log.admin_id}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="xs" style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {formatDetails(log.details)}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{formatDate(log.created_at)}</Text>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {auditLogsData?.data?.pagination && (
            <Group justify="center" mt="md" p="md">
              <Pagination
                total={auditLogsData.data.pagination.totalPages}
                value={filters.page + 1}
                onChange={handlePageChange}
              />
            </Group>
          )}
        </Paper>
      </Container>
    </>
  );
}

export default AuditLogsPage;
