/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable sonarjs/no-duplicate-string */
import { describe, it, expect } from '@jest/globals';
import {
  transformSendBroadcastRequest,
  transformScheduleBroadcastRequest,
  transformPreviewBroadcastRequest,
  transformGetRecipientOptionsParams,
  validateRecipientSelection,
  validateTemplateVariables,
} from '../broadcast-request-transformer';
import {
  SendBroadcastRequest,
  ScheduleBroadcastRequest,
  PreviewBroadcastRequest,
  GetRecipientOptionsRequest,
} from '../../../types/notification-api.types';
import { NotificationPriority } from '../../../types/notification.types';

describe('Broadcast Request Transformer', () => {
  describe('transformSendBroadcastRequest', () => {
    it('should transform valid send broadcast request', () => {
      const input: SendBroadcastRequest = {
        title: '  Test Broadcast  ',
        content: '  This is a test message  ',
        priority: NotificationPriority.HIGH,
        recipients: {
          type: 'all',
        },
      };

      const result = transformSendBroadcastRequest(input);

      expect(result).toEqual({
        title: 'Test Broadcast',
        content: 'This is a test message',
        priority: NotificationPriority.HIGH,
        recipients: {
          type: 'all',
        },
      });
    });

    it('should include optional fields when provided', () => {
      const input: SendBroadcastRequest = {
        title: 'Test Broadcast',
        content: 'This is a test message',
        priority: NotificationPriority.NORMAL,
        recipients: {
          type: 'role',
          roles: ['customer', 'operator'],
        },
        scheduleTime: '2024-12-31T10:00:00Z',
        templateId: 'template-123',
        variables: {
          userName: 'John Doe',
          companyName: 'NAQALAT',
        },
      };

      const result = transformSendBroadcastRequest(input);

      expect(result).toEqual({
        title: 'Test Broadcast',
        content: 'This is a test message',
        priority: NotificationPriority.NORMAL,
        recipients: {
          type: 'role',
          roles: ['customer', 'operator'],
        },
        scheduleTime: '2024-12-31T10:00:00Z',
        templateId: 'template-123',
        variables: {
          userName: 'John Doe',
          companyName: 'NAQALAT',
        },
      });
    });

    it('should throw validation error for invalid input', () => {
      const input = {
        title: '', // Empty title should fail validation
        content: 'Valid content',
        priority: NotificationPriority.NORMAL,
        recipients: {
          type: 'all',
        },
      } as SendBroadcastRequest;

      expect(() => transformSendBroadcastRequest(input)).toThrow();
    });

    it('should throw validation error for content too long', () => {
      const input: SendBroadcastRequest = {
        title: 'Valid title',
        content: 'a'.repeat(5001), // Exceeds 5000 character limit
        priority: NotificationPriority.NORMAL,
        recipients: {
          type: 'all',
        },
      };

      expect(() => transformSendBroadcastRequest(input)).toThrow();
    });
  });

  describe('transformScheduleBroadcastRequest', () => {
    it('should transform valid schedule broadcast request', () => {
      const futureDate = new Date();
      futureDate.setHours(futureDate.getHours() + 1);

      const input: ScheduleBroadcastRequest = {
        title: 'Scheduled Broadcast',
        content: 'This is a scheduled message',
        priority: NotificationPriority.URGENT,
        recipients: {
          type: 'individual',
          userIds: ['user-1', 'user-2'],
        },
        scheduleTime: futureDate.toISOString(),
      };

      const result = transformScheduleBroadcastRequest(input);

      expect(result).toEqual({
        title: 'Scheduled Broadcast',
        content: 'This is a scheduled message',
        priority: NotificationPriority.URGENT,
        recipients: {
          type: 'individual',
          userIds: ['user-1', 'user-2'],
        },
        scheduleTime: futureDate.toISOString(),
      });
    });

    it('should throw error for past schedule time', () => {
      const pastDate = new Date();
      pastDate.setHours(pastDate.getHours() - 1);

      const input: ScheduleBroadcastRequest = {
        title: 'Scheduled Broadcast',
        content: 'This is a scheduled message',
        priority: NotificationPriority.NORMAL,
        recipients: {
          type: 'all',
        },
        scheduleTime: pastDate.toISOString(),
      };

      expect(() => transformScheduleBroadcastRequest(input)).toThrow('Schedule time must be in the future');
    });

    it('should require scheduleTime for scheduled broadcasts', () => {
      const input = {
        title: 'Scheduled Broadcast',
        content: 'This is a scheduled message',
        priority: NotificationPriority.NORMAL,
        recipients: {
          type: 'all',
        },
        // Missing scheduleTime
      } as ScheduleBroadcastRequest;

      expect(() => transformScheduleBroadcastRequest(input)).toThrow();
    });
  });

  describe('transformPreviewBroadcastRequest', () => {
    it('should transform valid preview broadcast request', () => {
      const input: PreviewBroadcastRequest = {
        title: '  Preview Title  ',
        content: '  Preview content  ',
        recipients: {
          type: 'role',
          roles: ['customer'],
        },
        templateId: 'template-456',
        variables: {
          productName: 'NAQALAT Express',
        },
      };

      const result = transformPreviewBroadcastRequest(input);

      expect(result).toEqual({
        title: 'Preview Title',
        content: 'Preview content',
        recipients: {
          type: 'role',
          roles: ['customer'],
        },
        templateId: 'template-456',
        variables: {
          productName: 'NAQALAT Express',
        },
      });
    });

    it('should handle minimal preview request', () => {
      const input: PreviewBroadcastRequest = {
        title: 'Simple Preview',
        content: 'Simple content',
        recipients: {
          type: 'all',
        },
      };

      const result = transformPreviewBroadcastRequest(input);

      expect(result).toEqual({
        title: 'Simple Preview',
        content: 'Simple content',
        recipients: {
          type: 'all',
        },
      });
    });
  });

  describe('transformGetRecipientOptionsParams', () => {
    it('should transform recipient options parameters', () => {
      const input: GetRecipientOptionsRequest = {
        search: '  john doe  ',
        type: 'user',
      };

      const result = transformGetRecipientOptionsParams(input);

      expect(result).toEqual({
        search: 'john doe',
        type: 'user',
      });
    });

    it('should handle empty parameters', () => {
      const input: GetRecipientOptionsRequest = {};

      const result = transformGetRecipientOptionsParams(input);

      expect(result).toEqual({});
    });

    it('should omit undefined values', () => {
      const input: GetRecipientOptionsRequest = {
        search: undefined,
        type: 'role',
      };

      const result = transformGetRecipientOptionsParams(input);

      expect(result).toEqual({
        type: 'role',
      });
    });
  });

  describe('validateRecipientSelection', () => {
    it('should validate "all" recipient type', () => {
      const recipients = {
        type: 'all' as const,
      };

      const result = validateRecipientSelection(recipients);

      expect(result).toEqual(recipients);
    });

    it('should validate "role" recipient type with roles', () => {
      const recipients = {
        type: 'role' as const,
        roles: ['customer', 'operator'],
      };

      const result = validateRecipientSelection(recipients);

      expect(result).toEqual(recipients);
    });

    it('should throw error for "role" type without roles', () => {
      const recipients = {
        type: 'role' as const,
      };

      expect(() => validateRecipientSelection(recipients)).toThrow(
        'At least one role must be selected when recipient type is "role"',
      );
    });

    it('should validate "individual" recipient type with userIds', () => {
      const recipients = {
        type: 'individual' as const,
        userIds: ['user-1', 'user-2'],
      };

      const result = validateRecipientSelection(recipients);

      expect(result).toEqual(recipients);
    });

    it('should throw error for "individual" type without userIds', () => {
      const recipients = {
        type: 'individual' as const,
      };

      expect(() => validateRecipientSelection(recipients)).toThrow(
        'At least one user must be selected when recipient type is "individual"',
      );
    });
  });

  describe('validateTemplateVariables', () => {
    it('should validate when all required variables are provided', () => {
      const variables = {
        userName: 'John Doe',
        companyName: 'NAQALAT',
        productName: 'Express Delivery',
      };
      const requiredVariables = ['userName', 'companyName'];

      const result = validateTemplateVariables(variables, requiredVariables);

      expect(result).toEqual(variables);
    });

    it('should throw error when required variables are missing', () => {
      const variables = {
        userName: 'John Doe',
      };
      const requiredVariables = ['userName', 'companyName', 'productName'];

      expect(() => validateTemplateVariables(variables, requiredVariables)).toThrow(
        'Missing required template variables: companyName, productName',
      );
    });

    it('should handle empty required variables array', () => {
      const variables = {
        userName: 'John Doe',
      };
      const requiredVariables: string[] = [];

      const result = validateTemplateVariables(variables, requiredVariables);

      expect(result).toEqual(variables);
    });
  });
});
