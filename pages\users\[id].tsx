/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable max-lines */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Title,
  Group,
  Text,
  Badge,
  Button,
  Stack,
  Grid,
  Card,
  Alert,
  Modal,
  Textarea,
  Avatar,
  Paper,
  Box,
  ThemeIcon,
  SimpleGrid,
  Anchor,
} from '@mantine/core';
import {
  IconArrowLeft,
  IconUserCheck,
  IconUserX,
  IconAlertCircle,
  IconUser,
  IconMail,
  IconPhone,
  IconCalendar,
  IconEye,
  IconMapPin,
  IconTruck,
  IconBuilding,
  IconShield,
  IconActivity,
  IconExternalLink,
} from '@tabler/icons-react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useDisclosure } from '@mantine/hooks';
import { useSession } from 'next-auth/react';
import { notifications } from '@mantine/notifications';
import { useLoading } from '../../src/contexts/LoadingContext';
import { CopyButton } from '../../src/components/common/CopyButton';

import {
  UserStatus,
  ApprovalStatus,
} from '../../src/types/admin.types';
import { getUserQuery } from '../../src/requests';
import { updateUserActiveApprovalMutation } from '../../src/requests/admin-users/calls';

// Type for status changes - only ACTIVE and SUSPENDED are allowed for status changes
type UserStatusChange = 'ACTIVE' | 'SUSPENDED';

function UserDetailPageContent() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { status: sessionStatus } = useSession();
  const { showLoading, hideLoading } = useLoading();
  const { id } = router.query;
  const userId = Array.isArray(id) ? id[0] : id;

  const [statusModalOpened, { open: openStatusModal, close: closeStatusModal }] = useDisclosure(false);
  const [approvalModalOpened, { open: openApprovalModal, close: closeApprovalModal }] = useDisclosure(false);
  const [statusReason, setStatusReason] = useState('');
  const [approvalNotes, setApprovalNotes] = useState('');
  const [newStatus, setNewStatus] = useState<UserStatusChange | ''>('');
  const [newApprovalStatus, setNewApprovalStatus] = useState<ApprovalStatus | ''>('');

  // Handle authentication
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/login');
    }
  }, [sessionStatus, router]);

  const {
    data: userData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    getUserQuery(userId || '', { isAuth: sessionStatus === 'authenticated' }),
  );
  // Use global loading for this query
  useEffect(() => {
    if (isLoading && userId) {
      showLoading('Loading user details...');
    } else if (!isLoading || error) {
      hideLoading();
    }
  }, [isLoading, userId, showLoading, hideLoading, error]);

  // Force refetch when userId changes
  useEffect(() => {
    if (userId && router.isReady) {
      queryClient.removeQueries({ queryKey: ['admin-userData-detail'] });
      queryClient.invalidateQueries({ queryKey: ['admin-userData-detail', userId] });
      refetch();
    }
  }, [userId, router.isReady, queryClient, refetch]);

  const openStatusChangeModal = (status: UserStatusChange) => {
    setNewStatus(status);
    openStatusModal();
  };

  const openApprovalChangeModal = (approval: ApprovalStatus) => {
    setNewApprovalStatus(approval);
    openApprovalModal();
  };

  // Helper functions
  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE:
        return 'green';
      case UserStatus.SUSPENDED:
        return 'red';
      case UserStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  const getApprovalStatusColor = (status?: ApprovalStatus) => {
    switch (status) {
      case ApprovalStatus.APPROVED:
        return 'green';
      case ApprovalStatus.PENDING:
        return 'orange';
      default:
        return 'gray';
    }
  };

  // Helper function to get the actual approval status for the userData
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getUserApprovalStatus = (userInfo: any): ApprovalStatus => {
    if (userInfo.userType === 'CUSTOMER') {
      return ApprovalStatus.APPROVED;
    }

    if (userInfo.userType === 'ACCESS_OPERATOR') {
      return userInfo.accessOperator?.approved ? ApprovalStatus.APPROVED : ApprovalStatus.PENDING;
    }

    if (userInfo.userType === 'CAR_OPERATOR') {
      return userInfo.carOperator?.approved ? ApprovalStatus.APPROVED : ApprovalStatus.PENDING;
    }

    return ApprovalStatus.PENDING;
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });

  const userStatusMutation = useMutation({
    mutationFn: updateUserActiveApprovalMutation().mutationFn,
    onSuccess: (response) => {
      notifications.show({
        title: 'Success',
        message: response.message || 'User status updated successfully',
        color: 'green',
      });
      // Invalidate both the individual user query and the users list
      queryClient.invalidateQueries({ queryKey: ['userData', userId] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
      refetch();
      closeStatusModal();
      closeApprovalModal();
      setStatusReason('');
      setApprovalNotes('');
    },
    onError: (e) => {
      notifications.show({
        title: 'Error',
        message: e?.message || 'Failed to update user status',
        color: 'red',
      });
    },
  });

  // Handler for status changes
  const handleStatusChange = () => {
    if (newStatus && userId) {
      const active = newStatus === 'ACTIVE';
      userStatusMutation.mutate({
        id: userId as string,
        body: {
          active,
          reason: statusReason || undefined,
        },
      });
    }
  };

  // Handler for approval changes
  const handleApprovalChange = () => {
    if (newApprovalStatus && userId) {
      const approved = newApprovalStatus === ApprovalStatus.APPROVED;
      const currentUser = userData?.data?.userData || userData;
      userStatusMutation.mutate({
        id: userId as string,
        body: {
          active: currentUser?.status === 'ACTIVE',
          approved,
          reason: approvalNotes || undefined,
        },
      });
    }
  };

  // Don't render anything while loading
  if (isLoading) {
    return null;
  }

  if (error || !userData) {
    return (
      <Container size="xl" py="xl">
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          title="Error loading user data"
          color="red"
          variant="light"
          radius="md"
        >
          <Text mb="md">
            Failed to load user data. Please try again.
          </Text>
          <Group>
            <Button variant="outline" onClick={() => refetch()}>
              Retry
            </Button>
            <Button variant="outline" onClick={() => router.back()}>
              Go Back
            </Button>
          </Group>
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="xl" py={{ base: 'md', sm: 'xl' }} key={`${router.asPath}-${userId}`}>
      {/* Enhanced Header with Gradient Background */}
      <Paper
        withBorder
        p={{ base: 'md', sm: 'xl' }}
        mb="xl"
        radius="lg"
      >
        <Stack gap="lg">
          {/* Navigation and Title */}
          <Group justify="space-between" align="flex-start" wrap="wrap" gap="md">
            <Group gap="md" wrap="wrap">
              <Button
                variant="subtle"
                leftSection={<IconArrowLeft size="1rem" />}
                onClick={() => router.back()}
                radius="md"
                size="sm"
              >
                Back to Users
              </Button>
              <Group gap="md" wrap="wrap" justify="flex-end">
                {userData.status === UserStatus.ACTIVE ? (
                  <Button
                    color="red"
                    variant="light"
                    leftSection={<IconUserX size="1rem" />}
                    onClick={() => openStatusChangeModal('SUSPENDED')}
                    radius="md"
                    size="sm"
                  >
                    Deactivate User
                  </Button>
                ) : (
                  <Button
                    color="green"
                    variant="light"
                    leftSection={<IconUserCheck size="1rem" />}
                    onClick={() => openStatusChangeModal('ACTIVE')}
                    radius="md"
                    size="sm"
                  >
                    Activate User
                  </Button>
                )}

                {(userData.userType === 'ACCESS_OPERATOR' || userData.userType === 'CAR_OPERATOR') && (
                  getUserApprovalStatus(userData) === ApprovalStatus.APPROVED ? (
                    <Button
                      color="orange"
                      variant="light"
                      leftSection={<IconUserX size="1rem" />}
                      onClick={() => openApprovalChangeModal(ApprovalStatus.PENDING)}
                      radius="md"
                      size="sm"
                    >
                      Revoke Approval
                    </Button>
                  ) : (
                    <Button
                      color="green"
                      variant="light"
                      leftSection={<IconUserCheck size="1rem" />}
                      onClick={() => openApprovalChangeModal(ApprovalStatus.APPROVED)}
                      radius="md"
                      size="sm"
                    >
                      Approve User
                    </Button>
                  )
                )}
              </Group>
            </Group>
          </Group>

          {/* User Header Info */}
          <Group gap="lg" wrap="wrap">
            <Avatar size="xl" radius="lg" color="blue" variant="gradient" gradient={{ from: 'blue', to: 'indigo' }}>
              {userData.name?.charAt(0).toUpperCase() || 'U'}
            </Avatar>
            <Box flex={1} style={{ minWidth: 0 }}>
              <Title order={2} mb="xs" style={{ wordBreak: 'break-word' }}>
                {userData.name}
              </Title>
              <Group gap="lg" wrap="wrap">
                <Group wrap="nowrap" gap={10} mt={5}>
                  <IconMail stroke={1.5} size="1rem" />
                  <Text size="sm" c="dimmed">
                    {userData?.email}
                  </Text>
                  {userData?.email && <CopyButton value={userData.email} tooltipLabel="Copy email" />}
                </Group>
                {userData?.phone && (
                  <Group wrap="nowrap" gap={10} mt={5}>
                    <IconPhone stroke={1.5} size="1rem" />
                    <Text size="sm" c="dimmed">
                      {userData.phone}
                    </Text>
                    {userData?.phone && <CopyButton value={userData.phone} tooltipLabel="Copy phone number" />}
                  </Group>
                )}

              </Group>
            </Box>

          </Group>

          {/* Action Buttons */}

        </Stack>
      </Paper>

      <Grid gutter="xl">
        {/* Main Content */}
        <Grid.Col span={{ base: 12, lg: 8 }}>
          <Stack gap="xl">
            {/* Status Overview Cards */}
            <SimpleGrid cols={{ base: 1, xs: 2, sm: 3 }} spacing="md">
              <Card withBorder shadow="sm" p="lg" radius="lg">
                <Group gap="md">
                  <ThemeIcon size="xl" radius="lg" variant="light" color="blue">
                    <IconUser size="1.5rem" />
                  </ThemeIcon>
                  <Box flex={1}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700} mb={4}>
                      User Type
                    </Text>
                    <Badge variant="light" color="blue" size="lg" radius="md">
                      {userData.userType.replace('_', ' ')}
                    </Badge>
                  </Box>
                </Group>
              </Card>

              <Card withBorder shadow="sm" p="lg" radius="lg">
                <Group gap="md">
                  <ThemeIcon size="xl" radius="lg" variant="light" color={getStatusColor(userData.status as UserStatus)}>
                    <IconShield size="1.5rem" />
                  </ThemeIcon>
                  <Box flex={1}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700} mb={4}>
                      Account Status
                    </Text>
                    <Badge color={getStatusColor(userData.status as UserStatus)} size="lg" radius="md">
                      {userData.status}
                    </Badge>
                  </Box>
                </Group>
              </Card>

              <Card withBorder shadow="sm" p="lg" radius="lg">
                <Group gap="md">
                  <ThemeIcon size="xl" radius="lg" variant="light" color={getApprovalStatusColor(getUserApprovalStatus(userData))}>
                    <IconUserCheck size="1.5rem" />
                  </ThemeIcon>
                  <Box flex={1}>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700} mb={4}>
                      Approval Status
                    </Text>
                    <Badge
                      color={getApprovalStatusColor(getUserApprovalStatus(userData))}
                      size="lg"
                      radius="md"
                      variant="filled"
                    >
                      {getUserApprovalStatus(userData)}
                    </Badge>
                  </Box>
                </Group>
              </Card>
            </SimpleGrid>

            {/* Account Details */}
            <Card withBorder shadow="sm" p="xl" radius="lg">
              <Group mb="xl">
                <ThemeIcon size="lg" radius="md" variant="light" color="blue">
                  <IconEye size="1.2rem" />
                </ThemeIcon>
                <Title order={3}>Account Details</Title>
              </Group>

              <SimpleGrid cols={{ base: 1, xs: 2, md: 3 }} spacing="xl">
                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Email Verification</Text>
                  {(() => {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const { emailVerified } = (userData as any);
                    return (
                      <Badge
                        color={emailVerified ? 'green' : 'red'}
                        size="md"
                        variant="light"
                        radius="md"
                        fullWidth
                      >
                        {emailVerified ? 'Verified' : 'Not Verified'}
                      </Badge>
                    );
                  })()}
                </Box>

                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Total Shipments</Text>
                  <Text fw={600} size="xl" c="blue">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(userData as any).count?.shipments || 0}
                  </Text>
                </Box>

                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Audit Logs</Text>
                  <Text fw={600} size="xl" c="blue">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(userData as any).count?.auditLogs || 0}
                  </Text>
                </Box>

                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">Email Verifications</Text>
                  <Text fw={600} size="xl" c="blue">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(userData as any).count?.emailVerifications || 0}
                  </Text>
                </Box>

                <Box>
                  <Text size="sm" fw={600} c="dimmed" mb="xs">User Verifications</Text>
                  <Text fw={600} size="xl" c="blue">
                    {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                    {(userData as any).count?.userVerifications || 0}
                  </Text>
                </Box>
              </SimpleGrid>
            </Card>

            {/* Business Information - Enhanced */}
            {(userData.userType === 'ACCESS_OPERATOR' || userData.userType === 'CAR_OPERATOR') && (
              <Card withBorder shadow="sm" p="xl" radius="lg">
                <Group mb="xl">
                  <ThemeIcon size="lg" radius="md" variant="light" color="orange">
                    <IconBuilding size="1.2rem" />
                  </ThemeIcon>
                  <Title order={3}>Business Information</Title>
                </Group>

                <Stack gap="xl">
                  {userData.userType === 'ACCESS_OPERATOR' && userData.accessOperator && (
                    <>
                      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
                        <Box>
                          <Text size="sm" fw={600} c="dimmed" mb="xs">Business Name</Text>
                          <Text fw={500} size="lg">
                            {userData.accessOperator.businessName || userData.profile?.businessName || 'Not provided'}
                          </Text>
                        </Box>

                        <Box>
                          <Text size="sm" fw={600} c="dimmed" mb="xs">Business Address</Text>
                          <Text fw={500}>
                            {userData.accessOperator.address || userData.profile?.businessAddress || 'Not provided'}
                          </Text>
                        </Box>
                      </SimpleGrid>

                      {userData.accessOperator.geoLatitude && userData.accessOperator.geoLongitude && (
                        <Paper withBorder p="md" radius="md">
                          <Group justify="space-between" align="flex-start">
                            <Box>
                              <Group gap="xs" mb="xs">
                                <IconMapPin size="1rem" />
                                <Text fw={600} size="sm">Location Coordinates</Text>
                              </Group>
                              <Text size="sm" c="dimmed">
                                Lat:
                                {' '}
                                {userData.accessOperator.geoLatitude}
                                , Lng:
                                {' '}
                                {userData.accessOperator.geoLongitude}
                              </Text>
                            </Box>
                            <Anchor
                              href={`https://maps.google.com/?q=${userData.accessOperator.geoLatitude},${userData.accessOperator.geoLongitude}`}
                              target="_blank"
                              size="sm"
                            >
                              <Group gap="xs">
                                View on Maps
                                <IconExternalLink size="0.8rem" />
                              </Group>
                            </Anchor>
                          </Group>
                        </Paper>
                      )}
                    </>
                  )}

                  {userData.userType === 'CAR_OPERATOR' && userData.carOperator && (
                    <>
                      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
                        <Box>
                          <Text size="sm" fw={600} c="dimmed" mb="xs">License Number</Text>
                          <Text fw={500} size="lg">
                            {userData.carOperator.licenseNumber || 'Not provided'}
                          </Text>
                        </Box>

                        <Box>
                          <Text size="sm" fw={600} c="dimmed" mb="xs">Operator Status</Text>
                          <Badge
                            color={userData.carOperator.approved ? 'green' : 'orange'}
                            variant="filled"
                            size="lg"
                            radius="md"
                          >
                            {userData.carOperator.approved ? 'Approved' : 'Pending Approval'}
                          </Badge>
                        </Box>
                      </SimpleGrid>

                      {userData.carOperator.vehicleInfo && (
                        <Box>
                          <Text size="sm" fw={600} c="dimmed" mb="xs">Vehicle Information</Text>
                          <Paper withBorder p="md" radius="md" bg="var(--mantine-color-gray-0)">
                            <Text size="sm" style={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                              {JSON.stringify(userData.carOperator.vehicleInfo, null, 2)}
                            </Text>
                          </Paper>
                        </Box>
                      )}
                    </>
                  )}
                </Stack>
              </Card>
            )}
          </Stack>
        </Grid.Col>

        {/* Sidebar */}
        <Grid.Col span={{ base: 12, lg: 4 }}>
          <Stack gap="xl">
            {/* Access Points - Enhanced for Car Operators */}
            {userData.userType === 'CAR_OPERATOR' && userData.carOperator && (
              <Card withBorder shadow="sm" p="xl" radius="lg">
                <Group mb="xl">
                  <ThemeIcon size="lg" radius="md" variant="light" color="teal">
                    <IconTruck size="1.2rem" />
                  </ThemeIcon>
                  <Title order={3}>Access Points</Title>
                </Group>

                <Stack gap="lg">
                  {/* Pickup Access Point */}
                  <Box>
                    <Text size="sm" fw={600} c="green" mb="xs">
                      <Group gap="xs">
                        <IconMapPin size="1rem" />
                        Pickup Location
                      </Group>
                    </Text>
                    {userData.carOperator.pickupAccessPoint ? (
                      <Paper withBorder p="md" radius="md" bg="var(--mantine-color-green-0)">
                        <Text fw={500} mb="xs">
                          {userData.carOperator.pickupAccessPoint.business_name}
                        </Text>
                        <Text size="sm" c="dimmed" mb="xs">
                          {userData.carOperator.pickupAccessPoint.address}
                        </Text>
                        {userData.carOperator.pickupAccessPoint.geo_latitude && userData.carOperator.pickupAccessPoint.geo_longitude && (
                          <Anchor
                            href={`https://maps.google.com/?q=${userData.carOperator.pickupAccessPoint.geo_latitude},${userData.carOperator.pickupAccessPoint.geo_longitude}`}
                            target="_blank"
                            size="xs"
                          >
                            View on Maps
                          </Anchor>
                        )}
                      </Paper>
                    ) : (
                      <Text size="sm" c="dimmed" fs="italic">Not assigned</Text>
                    )}
                  </Box>

                  {/* Dropoff Access Point */}
                  <Box>
                    <Text size="sm" fw={600} c="blue" mb="xs">
                      <Group gap="xs">
                        <IconMapPin size="1rem" />
                        Dropoff Location
                      </Group>
                    </Text>
                    {userData.carOperator.dropoffAccessPoint ? (
                      <Paper withBorder p="md" radius="md" bg="var(--mantine-color-blue-0)">
                        <Text fw={500} mb="xs">
                          {userData.carOperator.dropoffAccessPoint.business_name}
                        </Text>
                        <Text size="sm" c="dimmed" mb="xs">
                          {userData.carOperator.dropoffAccessPoint.address}
                        </Text>
                        {userData.carOperator.dropoffAccessPoint.geo_latitude && userData.carOperator.dropoffAccessPoint.geo_longitude && (
                          <Anchor
                            href={`https://maps.google.com/?q=${userData.carOperator.dropoffAccessPoint.geo_latitude},${userData.carOperator.dropoffAccessPoint.geo_longitude}`}
                            target="_blank"
                            size="xs"
                          >
                            View on Maps
                          </Anchor>
                        )}
                      </Paper>
                    ) : (
                      <Text size="sm" c="dimmed" fs="italic">Not assigned</Text>
                    )}
                  </Box>
                </Stack>
              </Card>
            )}

            {/* Performance Statistics */}
            {(userData.userType === 'ACCESS_OPERATOR' || userData.userType === 'CAR_OPERATOR') && userData.statistics && (
              <Card withBorder shadow="sm" p="xl" radius="lg">
                <Group mb="xl">
                  <ThemeIcon size="lg" radius="md" variant="light" color="violet">
                    <IconActivity size="1.2rem" />
                  </ThemeIcon>
                  <Title order={3}>Performance</Title>
                </Group>

                <Stack gap="lg">
                  <Group justify="space-between">
                    <Text size="sm" fw={600} c="dimmed">Total Shipments</Text>
                    <Text fw={600} size="lg" c="violet">
                      {userData.statistics.total_shipments}
                    </Text>
                  </Group>

                  <Group justify="space-between">
                    <Text size="sm" fw={600} c="dimmed">Completed</Text>
                    <Text fw={600} size="lg" c="green">
                      {userData.statistics.completed_shipments}
                    </Text>
                  </Group>

                  <Box>
                    <Group justify="space-between" mb="xs">
                      <Text size="sm" fw={600} c="dimmed">Success Rate</Text>
                      <Badge
                        color={(() => {
                          const rate = userData.statistics.success_rate;
                          if (rate >= 90) return 'green';
                          if (rate >= 70) return 'yellow';
                          return 'red';
                        })()}
                        variant="filled"
                        size="lg"
                        radius="md"
                      >
                        {userData.statistics.success_rate.toFixed(1)}
                        %
                      </Badge>
                    </Group>
                  </Box>
                </Stack>
              </Card>
            )}

            {/* Account Timeline */}
            <Card withBorder shadow="sm" p="xl" radius="lg">
              <Group mb="xl">
                <ThemeIcon size="lg" radius="md" variant="light" color="indigo">
                  <IconCalendar size="1.2rem" />
                </ThemeIcon>
                <Title order={3}>Timeline</Title>
              </Group>

              <Stack gap="lg">
                <Paper withBorder p="md" radius="md">
                  <Text size="sm" fw={600} c="blue" mb="xs">Account Created</Text>
                  <Text fw={500}>{formatDate(userData.createdAt)}</Text>
                </Paper>

                {userData.updatedAt && (
                  <Paper withBorder p="md" radius="md">
                    <Text size="sm" fw={600} c="green" mb="xs">Last Updated</Text>
                    <Text fw={500}>{formatDate(userData.updatedAt)}</Text>
                  </Paper>
                )}

                {userData.lastLogin && (
                  <Paper withBorder p="md" radius="md" bg="var(--mantine-color-orange-0)">
                    <Text size="sm" fw={600} c="orange" mb="xs">Last Login</Text>
                    <Text fw={500}>{formatDate(userData.lastLogin)}</Text>
                  </Paper>
                )}
              </Stack>
            </Card>
          </Stack>
        </Grid.Col>
      </Grid>

      {/* Enhanced Status Change Modal */}
      <Modal
        opened={statusModalOpened}
        onClose={closeStatusModal}
        title={(
          <Group gap="md">
            <ThemeIcon
              size="lg"
              radius="md"
              variant="light"
              color={newStatus === 'ACTIVE' ? 'green' : 'red'}
            >
              {newStatus === 'ACTIVE' ? <IconUserCheck size="1.2rem" /> : <IconUserX size="1.2rem" />}
            </ThemeIcon>
            <Box>
              <Text fw={600}>Change User Status</Text>
              <Text size="sm" c="dimmed">
                Update user status to
                {' '}
                {newStatus}
              </Text>
            </Box>
          </Group>
        )}
        size="md"
        radius="lg"
      >
        <Stack gap="lg">
          <Alert
            icon={<IconAlertCircle size="1rem" />}
            color={newStatus === 'ACTIVE' ? 'green' : 'red'}
            variant="light"
            radius="md"
          >
            <Text>
              Are you sure you want to change this user&apos;s status to
              {' '}
              <Text component="span" fw={600} c={newStatus === 'ACTIVE' ? 'green' : 'red'}>
                {newStatus}
              </Text>
              ?
            </Text>
          </Alert>

          <Textarea
            label="Reason (optional)"
            placeholder="Enter reason for status change..."
            value={statusReason}
            onChange={(e) => setStatusReason(e.target.value)}
            rows={3}
            radius="md"
          />

          <Group justify="flex-end" gap="md">
            <Button variant="outline" onClick={closeStatusModal} radius="md">
              Cancel
            </Button>
            <Button
              color={newStatus === 'ACTIVE' ? 'green' : 'red'}
              onClick={handleStatusChange}
              loading={userStatusMutation.isPending}
              radius="md"
            >
              Confirm Change
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Enhanced Approval Change Modal */}
      <Modal
        opened={approvalModalOpened}
        onClose={closeApprovalModal}
        title={(
          <Group gap="md">
            <ThemeIcon
              size="lg"
              radius="md"
              variant="light"
              color={newApprovalStatus === ApprovalStatus.APPROVED ? 'green' : 'red'}
            >
              {newApprovalStatus === ApprovalStatus.APPROVED ? <IconUserCheck size="1.2rem" /> : <IconUserX size="1.2rem" />}
            </ThemeIcon>
            <Box>
              <Text fw={600}>
                {newApprovalStatus === ApprovalStatus.APPROVED ? 'Approve User' : 'Revoke Approval'}
              </Text>
              <Text size="sm" c="dimmed">
                {newApprovalStatus === ApprovalStatus.APPROVED ? 'Grant user approval' : 'Remove user approval'}
              </Text>
            </Box>
          </Group>
        )}
        size="md"
        radius="lg"
      >
        <Stack gap="lg">
          <Alert
            icon={<IconAlertCircle size="1rem" />}
            color={newApprovalStatus === ApprovalStatus.APPROVED ? 'green' : 'red'}
            variant="light"
            radius="md"
          >
            <Text>
              Are you sure you want to
              {' '}
              <Text
                component="span"
                fw={600}
                c={newApprovalStatus === ApprovalStatus.APPROVED ? 'green' : 'red'}
              >
                {newApprovalStatus === ApprovalStatus.APPROVED ? 'approve' : 'revoke approval for'}
              </Text>
              {' '}
              this user?
            </Text>
          </Alert>

          <Textarea
            label="Notes (optional)"
            placeholder={`Enter notes for this ${newApprovalStatus === ApprovalStatus.APPROVED ? 'approval' : 'revocation'}...`}
            value={approvalNotes}
            onChange={(e) => setApprovalNotes(e.target.value)}
            rows={3}
            radius="md"
          />

          <Group justify="flex-end" gap="md">
            <Button variant="outline" onClick={closeApprovalModal} radius="md">
              Cancel
            </Button>
            <Button
              color={newApprovalStatus === ApprovalStatus.APPROVED ? 'green' : 'red'}
              onClick={handleApprovalChange}
              loading={userStatusMutation.isPending}
              radius="md"
            >
              {newApprovalStatus === ApprovalStatus.APPROVED ? 'Confirm Approval' : 'Confirm Revocation'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}

// Wrapper component that forces re-mounting when userId changes
export default function UserDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const userId = Array.isArray(id) ? id[0] : id;

  // Don't render anything until router is ready and we have a userId
  if (!router.isReady || !userId) {
    return null;
  }

  // Use userId as key to force complete component remount when it changes
  return <UserDetailPageContent key={userId} />;
}
