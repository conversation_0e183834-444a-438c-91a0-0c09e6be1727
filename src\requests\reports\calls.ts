/* eslint-disable @typescript-eslint/no-explicit-any */
import { CLIENT_API } from '../../lib/axios';
import { API_ENDPOINT } from '../../data/api-endpoints';
import { handleApiError } from '../../utils/handle-api-error';
import {
  transformUserReportsRequest,
  transformShipmentReportsRequest,
  transformExportUsersRequest,
  transformExportShipmentsRequest,
} from './request-transformer';
import {
  transformUserReportsResponse,
  transformShipmentReportsResponse,
  transformSystemReportsResponse,
  transformExportUsersResponse,
  transformExportShipmentsResponse,
  transformExportDownloadResponse,
} from './response-transformer';
import {
  UserReportsQueryType,
  ShipmentReportsQueryType,
  ExportUsersRequestType,
  ExportShipmentsRequestType,
  UserReportsResponseType,
  ShipmentReportsResponseType,
  SystemReportsResponseType,
  ExportUsersResponseType,
  ExportShipmentsResponseType,
  ExportDownloadResponseType,
} from './types';

// API Request Functions
export const getUserReportsRequest = async (params: UserReportsQueryType): Promise<UserReportsResponseType> => {
  try {
    const transformedParams = transformUserReportsRequest(params);
    const response = await CLIENT_API.get(API_ENDPOINT.reports.users(), { params: transformedParams });
    return transformUserReportsResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const getShipmentReportsRequest = async (params: ShipmentReportsQueryType): Promise<ShipmentReportsResponseType> => {
  try {
    const transformedParams = transformShipmentReportsRequest(params);
    const response = await CLIENT_API.get(API_ENDPOINT.reports.shipments(), { params: transformedParams });
    return transformShipmentReportsResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const getSystemReportsRequest = async (): Promise<SystemReportsResponseType> => {
  try {
    const response = await CLIENT_API.get(API_ENDPOINT.reports.system());
    return transformSystemReportsResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const exportUsersRequest = async (params: ExportUsersRequestType): Promise<ExportUsersResponseType> => {
  try {
    const transformedParams = transformExportUsersRequest(params);
    const response = await CLIENT_API.post(API_ENDPOINT.export.users(), transformedParams);
    return transformExportUsersResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const exportShipmentsRequest = async (params: ExportShipmentsRequestType): Promise<ExportShipmentsResponseType> => {
  try {
    const transformedParams = transformExportShipmentsRequest(params);
    const response = await CLIENT_API.post(API_ENDPOINT.export.shipments(), transformedParams);
    return transformExportShipmentsResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

export const downloadExportRequest = async (exportId: string): Promise<ExportDownloadResponseType> => {
  try {
    const response = await CLIENT_API.get(API_ENDPOINT.export.download(exportId));
    return transformExportDownloadResponse(response.data);
  } catch (error) {
    throw handleApiError(error as any);
  }
};

// React Query configurations for backward compatibility
enum queryKeys {
  userReports = 'userReports',
  shipmentReports = 'shipmentReports',
  systemReports = 'systemReports',
}

export const getUserReportsQuery = (props: UserReportsQueryType) => ({
  queryKey: [queryKeys.userReports, props],
  queryFn: () => getUserReportsRequest(props),
  refetchOnWindowFocus: false,
  retry: (_failureCount: number, error: { code: any }) => error?.code !== 401,
});

export const getShipmentReportsQuery = (props: ShipmentReportsQueryType) => ({
  queryKey: [queryKeys.shipmentReports, props],
  queryFn: () => getShipmentReportsRequest(props),
  refetchOnWindowFocus: false,
  retry: (_failureCount: number, error: { code: any }) => error?.code !== 401,
});

export const getSystemReportsQuery = () => ({
  queryKey: [queryKeys.systemReports],
  queryFn: () => getSystemReportsRequest(),
  refetchOnWindowFocus: false,
  retry: (_failureCount: number, error: { code: any }) => error?.code !== 401,
});
