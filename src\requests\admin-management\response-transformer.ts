import {
  adminListResponseSchema,
  adminDetailResponseSchema,
  createAdminResponseSchema,
  updateAdminResponseSchema,
  adminStatusChangeResponseSchema,
  updateProfileResponseSchema,
  changePasswordResponseSchema,
  AdminListResponseType,
  AdminDetailResponseType,
  CreateAdminResponseType,
  UpdateAdminResponseType,
  AdminStatusChangeResponseType,
  UpdateProfileResponseType,
  ChangePasswordResponseType,
} from './types';

// Response transformers - convert backend data to frontend format
export const transformAdminListResponse = (data: unknown): AdminListResponseType => {
  const validated = adminListResponseSchema.parse(data);
  return {
    success: validated.success,
    data: {
      admins: validated.data.admins.map((admin) => ({
        id: admin.id,
        name: admin.name,
        email: admin.email,
        role: admin.role,
        status: admin.status,
        created_at: admin.created_at,
        updated_at: admin.updated_at,
        last_login: admin.last_login,
      })),
      total: validated.data.total,
      pagination: {
        page: validated.data.pagination.page,
        limit: validated.data.pagination.limit,
        total: validated.data.pagination.total,
        totalPages: validated.data.pagination.totalPages,
        hasNext: validated.data.pagination.hasNext,
        hasPrev: validated.data.pagination.hasPrev,
      },
    },
  };
};

export const transformAdminDetailResponse = (data: unknown): AdminDetailResponseType => {
  const validated = adminDetailResponseSchema.parse(data);
  return {
    success: validated.success,
    data: {
      admin: {
        id: validated.data.admin.id,
        name: validated.data.admin.name,
        email: validated.data.admin.email,
        role: validated.data.admin.role,
        status: validated.data.admin.status,
        created_at: validated.data.admin.created_at,
        updated_at: validated.data.admin.updated_at,
        last_login: validated.data.admin.last_login,
      },
    },
  };
};

export const transformCreateAdminResponse = (data: unknown): CreateAdminResponseType => {
  const validated = createAdminResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        name: validated.data.admin.name,
        email: validated.data.admin.email,
        role: validated.data.admin.role,
        status: validated.data.admin.status,
        created_at: validated.data.admin.created_at,
        updated_at: validated.data.admin.updated_at,
        last_login: validated.data.admin.last_login,
      },
    },
  };
};

export const transformUpdateAdminResponse = (data: unknown): UpdateAdminResponseType => {
  const validated = updateAdminResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        name: validated.data.admin.name,
        email: validated.data.admin.email,
        role: validated.data.admin.role,
        status: validated.data.admin.status,
        created_at: validated.data.admin.created_at,
        updated_at: validated.data.admin.updated_at,
        last_login: validated.data.admin.last_login,
      },
    },
  };
};

export const transformAdminStatusChangeResponse = (data: unknown): AdminStatusChangeResponseType => {
  const validated = adminStatusChangeResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        status: validated.data.admin.status,
      },
    },
  };
};

export const transformUpdateProfileResponse = (data: unknown): UpdateProfileResponseType => {
  const validated = updateProfileResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
    data: {
      admin: {
        id: validated.data.admin.id,
        name: validated.data.admin.name,
        email: validated.data.admin.email,
        role: validated.data.admin.role,
        status: validated.data.admin.status,
        created_at: validated.data.admin.created_at,
        updated_at: validated.data.admin.updated_at,
        last_login: validated.data.admin.last_login,
      },
    },
  };
};

export const transformChangePasswordResponse = (data: unknown): ChangePasswordResponseType => {
  const validated = changePasswordResponseSchema.parse(data);
  return {
    success: validated.success,
    message: validated.message,
  };
};
